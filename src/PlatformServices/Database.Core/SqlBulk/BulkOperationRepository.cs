using System.Collections.Generic;
using System.Threading.Tasks;
using Database.Core.DataReaders;
using Microsoft.Data.SqlClient;

namespace Database.Core.SqlBulk;

public abstract class BulkOperationRepository : IBulkOperationRepository
{
    public async Task BulkInsertAsync<T>(
        IEnumerable<T> data,
        string tableName,
        BulkColumnMapper columnMapper,
        SqlConnection connection,
        SqlTransaction transaction)
    {
        using var bulkCopy = new SqlBulkCopy(
            connection,
            SqlBulkCopyOptions.Default,
            transaction);

        await using var dr = data.AsDataReader();

        foreach (var item in columnMapper.Columns)
        {
            bulkCopy.ColumnMappings.Add(item);
        }

        bulkCopy.BulkCopyTimeout = 60;
        bulkCopy.BatchSize = 10000;
        bulkCopy.DestinationTableName = tableName;
        
        await bulkCopy.WriteToServerAsync(dr);
    }
}
