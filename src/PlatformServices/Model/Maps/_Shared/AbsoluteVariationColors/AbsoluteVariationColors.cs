using System;
using System.Text.Json.Serialization;

namespace Model.Maps._Shared.AbsoluteVariationColors
{
    public sealed record AbsoluteVariationColors
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("positive_absolute_variation_color")]
        public string PositiveAbsoluteVariationColor { get; set; }

        [JsonPropertyName("negative_absolute_variation_color")]
        public string NegativeAbsoluteVariationColor { get; set; }

        [JsonPropertyName("constant_absolute_variation_color")]
        public string ConstantAbsoluteVariationColor { get; set; }
    }
}
