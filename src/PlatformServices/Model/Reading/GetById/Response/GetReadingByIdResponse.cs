using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Model.Reading.GetById.Response
{
    public sealed record GetReadingByIdResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("instrument")]
        public Model._Shared.Instrument.Instrument Instrument { get; set; }

        [JsonPropertyName("instrument_top_quota")]
        public decimal? InstrumentTopQuota { get; set; }

        [JsonPropertyName("instrument_base_quota")]
        public decimal? InstrumentBaseQuota { get; set; }

        [JsonPropertyName("instrument_azimuth")]
        public double? InstrumentAzimuth { get; set; }

        [JsonPropertyName("instrument_measurement_frequency")]
        public TimeSpan? InstrumentMeasurementFrequency { get; set; }

        [JsonPropertyName("is_referential")]
        public bool? IsReferential { get; set; }

        [JsonPropertyName("values")]
        public List<ReadingValueResponse> Values { get; set; } = new();

        [JsonPropertyName("reference_reading")]
        public GetReadingByIdResponse ReferenceReading { get; set; }

        [JsonPropertyName("sections")]
        public List<Model._Shared.Section.Section> Sections { get; set; } = new ();
    }
}
