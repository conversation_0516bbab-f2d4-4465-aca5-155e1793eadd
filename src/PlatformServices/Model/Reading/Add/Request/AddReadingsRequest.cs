using System;
using System.Collections.Generic;
using System.Linq;
using Model.Reading._Shared;

namespace Model.Reading.Add.Request
{
    public record AddReadingsRequest : RequestWithMetadata
    {
        public List<ReadingRequest> Readings { get; set; }

        public HashSet<Guid> GetInstrumentIds() =>
            Readings
                .Select(reading => reading.Instrument?.Id ?? Guid.Empty)
                .Distinct()
                .ToHashSet();

        public IEnumerable<ReadingRequest> GetReferentialReadings() =>
            Readings?
                .Where(reading => reading.IsReferential.HasValue
                                  && reading.IsReferential.Value);

        public IEnumerable<ReadingRequest> GetCommonReadings() =>
            Readings?
                .Where(reading => !reading.IsReferential.HasValue
                                  || !reading.IsReferential.Value);

        public void SetDbInstruments(
            IEnumerable<Domain.Entities.Instrument> instruments)
        {
            if (instruments is null || !instruments.Any())
            {
                return;
            }

            foreach (var reading in Readings)
            {
                reading.DbInstrument = instruments.FirstOrDefault(instrument =>
                    instrument.Id == reading.Instrument.Id);
            }
        }

        public void SetReferentialReadings(
            IEnumerable<Domain.Entities.Reading> refReadingsFromDatabase,
            IEnumerable<Domain.Entities.Reading> refReadingsFromRequest)
        {
            if (refReadingsFromRequest is null ||
                !refReadingsFromRequest.Any() ||
                refReadingsFromDatabase is null ||
                !refReadingsFromDatabase.Any())
            {
                return;
            }

            foreach (var reading in Readings)
            {
                if (reading.DbInstrument is null)
                {
                    return;
                }

                var refReadingFromRequest = refReadingsFromRequest
                    .FirstOrDefault(item => item.Instrument.Id ==
                                            reading.DbInstrument.Id);

                var refReadingFromDatabase = refReadingsFromDatabase?
                    .FirstOrDefault(item => item.Instrument.Id ==
                                            reading.DbInstrument.Id);

                reading.ReferenceReading = refReadingFromRequest
                                           ?? refReadingFromDatabase;
            }
        }
    }
}
