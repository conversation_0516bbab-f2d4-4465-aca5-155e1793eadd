using FluentValidation;
using Model.Extensions;
using Model.Reading._Shared.ReadingValue;
using Model.Reading.Update.Request;
using System.Linq;
using Domain.Entities;

namespace Model.Reading.Update.Validators
{
    public class SurfaceLandmarkAndPrismRequestValidator : AbstractValidator<ReadingValueRequest>
    {
        public SurfaceLandmarkAndPrismRequestValidator(UpdateReadingRequest reading)
        {
            CascadeMode = CascadeMode.Stop;

            RuleFor(x => reading.IsReferential)
                .NotNull();

            RuleFor(x => reading.ReferenceReading)
                .NotNull();

            RuleFor(x => x.Datum)
                .NotNull()
                .IsInEnum();

            RuleFor(x => x.EastCoordinate)
                .NotNull();

            RuleFor(x => x.NorthCoordinate)
                .NotNull();

            RuleFor(x => x.Quota)
                .NotNull();

            RuleFor(x => x.EastDisplacement)
                .NotNull();

            RuleFor(x => x)
                .Must(x =>
                {
                    var expectedDisplacement = ReadingValue
                        .CalculateUtmCoordinateDisplacement(
                            reading.IsReferential.Value,
                            x.EastCoordinate.Value,
                            reading.ReferenceReading?.Values[0]?.EastCoordinate);

                    return x.EastDisplacement == expectedDisplacement ;
                })
                .WithMessage("The east displacement value is not valid.");

            RuleFor(x => x.NorthDisplacement)
                .NotNull();

            RuleFor(x => x)
                .Must(x =>
                {
                    var expectedDisplacement = ReadingValue
                        .CalculateUtmCoordinateDisplacement(
                            reading.IsReferential.Value,
                            x.NorthCoordinate.Value,
                            reading.ReferenceReading?.Values[0]?.NorthCoordinate);

                    return x.NorthDisplacement == expectedDisplacement;
                })
                .WithMessage("The north displacement value is not valid.");

            RuleFor(x => x.ZDisplacement)
                .NotNull();

            RuleFor(x => x)
                .Must(x =>
                {
                    var expectedDisplacement = ReadingValue
                        .CalculateZDisplacement(
                            reading.IsReferential.Value,
                            x.Quota.Value,
                            reading.ReferenceReading?.Values[0]?.Quota);

                    return x.ZDisplacement == expectedDisplacement;
                })
                .WithMessage("The Z displacement value is not valid.");

            RuleFor(x => x.TotalPlanimetricDisplacement)
                .NotNull();

            RuleFor(x => x)
                .Must(x =>
                {
                    var expectedDisplacement = ReadingValue
                        .CalculateTotalPlanimetricDisplacement(
                            x.NorthDisplacement.Value,
                            x.EastDisplacement.Value);
                    
                    return x.TotalPlanimetricDisplacement.Round(ReadingValue.DisplacementDecimalPlaces) == expectedDisplacement;
                })
                .WithMessage("The total planimetric displacement value is not valid.");

            RuleFor(x => x.ADisplacement)
                .NotNull()
                .When(x => reading.DbReading.InstrumentAzimuth.HasValue, ApplyConditionTo.CurrentValidator)
                .WithMessage("The A displacement value is required when the instrument has azimuth value.")
                .Null()
                .When(x => !reading.DbReading.InstrumentAzimuth.HasValue, ApplyConditionTo.CurrentValidator)
                .WithMessage("The A displacement value is not required when the instrument has no azimuth value.");

            RuleFor(x => x)
                .Must(x =>
                {
                    var expectedDisplacement = ReadingValue
                        .CalculateADisplacement(
                            x.NorthDisplacement.Value,
                            x.EastDisplacement.Value,
                            reading.DbInstrument.Azimuth.Value);

                    return x.ADisplacement.Round(ReadingValue.DisplacementDecimalPlaces) == expectedDisplacement;
                })
                .When(x => x.ADisplacement.HasValue)
                .WithMessage("The A displacement value is not valid.");

            RuleFor(x => x.BDisplacement)
                .NotNull()
                .When(x => reading.DbReading.InstrumentAzimuth.HasValue, ApplyConditionTo.CurrentValidator)
                .Null()
                .When(x => !reading.DbReading.InstrumentAzimuth.HasValue, ApplyConditionTo.CurrentValidator);

            RuleFor(x => x)
                .Must(x =>
                {
                    var expectedDisplacement = ReadingValue
                        .CalculateBDisplacement(
                            x.NorthDisplacement.Value,
                            x.EastDisplacement.Value,
                            reading.DbInstrument.Azimuth.Value);

                    return x.BDisplacement.Round(ReadingValue.DisplacementDecimalPlaces) == expectedDisplacement;
                })
                .When(x => x.BDisplacement.HasValue)
                .WithMessage("The B displacement value is not valid.");

            RuleFor(x => x.Measurement)
                .Null();

            RuleFor(x => x.Dry)
                .Null();

            RuleFor(x => x.Pressure)
                .Null();

            RuleFor(x => x.Depth)
                .Null();

            RuleFor(x => x.PositiveA)
                .Null();

            RuleFor(x => x.NegativeA)
                .Null();

            RuleFor(x => x.PositiveB)
                .Null();

            RuleFor(x => x.NegativeB)
                .Null();

            RuleFor(x => x.AverageDisplacementA)
                .Null();

            RuleFor(x => x.AverageDisplacementB)
                .Null();

            RuleFor(x => x.AccumulatedDisplacementA)
                .Null();

            RuleFor(x => x.AccumulatedDisplacementB)
                .Null();

            RuleFor(x => x.DeviationA)
                .Null();

            RuleFor(x => x.DeviationB)
                .Null();

            RuleFor(x => x.AAxisReading)
                .Null();

            RuleFor(x => x.BAxisReading)
                .Null();

            RuleFor(x => x.RelativeDepth)
                .Null();

            RuleFor(x => x.DeltaRef)
                .Null();

            RuleFor(x => x.AbsoluteSettlement)
                .Null();

            RuleFor(x => x.RelativeSettlement)
                .Null();

            RuleFor(x => x.Nature)
                .Null();

            RuleFor(x => x.AAxisPga)
                .Null();

            RuleFor(x => x.BAxisPga)
                .Null();

            RuleFor(x => x.ZAxisPga)
                .Null();

            RuleFor(x => x.Intensity)
                .Null();

            RuleFor(x => x.Pluviometry)
                .Null();
        }
    }
}
