using FluentValidation;
using Model.Client._Shared.FtpRootPath;

namespace Model.Client.UpsertClientAutomatedReadingsConfigurations.Request
{
    public class ClientAutomatedReadingConfigurationRequestValidator
        : AbstractValidator<ClientAutomatedReadingConfigurationRequest>
    {
        private readonly FtpRootPathValidator _validator = new();

        public ClientAutomatedReadingConfigurationRequestValidator()
        {
            RuleFor(x => x.ClientId)
                .NotEmpty();

            RuleFor(x => x.TemplateType)
                .IsInEnum();

            RuleFor(x => x.FtpRootPath)
                .NotEmpty();

            RuleFor(x => x.FtpRootPath)
                .SetValidator(_validator);
        }
    }
}
