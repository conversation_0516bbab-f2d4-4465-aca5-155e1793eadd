using System;
using System.Text.Json.Serialization;
using Domain.Enums;

namespace Model.Aspect.List.Response
{
    public sealed record ListAspectResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }
        
        [JsonPropertyName("allow_option_not_applicable")]
        public bool AllowOptionApplicable { get; set; }
        
        [JsonPropertyName("response_for_occurrence")]
        public OccurrenceDecision ResponseForOccurrence { get; set; }
        
        [JsonPropertyName("area")]
        public _Shared.Area.Area Area { get; set; }
    }
}
