using FluentValidation;
using Model._Shared;
using Model.Section.V2._Shared;

namespace Model.Section.V2.AddConstructionStage.Request;

public sealed class AddConstructionStageRequestValidator
    : AbstractValidator<AddConstructionStageRequest>
{
    private readonly EntityReferenceValidator _entityReferenceValidator = new();

    private readonly ConstructionStageV2RequestValidator
        _constructionStageRequestValidator = new();

    public AddConstructionStageRequestValidator()
    {
        CascadeMode = CascadeMode.Stop;

        RuleFor(request => request.Section).NotEmpty();
        RuleFor(request => request.Section)
            .SetValidator(_entityReferenceValidator);

        RuleFor(request => request.SectionReview).NotEmpty();
        RuleFor(request => request.SectionReview)
            .SetValidator(_entityReferenceValidator);
        
        RuleFor(request => request.ConstructionStage).NotEmpty();
        RuleFor(request => request.ConstructionStage)
            .SetValidator(_constructionStageRequestValidator);
    }
}
