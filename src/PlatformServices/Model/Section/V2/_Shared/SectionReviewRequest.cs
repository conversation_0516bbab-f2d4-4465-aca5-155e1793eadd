using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using Model._Shared;
using Model._Shared.File;

namespace Model.Section.V2._Shared;

public sealed record SectionReviewRequest
{
    [JsonPropertyName("id")]
    public Guid? Id { get; set; }

    [JsonPropertyName("start_date")]
    public DateTime StartDate { get; set; }

    [JsonPropertyName("section_type")]
    public EntityReference SectionType { get; set; }

    [JsonPropertyName("description")]
    public string Description { get; set; }

    [JsonPropertyName("drawing")]
    public File Drawing { get; set; }

    [JsonPropertyName("index")]
    public int Index { get; set; }

    [JsonPropertyName("is_under_construction")]
    public bool IsUnderConstruction { get; set; }

    [JsonPropertyName("construction_stages")]
    public List<ConstructionStageV2> ConstructionStages { get; set; } = new();
}