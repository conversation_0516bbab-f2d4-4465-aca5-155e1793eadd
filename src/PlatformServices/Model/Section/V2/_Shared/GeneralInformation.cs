using System.Collections.Generic;
using System.Text.Json.Serialization;
using Model._Shared;
using Model._Shared.Instrument;
using Model._Shared.Structure;
using Model.Section._Shared.MapLineSetting;
using Model.Section._Shared.SectionCoordinates;

namespace Model.Section.V2._Shared;

public sealed record GeneralInformation
{
    [JsonPropertyName("name")]
    public string Name { get; init; }

    [JsonPropertyName("structure")]
    public EntityReference Structure { get; init; }

    [JsonPropertyName("is_skew")]
    public bool IsSkew { get; init; }

    [JsonPropertyName("normal_line_azimuth")]
    public double NormalLineAzimuth { get; init; }

    [JsonPropertyName("skew_line_azimuth")]
    public double? SkewLineAzimuth { get; init; }

    [JsonPropertyName("coordinates")]
    public SectionCoordinates Coordinates { get; init; }

    [JsonPropertyName("map_line_setting")]
    public MapLineSetting MapLineSetting { get; init; }

    [JsonPropertyName("instruments")]
    public HashSet<EntityReference> Instruments { get; init; }
}