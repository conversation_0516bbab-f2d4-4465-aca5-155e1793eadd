using System.Text.Json.Serialization;

namespace Model.Section.V2._Shared;

public sealed record ConstructionStageV2
{
    [JsonPropertyName("description")]
    public string Description { get; init; }

    [JsonPropertyName("drawing")]
    public Model._Shared.File.File Drawing { get; init; }

    [JsonPropertyName("stage")]
    public string Stage { get; init; }

    [JsonPropertyName("is_current_stage")]
    public bool IsCurrentStage { get; init; }
}