using FluentValidation;
using Model._Shared;
using Model.Section.V2._Shared;

namespace Model.Section.V2.AddReview.Request;

public sealed class AddSectionReviewRequestValidator
    : AbstractValidator<AddSectionReviewRequest>
{
    private readonly EntityReferenceValidator _sectionValidator = new();
    private readonly SectionReviewV2Validator _sectionReviewValidator = new();

    public AddSectionReviewRequestValidator()
    {
        CascadeMode = CascadeMode.Stop;

        RuleFor(request => request.Section).NotEmpty();
        
        RuleFor(request => request.Section)
            .SetValidator(_sectionValidator);
        
        RuleFor(request => request.SectionReviewV2).NotEmpty();
        
        RuleFor(request => request.SectionReviewV2)
            .SetValidator(_sectionReviewValidator);
    }
}
