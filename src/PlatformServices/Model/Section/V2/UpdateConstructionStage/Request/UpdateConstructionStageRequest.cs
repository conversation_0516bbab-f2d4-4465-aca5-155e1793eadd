using System;
using Model._Shared;
using Model.Section.V2._Shared;

namespace Model.Section.V2.UpdateConstructionStage.Request;

public sealed record UpdateConstructionStageRequest : RequestWithMetadata
{
    public Guid Id { get; init; }
    public EntityReference Section { get; init; }
    public EntityReference SectionReview { get; init; }
    public ConstructionStageV2 ConstructionStage { get; init; }
}