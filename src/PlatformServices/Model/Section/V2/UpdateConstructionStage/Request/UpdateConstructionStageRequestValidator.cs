using FluentValidation;
using Model._Shared;
using Model.Section.V2._Shared;

namespace Model.Section.V2.UpdateConstructionStage.Request;

public class UpdateConstructionStageRequestValidator
    : AbstractValidator<UpdateConstructionStageRequest>
{
    private readonly EntityReferenceValidator _entityReferenceValidator=new();
    private readonly ConstructionStageV2RequestValidator _constructionStageRequestValidator=new();

    public UpdateConstructionStageRequestValidator()
    {
        CascadeMode=CascadeMode.Stop;
        
        RuleFor(request => request.Id).NotEmpty();
        
        RuleFor(request => request.Section).NotEmpty();
        RuleFor(request => request.Section)
            .SetValidator(_entityReferenceValidator);
        
        RuleFor(request => request.SectionReview).NotEmpty();
        RuleFor(request => request.SectionReview)
            .SetValidator(_entityReferenceValidator);
        
        RuleFor(request => request.ConstructionStage).NotEmpty();
        RuleFor(request => request.ConstructionStage)
            .SetValidator(_constructionStageRequestValidator);
    }
}
