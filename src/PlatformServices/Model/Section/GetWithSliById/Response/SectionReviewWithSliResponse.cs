using Model.StructureType.GetById.Response;
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Model.Section.GetWithSliById.Response
{
    public sealed record SectionReviewWithSliResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("start_date")]
        public DateTime StartDate { get; set; }

        [JsonPropertyName("structure_type")]
        public GetStructureTypeByIdResponse StructureType { get; set; }

        [JsonPropertyName("drawing")]
        public Model._Shared.File.File Drawing { get; set; }

        [JsonPropertyName("sli")]
        public Model._Shared.File.File Sli { get; set; }

        [JsonPropertyName("dxf_has_waterline")]
        public bool DxfHasWaterline { get; set; }

        [JsonPropertyName("index")]
        public int Index { get; set; }

        [JsonPropertyName("construction_stages")]
        public List<ConstructionStageWithSliResponse> ConstructionStages { get; set; }
    }
}
