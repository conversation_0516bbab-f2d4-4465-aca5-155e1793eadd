using System;
using System.Text.Json.Serialization;

namespace Model.Structure._Shared.AutomationFrequency
{
    public sealed record AutomationFrequency
    {
        [JsonPropertyName("interval")]
        public string Interval { get; set; }

        private string _lastUpdate { get; set; }

        [JsonPropertyName("last_update")]
        public DateTime? LastUpdate { get => string.IsNullOrEmpty(_lastUpdate) ? null : DateTime.Parse(_lastUpdate); set => _lastUpdate = value?.ToString("R"); }
    }
}
