using Domain.Enums;
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Model.Instrument.List.Response
{
    public sealed record ListInstrumentResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("identifier")]
        public string Identifier { get; set; }

        [JsonPropertyName("type")]
        public InstrumentType Type { get; set; }

        [JsonPropertyName("sections")]
        public HashSet<Guid> Sections { get; set; } = new();
    }
}
