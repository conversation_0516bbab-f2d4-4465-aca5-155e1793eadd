using Api.Configuration;
using Api.Configuration.Auth;
using Api.Configuration.JsonConverter;
using Api.Configuration.MassTransit;
using Api.Configuration.Options;
using Api.Configuration.Swagger;
using Api.Extensions;
using Api.Healthchecks.SqlServer;
using Application;
using Application.Services;
using Application.Services.BlobStorage;
using Azure.Monitor.OpenTelemetry.AspNetCore;
using Database;
using Domain.Core;
using Domain.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.Globalization;
using System.Linq;

var builder = WebApplication.CreateBuilder(args);

var isLocalEnvironment = builder.Environment.IsEnvironment("Local");

builder.Configuration
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true)
    .AddEnvironmentVariables();

if (!isLocalEnvironment)
{
    builder.Configuration.ConfigureKeyVault();
}

var databaseOptions = builder.Configuration.GetSection("Database:Clients")
    .Get<ConnectionStringOptions>();

var messageBrokerOptions = builder.Configuration.GetSection("MessageBroker:Clients")
    .Get<ConnectionStringOptions>();

var azureMonitorOptions = builder.Configuration.GetSection("AzureMonitor")
    .Get<AzureMonitorOptions>();

var blobStorageSection = builder.Configuration.GetSection("BlobStorage");

builder.Services
    .AddDistributedMemoryCache()
    .AddTelemetry(azureMonitorOptions)
    .Configure<BlobStorageOptions>(blobStorageSection)
    .AddApis(builder.Configuration)
    .AddUseCases()
    .AddEndpointsApiExplorer()
    .AddServices()
    .AddResponseCompression()
    .AddRepos(databaseOptions.ConnectionString)
    .AddMassTransit(messageBrokerOptions.ConnectionString)
    .AddApiVersioning(options =>
    {
        options.AssumeDefaultVersionWhenUnspecified = true;
        options.DefaultApiVersion = new ApiVersion(1, 0);
        options.ReportApiVersions = true;
    })
    .AddCors(options => options.AddDefaultPolicy(builder =>
    {
        builder.AllowAnyHeader();
        builder.AllowAnyMethod();
        builder.AllowAnyOrigin();
    }))
    .AddAuth(builder.Configuration, builder.Environment, options =>
     {
         options.Audience = builder.Configuration["Auth:Clients:ClientId"];
         options.TokenEndpoint = $"{builder.Configuration["Auth:BaseUrl"]}{builder.Configuration["Auth:TokenEndpoint"]}";
     })
    .AddSwagger(builder.Configuration)
    .AddLocalization()
    .AddControllers(options =>
    {
        options.ModelBinderProviders.Insert(0, new Model.CustomModelBinderProvider());
    })
    .AddJsonOptions();

builder.Services
    .AddFluentMigrator(databaseOptions.ConnectionString);

builder.Services
    .AddHealthChecks()
    .AddSqlServer(databaseOptions.ConnectionString);

builder.Services
    .AddHostedService<OutboxPublisherCfg>();

var app = builder.Build();

LocalizationCfg.ConfigureCulture("en-US");

if (app.Environment.IsDevelopment() || app.Environment.IsLocal())
{
    app
        .UseDeveloperExceptionPage()
        .UseSwagger()
        .UseSwaggerUI(options =>
        {
            options.OAuthClientId(builder.Configuration["Auth:Clients:ClientId"]);
            options.OAuth2RedirectUrl(builder.Configuration["Swagger:BaseUrl"] + builder.Configuration["Swagger:OAuth2RedirectEndpoint"]);
            options.OAuthAdditionalQueryStringParams(new() { { "nonce", "b20qlv2lg525sc1" } });
            options.OAuthAppName("Clients API - Swagger");
            options.RoutePrefix = "swagger";
            options.SwaggerEndpoint("v1/swagger.json", "Api v1");
            options.SwaggerEndpoint("v2/swagger.json", "Api v2");
        });
}

app.UseResponseCompression()
    .UseHttpsRedirection()
    .UseCors()
    .UseApiVersioning()
    .UseRouting()
    .UseAuthentication()
    .UseAuthorization()
    .UseEndpoints(endpoints =>
    {
        endpoints.MapHealthChecks("/health");
        endpoints.MapControllers();
    })
    .UseRequestLocalization(new RequestLocalizationOptions()
    {
        SupportedUICultures = Enum.GetValues(typeof(Locale)).Cast<Locale>()
            .Select(x => new CultureInfo(x.GetDescription())).ToList()
    });

app.Migrate(databaseOptions.ConnectionString);

await app.RunAsync();