using Api.Configuration.Auth;
using Api.Extensions;
using Application.Core;
using Application.Reading.Add;
using Application.Reading.AddBeachLength;
using Application.Reading.ConvertFileToAdd;
using Application.Reading.Delete;
using Application.Reading.GetBeachLengthById;
using Application.Reading.GetBeachLengthStats;
using Application.Reading.GetById;
using Application.Reading.GetReferential;
using Application.Reading.GetRelatedReadingValues;
using Application.Reading.GetStats;
using Application.Reading.GetTemplateFile;
using Application.Reading.Patch;
using Application.Reading.Search;
using Application.Reading.SearchBeachLength;
using Application.Reading.SearchBeachLengthHistory;
using Application.Reading.SearchHistory;
using Application.Reading.Update;
using Application.Reading.UpdateBeachLength;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Model;
using Model.Core.Search.Pagination;
using Model.Reading._Shared;
using Model.Reading.Add.Request;
using Model.Reading.AddBeachLength.Request;
using Model.Reading.Convert.Request;
using Model.Reading.Convert.Response;
using Model.Reading.Delete.Request;
using Model.Reading.GetBeachLengthById.Request;
using Model.Reading.GetBeachLengthById.Response;
using Model.Reading.GetBeachLengthStats.Request;
using Model.Reading.GetBeachLengthStats.Response;
using Model.Reading.GetById.Request;
using Model.Reading.GetById.Response;
using Model.Reading.GetReferentialReadingValue.Request;
using Model.Reading.GetReferentialReadingValue.Response;
using Model.Reading.GetRelatedReadingValues.Request;
using Model.Reading.GetStats.Request;
using Model.Reading.GetStats.Response;
using Model.Reading.GetTemplateFile.Request;
using Model.Reading.GetTemplateFile.Response;
using Model.Reading.Patch.Request;
using Model.Reading.Search.Request;
using Model.Reading.SearchBeachLength.Request;
using Model.Reading.SearchBeachLengthHistory.Request;
using Model.Reading.SearchHistory.Request;
using Model.Reading.Update.Request;
using Model.Reading.UpdateBeachLength.Request;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Api.Controllers;

[ApiController]
[ApiVersion("1")]
[Route("api/v{version:apiVersion}/readings")]
public class ReadingController : ControllerBase
{
    [HttpPost]
    [Authorize(PolicyNames.SuperSupportOrSuperAdminOrAdminOrOperatorOrSupport)]
    [ProducesResponseType(200)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    [RequestSizeLimit(100_000_000)]
    public async Task<IActionResult> Add(
        [FromServices] IAddReadingUseCase addReadingUseCase,
        [FromBody] List<ReadingRequest> request
    )
    {
        var req = new AddReadingsRequest()
        {
            Readings = request
        };

        AddRequestMetadata(req);

        var result = await addReadingUseCase
            .Execute(req);

        return result.ToObjectResult();
    }

    [HttpPost("beach-lengths")]
    [Authorize(PolicyNames.SuperSupportOrSuperAdminOrAdminOrOperatorOrSupport)]
    [ProducesResponseType(200)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    public async Task<IActionResult> Add(
        [FromServices] IAddBeachLengthUseCase addBeachLengthUseCase,
        [FromBody] AddBeachLengthRequest request
    )
    {
        AddRequestMetadata(request);

        var result = await addBeachLengthUseCase
            .Execute(request);

        return result.ToObjectResult();
    }

    [HttpPost("values/search")]
    [Authorize]
    [ProducesResponseType(200, Type = typeof(PaginationResponse))]
    [ProducesResponseType(204)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    public async Task<IActionResult> Search(
        [FromServices] ISearchReadingValueUseCase searchReadingValueUseCase,
        [FromQuery] QuerySearchReadingValueRequest query,
        [FromBody] BodySearchReadingValueRequest body
    )
    {
        var request = new SearchReadingValueRequest
        {
            Query = query,
            Body = body
        };

        AddRequestMetadata(request);

        var result = await searchReadingValueUseCase
            .Execute(request);

        return result.ToObjectResult();
    }


    [HttpPost("beach-lengths/search")]
    [Authorize]
    [ProducesResponseType(200, Type = typeof(PaginationResponse))]
    [ProducesResponseType(204)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    public async Task<IActionResult> Search(
        [FromServices] ISearchBeachLengthUseCase searchBeachLengthUseCase,
        [FromQuery] QuerySearchBeachLengthRequest query,
        [FromBody] BodySearchBeachLengthRequest body
    )
    {
        var request = new SearchBeachLengthRequest
        {
            Query = query,
            Body = body
        };

        AddRequestMetadata(request);

        var result = await searchBeachLengthUseCase
            .Execute(request);

        return result.ToObjectResult();
    }

    [HttpGet("{id:guid}")]
    [Authorize]
    [ProducesResponseType(200, Type = typeof(GetReadingByIdResponse))]
    [ProducesResponseType(204)]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    public async Task<IActionResult> GetById(
        [FromServices] IGetReadingByIdUseCase getReadingByIdUseCase,
        Guid id
    )
    {
        var request = new GetReadingByIdRequest
        {
            Id = id
        };

        AddRequestMetadata(request);

        var result = await getReadingByIdUseCase
            .Execute(request);

        return result.ToObjectResult();
    }

    [HttpGet("beach-lengths/{id:guid}")]
    [Authorize]
    [ProducesResponseType(200, Type = typeof(GetBeachLengthByIdResponse))]
    [ProducesResponseType(204)]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    public async Task<IActionResult> GetById(
        [FromServices] IGetBeachLengthByIdUseCase getBeachLengthByIdUseCase,
        Guid id
    )
    {
        var request = new GetBeachLengthByIdRequest
        {
            Id = id
        };

        AddRequestMetadata(request);

        var result = await getBeachLengthByIdUseCase
            .Execute(request);

        return result.ToObjectResult();
    }

    [HttpGet("beach-lengths/{id:guid}/history")]
    [Authorize]
    [ProducesResponseType(200, Type = typeof(PaginationResponse))]
    [ProducesResponseType(204)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    public async Task<IActionResult> Search(
        [FromServices] ISearchBeachLengthHistoryUseCase searchBeachLengthHistoryUseCase,
        Guid id,
        [FromQuery] SearchBeachLengthHistoryRequest request
    )
    {
        request.BeachLengthId = id;

        AddRequestMetadata(request);

        var result = await searchBeachLengthHistoryUseCase
            .Execute(request);

        return result.ToObjectResult();
    }

    [HttpGet("{id:guid}/history")]
    [Authorize]
    [ProducesResponseType(200, Type = typeof(PaginationResponse))]
    [ProducesResponseType(204)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    public async Task<IActionResult> Search(
        [FromServices] ISearchReadingHistoryUseCase searchReadingHistoryUseCase,
        Guid id,
        [FromQuery] SearchReadingHistoryRequest request
    )
    {
        request.ReadingId = id;
        AddRequestMetadata(request);

        var result = await searchReadingHistoryUseCase
            .Execute(request);

        return result.ToObjectResult();
    }

    [HttpGet("{id:guid}/related-values")]
    [Authorize()]
    [ProducesResponseType(200, Type = typeof(IEnumerable<Guid>))]
    [ProducesResponseType(204)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(500)]
    public async Task<IActionResult> GetRelatedReadingValues(
        [FromServices] IGetRelatedReadingValuesUseCase searchReadingHistoryUseCase,
        Guid id)
    {
        var request = new GetRelatedReadingValuesRequest(id);
        AddRequestMetadata(request);

        var result = await searchReadingHistoryUseCase
            .Execute(request);

        return result.ToObjectResult();
    }

    [HttpPut]
    [Authorize(PolicyNames.SuperSupportOrSuperAdminOrAdminOrOperatorOrSupport)]
    [ProducesResponseType(200)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    public async Task<IActionResult> Update(
        [FromServices] IUpdateReadingUseCase updateReadingUseCase,
        [FromBody] UpdateReadingRequest request
    )
    {
        AddRequestMetadata(request);

        var result = await updateReadingUseCase
            .Execute(request);

        return result.ToObjectResult();
    }

    [HttpPut("beach-lengths/{id:guid}")]
    [Authorize(PolicyNames.SuperSupportOrSuperAdminOrAdminOrOperatorOrSupport)]
    [ProducesResponseType(200)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    public async Task<IActionResult> Update(
        Guid id,
        [FromServices] IUpdateBeachLengthUseCase updateBeachLengthUseCase,
        [FromBody] UpdateBeachLengthRequest request
    )
    {
        request.Id = id;

        AddRequestMetadata(request);

        var result = await updateBeachLengthUseCase
            .Execute(request);

        return result.ToObjectResult();
    }

    [HttpPatch("values")]
    [Authorize(PolicyNames.SuperSupportOrSuperAdminOrAdminOrOperatorOrSupport)]
    [ProducesResponseType(200)]
    [ProducesResponseType(204)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    public async Task<IActionResult> Patch(
        [FromServices] IPatchReadingValueUseCase patchReadingValueUseCase,
        [FromBody] List<PatchReadingValueRequest> request)
    {
        var req = new PatchReadingValuesRequest()
        {
            ReadingValues = request
        };

        AddRequestMetadata(req);

        var result = await patchReadingValueUseCase
            .Execute(req);

        return result.ToObjectResult();
    }

    [HttpGet("values/instruments/{instrumentId:guid}/reference")]
    [Authorize(PolicyNames.SuperSupportOrSuperAdminOrAdminOrOperatorOrSupport)]
    [ProducesResponseType(200, Type = typeof(GetReferentialReadingValueResponse))]
    [ProducesResponseType(204)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    public async Task<IActionResult> GetReferentialReadingValue(
        [FromServices] IGetReferentialReadingValueUseCase getInstrumentReferentialReadingValueUseCase,
        Guid instrumentId)
    {
        var request = new GetReferentialReadingValueRequest()
        {
            InstrumentId = instrumentId
        };

        AddRequestMetadata(request);

        var result = await getInstrumentReferentialReadingValueUseCase
            .Execute(request);

        return result.ToObjectResult();
    }

    [HttpDelete]
    [Authorize(PolicyNames.SuperSupportOrSuperAdminOrAdminOrOperatorOrSupport)]
    [ProducesResponseType(200)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    public async Task<IActionResult> Delete(
        [FromServices] IDeleteReadingUseCase deleteReadingUseCase,
        [FromBody] List<Guid> ids
    )
    {
        var request = new DeleteReadingRequest
        {
            Ids = ids
        };

        AddRequestMetadata(request);

        var result = await deleteReadingUseCase
            .Execute(request);

        return result.ToObjectResult();
    }

    [HttpGet("file/download/template")]
    [Authorize(PolicyNames.SuperSupportOrSuperAdminOrAdminOrOperatorOrSupport)]
    [ProducesResponseType(200, Type = typeof(FileContentResult))]
    [ProducesResponseType(204)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    public async Task<IActionResult> GetTemplateFile(
        [FromQuery] GetReadingTemplateFileRequest request,
        [FromServices] IGetReadingTemplateFileUseCase downloadTemplateUseCase)
    {
        AddRequestMetadata(request);

        var result = await downloadTemplateUseCase
            .Execute(request);

        if (!result.IsSuccessful || result.Status == UseCaseResponseKind.NoContent)
        {
            return result.ToObjectResult();
        }

        return File(result.Result.Bytes, result.Result.ContentType, result.Result.FileName);
    }

    [HttpPost("file/convert/to/add")]
    [Authorize(PolicyNames.SuperSupportOrSuperAdminOrAdminOrOperatorOrSupport)]
    [ProducesResponseType(200, Type = typeof(ConvertReadingsResponse))]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    [RequestSizeLimit(25_000_000)]
    public async Task<IActionResult> Convert(
        IFormFile file,
        [FromQuery] Guid structureId,
        [FromServices] IConvertFileToAddReadingUseCase convertFileToAddReadingUseCase
    )
    {
        var request = new ConvertReadingsRequest()
        {
            File = file,
            StructureId = structureId
        };

        AddRequestMetadata(request);

        var result = await convertFileToAddReadingUseCase
            .Execute(request);

        if (result.Status == UseCaseResponseKind.BadRequest && result.Result?.ErrorsFile != null)
        {
            return File(result.Result.ErrorsFile.Bytes, result.Result.ErrorsFile.ContentType, result.Result.ErrorsFile.FileName);
        }

        return result.ToObjectResult();
    }

    [HttpPost("statistical-calculations")]
    [Authorize(PolicyNames.SuperSupport)]
    [ProducesResponseType(200, Type = typeof(List<GetReadingStatsResponse>))]
    [ProducesResponseType(204)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<IActionResult> CalculateStatistics(
        [FromServices] ICalculateReadingStatsUseCase calculateReadingStatsUseCase,
        [FromBody] GetReadingStatsRequest request)
    {
        var result = await calculateReadingStatsUseCase
            .Execute(request);

        return result.ToObjectResult();
    }

    [HttpPost("beach-lengths/statistical-calculations")]
    [Authorize(PolicyNames.SuperSupport)]
    [ProducesResponseType(200, Type = typeof(List<GetBeachLengthStatsResponse>))]
    [ProducesResponseType(204)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    [ApiExplorerSettings(IgnoreApi = true)]
    public async Task<IActionResult> CalculateStatistics(
        [FromServices] ICalculateBeachLengthStatsUseCase calculateBeachLengthStatsUseCase,
        [FromBody] GetBeachLengthStatsRequest request)
    {
        var result = await calculateBeachLengthStatsUseCase
            .Execute(request);

        return result.ToObjectResult();
    }

    private void AddRequestMetadata(RequestWithMetadata request)
    {
        var userRole = User.Role();
        var userId = User.Id();

        request.RequestedBy = userId;
        request.RequestedUserRole = userRole;
        request.RequestedBySuperSupport = User.RequestedBySuperSupport();
    }
}