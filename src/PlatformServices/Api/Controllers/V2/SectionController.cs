using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Api.Configuration.Auth;
using Api.Extensions;
using Application.Core;
using Application.Section.V2.Add;
using Application.Section.V2.AddConstructionStage;
using Application.Section.V2.AddReview;
using Application.Section.V2.GetById;
using Application.Section.V2.GetDrawingFile;
using Application.Section.V2.GetLengthData;
using Application.Section.V2.UpdateConstructionStage;
using Application.Section.V2.UpdateGeneralInformation;
using Application.Section.V2.UpdateReview;
using Application.Section.V2.UpdateStabilityConfiguration;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Model;
using Model._Shared;
using Model._Shared.File;
using Model.Section.V2._Shared;
using Model.Section.V2.Add.Request;
using Model.Section.V2.AddConstructionStage.Request;
using Model.Section.V2.AddReview.Request;
using Model.Section.V2.GetById.Request;
using Model.Section.V2.GetById.Response;
using Model.Section.V2.GetDrawingFile.Request;
using Model.Section.V2.UpdateConstructionStage.Request;
using Model.Section.V2.UpdateGeneralInformation.Request;
using Model.Section.V2.UpdateReview.Request;
using Model.Section.V2.UpdateStabilityConfiguration.Request;
using Swashbuckle.AspNetCore.Annotations;

namespace Api.Controllers.V2;

[ApiController]
[ApiVersion("2")]
[Route("api/v{version:apiVersion}/sections")]
public sealed class SectionController : ControllerBase
{
    [HttpPost]
    [Authorize(PolicyNames.SuperSupportOrSupport)]
    [ProducesResponseType(201)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    [RequestSizeLimit(15_000_000)]
    [SwaggerOperation(Summary = "Creates a new section")]
    public async Task<IActionResult> AddAsync(
        [FromBody] AddSectionRequestV2 request,
        [FromServices] IAddSectionUseCase useCase)
    {
        AddRequestMetadata(request);

        var result = await useCase.Execute(request);

        return result.ToObjectResult();
    }

    [HttpPatch("{id:guid}/general-information")]
    [Authorize(PolicyNames.SuperSupportOrSupport)]
    [ProducesResponseType(200)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    [SwaggerOperation(
        Summary = "Updates general information of an existing section")]
    public async Task<IActionResult> UpdateGeneralInformationAsync(
        Guid id,
        [FromBody] GeneralInformation generalInformation,
        [FromServices] IUpdateGeneralInformationUseCase useCase)
    {
        var request = new UpdateGeneralInformationRequest()
        {
            Id = id, GeneralInformation = generalInformation
        };

        AddRequestMetadata(request);

        var result = await useCase.Execute(request);

        return result.ToObjectResult();
    }

    [HttpPatch("{id:guid}/stability-analysis-configuration")]
    [Authorize(PolicyNames.SuperSupportOrSupport)]
    [ProducesResponseType(200)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    [SwaggerOperation(
        Summary =
            "Updates stability analysis configuration of an existing section")]
    public async Task<IActionResult> UpdateStabilityConfigurationAsync(
        Guid id,
        [FromBody] StabilityAnalysisConfiguration stabilityConfiguration,
        [FromServices] IUpdateStabilityConfigurationUseCase useCase)
    {
        var request = new UpdateStabilityConfigurationRequest
        {
            Id = id, StabilityAnalysisConfiguration = stabilityConfiguration
        };

        AddRequestMetadata(request);

        var result = await useCase.Execute(request);

        return result.ToObjectResult();
    }

    [HttpPost("{id:guid}/reviews")]
    [Authorize(PolicyNames.SuperSupportOrSupport)]
    [ProducesResponseType(201)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    [RequestSizeLimit(15_000_000)]
    [SwaggerOperation(
        Summary = "Adds a section review into an existing section")]
    public async Task<IActionResult> AddSectionReviewAsync(
        Guid id,
        [FromBody] SectionReviewV2 sectionReview,
        [FromServices] IAddSectionReviewUseCase useCase)
    {
        var request = new AddSectionReviewRequest()
        {
            Section = new EntityReference(id),
            SectionReviewV2 = sectionReview
        };

        AddRequestMetadata(request);

        var result = await useCase.Execute(request);

        return result.ToObjectResult();
    }

    [HttpPut("{id:guid}/reviews/{reviewId:guid}")]
    [Authorize(PolicyNames.SuperSupportOrSupport)]
    [ProducesResponseType(201)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    [RequestSizeLimit(15_000_000)]
    [SwaggerOperation(Summary = "Fully updates an existing section review")]
    public async Task<IActionResult> UpdateSectionReviewAsync(
        Guid id,
        Guid reviewId,
        [FromBody] SectionReviewV2 sectionReview,
        [FromServices] IUpdateSectionReviewUseCase useCase)
    {
        var request = new UpdateSectionReviewRequest()
        {
            Id = reviewId,
            Section = new EntityReference(id),
            SectionReviewV2 = sectionReview
        };

        AddRequestMetadata(request);

        var result = await useCase.Execute(request);

        return result.ToObjectResult();
    }

    [HttpPost("{id:guid}/reviews/{reviewId:guid}/construction-stages")]
    [Authorize(PolicyNames.SuperSupportOrSupport)]
    [ProducesResponseType(201)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    [RequestSizeLimit(15_000_000)]
    [SwaggerOperation(
        Summary = "Adds a construction stage into an existing section review")]
    public async Task<IActionResult> AddConstructionStageAsync(
        Guid id,
        Guid reviewId,
        [FromBody] ConstructionStageV2 constructionStage,
        [FromServices] IAddConstructionStageUseCase useCase)
    {
        var request = new AddConstructionStageRequest
        {
            Section = new EntityReference(id),
            SectionReview = new EntityReference(reviewId),
            ConstructionStage = constructionStage
        };

        AddRequestMetadata(request);

        var result = await useCase.Execute(request);

        return result.ToObjectResult();
    }

    [HttpPut(
        "{id:guid}/reviews/{reviewId:guid}/construction-stages/{constructionStageId:guid}")]
    [Authorize(PolicyNames.SuperSupportOrSupport)]
    [ProducesResponseType(201)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    [RequestSizeLimit(15_000_000)]
    [SwaggerOperation(Summary = "Fully updates an existing construction stage")]
    public async Task<IActionResult> UpdateConstructionStageAsync(
        Guid id,
        Guid reviewId,
        Guid constructionStageId,
        [FromBody] ConstructionStageV2 constructionStage,
        [FromServices] IUpdateConstructionStageUseCase useCase)
    {
        var request = new UpdateConstructionStageRequest
        {
            Section = new EntityReference(id),
            SectionReview = new EntityReference(reviewId),
            Id = constructionStageId,
            ConstructionStage = constructionStage
        };

        AddRequestMetadata(request);

        var result = await useCase.Execute(request);

        return result.ToObjectResult();
    }

    [HttpGet("{id:guid}")]
    [Authorize]
    [ProducesResponseType(200, Type = typeof(GetSectionByIdResponse))]
    [ProducesResponseType(204)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    [SwaggerOperation(Summary = "Gets a section by id")]

    public async Task<IActionResult> GetById(
        Guid id,
        [FromServices] IGetSectionByIdUseCase useCase)
    {
        var request = new GetSectionByIdRequest(id);

        AddRequestMetadata(request);
        
        var result = await useCase.Execute(request);

        return result.ToObjectResult();
    }
    
    [HttpGet("{id:guid}/reviews/{reviewId:guid}/drawing")]
    [Authorize]
    [ProducesResponseType(200, Type = typeof(File))]
    [ProducesResponseType(204)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    [SwaggerOperation(Summary = "Gets the section review's drawing file by id")]

    public async Task<IActionResult> GetSectionReviewDrawing(Guid id,
        Guid reviewId,
        [FromServices] IGetSectionDrawingFileUseCase useCase)
    {
        var request = new GetSectionDrawingFileRequest
        {
            SectionId = id,
            SectionReviewId = reviewId
        };
        
        AddRequestMetadata(request);
        
        var result = await useCase.Execute(request);

        return result.ToObjectResult();
    }
    
    [HttpGet(
        "{id:guid}/reviews/{reviewId:guid}/construction-stages/{constructionStageId:guid}/drawing")]
    [Authorize]
    [ProducesResponseType(200, Type = typeof(File))]
    [ProducesResponseType(204)]
    [ProducesResponseType(400, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    [SwaggerOperation(Summary = "Gets the construction stage's drawing file by id")]
    public async Task<IActionResult> GetConstructionStageDrawing(
        Guid id,
        Guid reviewId,
        Guid constructionStageId,
        [FromServices] IGetSectionDrawingFileUseCase useCase)
    {
        var request = new GetSectionDrawingFileRequest
        {
            SectionId = id,
            SectionReviewId = reviewId,
            ConstructionStageId = constructionStageId
        };
        
        AddRequestMetadata(request);
        
        var result = await useCase.Execute(request);

        return result.ToObjectResult();
    }

    [HttpGet("length-data")]
    [Authorize(PolicyNames.SuperSupportOrSupport)]
    [ProducesResponseType(200, Type = typeof(FileResult))]
    [ProducesResponseType(401, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(403, Type = typeof(IEnumerable<ErrorMessage>))]
    [ProducesResponseType(500, Type = typeof(IEnumerable<ErrorMessage>))]
    [SwaggerOperation(Summary = "Recalculates and updates length data for all section reviews and construction stages and returns a CSV report")]
    public async Task<IActionResult> GetLengthDataAsync(
        [FromServices] IGetLengthDataUseCase useCase)
    {
        var result = await useCase.Execute();

        return result.ToFileResult();
    }

    private void AddRequestMetadata<T>(T request) where T : RequestWithMetadata
    {
        var userRole = User.Role();
        var userId = User.Id();

        request.RequestedBy = userId;
        request.RequestedUserRole = userRole;
        request.RequestedBySuperSupport = User.RequestedBySuperSupport();
    }
}
