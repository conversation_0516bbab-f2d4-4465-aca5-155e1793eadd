using Domain.Extensions;

namespace Domain.Tests.Extensions.StringExtensions;

[Trait("StringExtensions", "ToBool")]
public class ToBoolTests
{
    [Fact(DisplayName = "When string is null, then returns false")]
    public void WhenStringIsNull_ReturnsFalse()
    {
        string value = null;

        var result = value.ToBool();

        result.Should().BeFalse();
    }
    
    [Fact(DisplayName = "When string is empty, then returns false")]
    public void WhenStringIsEmpty_ReturnsFalse()
    {
        string value = string.Empty;

        var result = value.ToBool();

        result.Should().BeFalse();
    }
    
    [Theory(DisplayName = "When string is a valid boolean representation, then returns true")]
    [InlineData("true")]
    [InlineData("t")]
    [InlineData("yes")]
    [InlineData("1")]
    [InlineData("sim")]
    [InlineData("s")]
    [InlineData("verdadeiro")]
    public void WhenStringIsValidBooleanRepresentation_ReturnsTrue(string value)
    {
        var result = value.ToBool();

        result.Should().BeTrue();
    }
    
    [Theory(DisplayName = "When string is a valid false representation, then returns false")]
    [InlineData("false")]
    [InlineData("f")]
    [InlineData("no")]
    [InlineData("0")]
    [InlineData("não")]
    [InlineData("nao")]
    [InlineData("n")]
    [InlineData("falso")]
    public void WhenStringIsValidFalseRepresentation_ReturnsFalse(string value)
    {
        var result = value.ToBool();

        result.Should().BeFalse();
    }
    
    [Fact(DisplayName = "When string is an invalid boolean representation, then returns false")]
    public void WhenStringIsInvalidBooleanRepresentation_ReturnsFalse()
    {
        string value = "invalid";

        var result = value.ToBool();

        result.Should().BeFalse();
    }
}