using Bogus;
using Domain.Extensions;

namespace Domain.Tests.Extensions.StringExtensions;

[Trait("StringExtensions", "ToUtcDateTime")]
public class ToUtcDateTimeTests
{
    private readonly Faker _faker = new();

    [Theory(DisplayName = "When valid date formats without time are provided, then converts to DateTime")]
    [InlineData("1/1/23")]
    [InlineData("29/2/24")]
    [InlineData("1-1-23")]
    [InlineData("29-2-24")]
    [InlineData("01/01/23")]
    [InlineData("31/12/23")]
    [InlineData("29/02/24")]
    [InlineData("01-01-23")]
    [InlineData("31-12-23")]
    [InlineData("29-02-24")]
    [InlineData("1/1/2023")]
    [InlineData("29/2/2024")]
    [InlineData("1-1-2023")]
    [InlineData("29-2-2024")]
    [InlineData("01/01/2023")]
    [InlineData("31/12/2023")]
    [InlineData("29/02/2024")]
    [InlineData("01-01-2023")]
    [InlineData("31-12-2023")]
    [InlineData("29-02-2024")]
    [InlineData("1/1/2023 00:00")]
    [InlineData("1/1/2023  00:00")]
    [InlineData("29/2/2024 23:59")]
    [InlineData("29/2/2024  23:59")]
    [InlineData("1-1-2023 00:00")]
    [InlineData("1-1-2023  00:00")]
    [InlineData("29-2-2024 23:59")]
    [InlineData("29-2-2024  23:59")]
    [InlineData("01/01/2023 00:00")]
    [InlineData("01/01/2023  00:00")]
    [InlineData("31/12/2023 23:59")]
    [InlineData("31/12/2023  23:59")]
    [InlineData("29/02/2024 23:59")]
    [InlineData("29/02/2024  23:59")]
    [InlineData("01-01-2023 00:00")]
    [InlineData("01-01-2023  00:00")]
    [InlineData("31-12-2023 23:59")]
    [InlineData("31-12-2023  23:59")]
    [InlineData("29-02-2024 23:59")]
    [InlineData("29-02-2024  23:59")]
    [InlineData("1/1/2023 10:30:00")]
    [InlineData("1/1/2023  10:30:00")]
    [InlineData("29/2/2024 23:59:59")]
    [InlineData("29/2/2024  23:59:59")]
    [InlineData("1-1-2023 10:30:00")]
    [InlineData("1-1-2023  10:30:00")]
    [InlineData("29-2-2024 23:59:59")]
    [InlineData("29-2-2024  23:59:59")]
    [InlineData("01/01/2023 10:30:00")]
    [InlineData("01/01/2023  10:30:00")]
    [InlineData("31/12/2023 23:59:59")]
    [InlineData("31/12/2023  23:59:59")]
    [InlineData("29/02/2024 23:59:59")]
    [InlineData("29/02/2024  23:59:59")]
    [InlineData("01-01-2023 10:30:00")]
    [InlineData("01-01-2023  10:30:00")]
    [InlineData("31-12-2023 23:59:59")]
    [InlineData("31-12-2023  23:59:59")]
    [InlineData("29-02-2024 23:59:59")]
    [InlineData("29-02-2024  23:59:59")]
    public void WhenValidDateFormatsWithoutTimeAreProvided_ThenConvertsToDateTime(string input)
    {
        var result = input.ToUtcDateTime();

        result.Should().NotBe(default(DateTime));
    }

    [Fact(DisplayName = "When null input is provided, then throws ArgumentNullException")]
    public void WhenNullInputIsProvided_ThenThrowsArgumentNullException()
    {
        string? input = null;

        var action = () => input.ToUtcDateTime();

        action.Should().Throw<ArgumentNullException>();
    }

    [Theory(DisplayName = "When invalid date formats are provided, then throws FormatException")]
    [InlineData("2023-01-01")] // ISO format
    [InlineData("2023/01/01")] // ISO format with slashes
    [InlineData("January 1, 2023")] // English format
    [InlineData("01.01.2023")] // German format
    [InlineData("1 Jan 2023")] // English short format
    [InlineData("2023-01-01T10:30:00")] // ISO with time
    [InlineData("29/02/2023")] // Invalid leap year
    [InlineData("29/02/1900")] // Invalid leap year (century rule)
    [InlineData("29-02-2023")] // Invalid leap year with dash
    [InlineData("29-02-1900")] // Invalid leap year with dash (century rule)
    [InlineData("32/01/2023")] // Invalid day - January
    [InlineData("31/04/2023")] // Invalid day - April has 30 days
    [InlineData("31/06/2023")] // Invalid day - June has 30 days
    [InlineData("31/09/2023")] // Invalid day - September has 30 days
    [InlineData("31/11/2023")] // Invalid day - November has 30 days
    [InlineData("30/02/2023")] // Invalid day - February
    [InlineData("00/01/2023")] // Invalid day - zero
    [InlineData("01/13/2023")] // Invalid month
    [InlineData("01/00/2023")] // Invalid month - zero
    [InlineData("15/15/2023")] // Invalid month
    [InlineData("01/01/2023 25:00")] // Invalid hour
    [InlineData("01/01/2023 23:60")] // Invalid minute
    [InlineData("01/01/2023 23:59:60")] // Invalid second
    [InlineData("01/01/2023 -1:00")] // Negative hour
    [InlineData("01/01/2023 10:-1")] // Negative minute
    [InlineData("01/01/2023 10:30:-1")] // Negative second
    [InlineData("01/01")]
    [InlineData("01/01/")]
    [InlineData("/01/2023")]
    [InlineData("01//2023")]
    [InlineData("01/01/23/")]
    [InlineData("test")]
    [InlineData("1234567")]
    [InlineData("abc/def/ghi")]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("\t")]
    [InlineData("\n")]
    public void WhenInvalidDateFormatsAreProvided_ThenThrowsFormatException(string input)
    {
        var action = () => input.ToUtcDateTime();

        action.Should().Throw<FormatException>();
    }

    [Fact(DisplayName = "When valid date is provided, then returns UTC DateTime Kind")]
    public void WhenValidDateIsProvided_ThenReturnsUtcDateTimeKind()
    {
        const string input = "01/01/2023 12:00:00";

        var result = input.ToUtcDateTime();

        result.Kind.Should().Be(DateTimeKind.Utc);
    }

    [Fact(DisplayName = "When valid date is provided, then returns expected UTC DateTime")]
    public void WhenValidDateIsProvided_ThenReturnsExpectedUtcDateTime()
    {
        const string input = "01/01/2023 15:00:00";
        var expected = new DateTime(2023, 01, 01, 18, 00, 00, DateTimeKind.Utc);

        var result = input.ToUtcDateTime();

        result.Should().Be(expected);
    }

    [Fact(DisplayName = "When valid date without time is provided, then time defaults to midnight")]
    public void WhenValidDateWithoutTimeIsProvided_ThenTimeDefaultsToMidnight()
    {
        const string input = "01/01/2023";
        var expected = new DateTime(2023, 01, 01, 03, 00, 00, DateTimeKind.Utc);

        var result = input.ToUtcDateTime();

        result.TimeOfDay.Should().Be(expected.TimeOfDay);
    }

    [Fact(DisplayName = "When using Faker generated valid date, then converts successfully")]
    public void WhenUsingFakerGeneratedValidDate_ThenConvertsSuccessfully()
    {
        var date = _faker.Date.Between(new DateTime(2020, 1, 1), new DateTime(2030, 12, 31));
        var input = date.ToString("dd/MM/yyyy");

        var result = input.ToUtcDateTime();

        result.Should().NotBe(default(DateTime));
    }

    [Fact(DisplayName = "When using Faker generated valid date with time, then converts successfully")]
    public void WhenUsingFakerGeneratedValidDateWithTime_ThenConvertsSuccessfully()
    {
        var date = _faker.Date.Between(new DateTime(2020, 1, 1), new DateTime(2030, 12, 31));
        var input = date.ToString("dd/MM/yyyy HH:mm:ss");

        var result = input.ToUtcDateTime();

        result.Should().NotBe(default(DateTime));
    }
}
