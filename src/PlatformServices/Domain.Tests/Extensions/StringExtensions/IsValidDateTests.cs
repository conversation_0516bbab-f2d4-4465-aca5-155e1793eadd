using Domain.Extensions;

namespace Domain.Tests.Extensions.StringExtensions;

[Trai<PERSON>("StringExtensions", "IsValidDate")]
public class IsValidDateTests
{
    [Theory(DisplayName = "When valid date formats are provided, then returns true")]
    [InlineData("1/1/23")]
    [InlineData("29/2/24")] 
    [InlineData("1-1-23")]
    [InlineData("29-2-24")]
    [InlineData("01/01/23")]
    [InlineData("31/12/23")]
    [InlineData("29/02/24")] 
    [InlineData("01-01-23")]
    [InlineData("31-12-23")]
    [InlineData("29-02-24")] 
    [InlineData("1/1/2023")]
    [InlineData("29/2/2024")]
    [InlineData("1-1-2023")]
    [InlineData("29-2-2024")]
    [InlineData("01/01/2023")]
    [InlineData("31/12/2023")]
    [InlineData("29/02/2024")]
    [InlineData("01-01-2023")]
    [InlineData("31-12-2023")]
    [InlineData("29-02-2024")] 
    [InlineData("1/1/2023 00:00")]
    [InlineData("1/1/2023  00:00")]
    [InlineData("29/2/2024 23:59")]
    [InlineData("29/2/2024  23:59")]
    [InlineData("1-1-2023 00:00")]
    [InlineData("1-1-2023  00:00")]
    [InlineData("29-2-2024 23:59")]
    [InlineData("29-2-2024  23:59")]
    [InlineData("01/01/2023 00:00")]
    [InlineData("01/01/2023  00:00")]
    [InlineData("31/12/2023 23:59")]
    [InlineData("31/12/2023  23:59")]
    [InlineData("29/02/2024 23:59")]
    [InlineData("29/02/2024  23:59")]
    [InlineData("01-01-2023 00:00")]
    [InlineData("01-01-2023  00:00")]
    [InlineData("31-12-2023 23:59")]
    [InlineData("31-12-2023  23:59")]
    [InlineData("29-02-2024 23:59")]
    [InlineData("29-02-2024  23:59")]
    [InlineData("1/1/2023 10:30:00")]
    [InlineData("1/1/2023  10:30:00")]
    [InlineData("29/2/2024 23:59:59")]
    [InlineData("29/2/2024  23:59:59")]
    [InlineData("1-1-2023 10:30:00")]
    [InlineData("1-1-2023  10:30:00")]
    [InlineData("29-2-2024 23:59:59")]
    [InlineData("29-2-2024  23:59:59")]
    [InlineData("01/01/2023 10:30:00")]
    [InlineData("01/01/2023  10:30:00")]
    [InlineData("31/12/2023 23:59:59")]
    [InlineData("31/12/2023  23:59:59")]
    [InlineData("29/02/2024 23:59:59")]
    [InlineData("29/02/2024  23:59:59")]
    [InlineData("01-01-2023 10:30:00")]
    [InlineData("01-01-2023  10:30:00")]
    [InlineData("31-12-2023 23:59:59")]
    [InlineData("31-12-2023  23:59:59")]
    [InlineData("29-02-2024 23:59:59")]
    [InlineData("29-02-2024  23:59:59")]
    public void WhenValidPtBrDateFormatsAreProvided_ThenReturnsTrue(
        string input)
    {
        var result = input.IsValidInputDate();

        result.Should().BeTrue();
    }

    [Theory(DisplayName = "When invalid date formats are provided, then returns false")]
    [InlineData("2023-01-01")] // ISO format
    [InlineData("2023/01/01")] // ISO format with slashes
    [InlineData("January 1, 2023")] // English format
    [InlineData("01.01.2023")] // German format
    [InlineData("test")] // Non-date text
    [InlineData("1234567")] // Non-date text
    [InlineData("")] // Empty string
    [InlineData("   ")] // Whitespace only
    public void WhenInvalidPtBrDateFormatsAreProvided_ThenReturnsFalse(
        string input)
    {
        var result = input.IsValidInputDate();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When null is provided, then returns false")]
    public void WhenNullIsProvided_ThenReturnsFalse()
    {
        string input = null;
        
        var result = input.IsValidInputDate();

        result.Should().BeFalse();
    }
}
