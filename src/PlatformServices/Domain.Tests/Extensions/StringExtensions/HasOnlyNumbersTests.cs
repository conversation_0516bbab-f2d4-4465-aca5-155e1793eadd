using Domain.Extensions;

namespace Domain.Tests.Extensions.StringExtensions;

[Trait("StringExtensions", "HasOnlyNumbers")]
public class HasOnlyNumbersTests
{
    [Fact(DisplayName = "When string contains only numbers, then returns true")]
    public void WhenStringContainsOnlyNumbers_ThenReturnsTrue()
    {
        const string input = "123456";

        var result = input.HasOnlyNumbers();

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When string contains letters, then returns false")]
    public void WhenStringContainsLetters_ThenReturnsFalse()
    {
        const string input = "123abc";

        var result = input.HasOnlyNumbers();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When string is empty, then returns false")]
    public void WhenStringIsEmpty_ThenReturnsFalse()
    {
        const string input = "";

        var result = input.HasOnlyNumbers();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When string is null, then returns false")]
    public void WhenStringIsNull_ThenReturnsFalse()
    {
        string input = null;

        var result = input.HasOnlyNumbers();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When string contains special characters, then returns false")]
    public void WhenStringContainsSpecialCharacters_ThenReturnsFalse()
    {
        const string input = "123!@#";

        var result = input.HasOnlyNumbers();

        result.Should().BeFalse();
    }
}
