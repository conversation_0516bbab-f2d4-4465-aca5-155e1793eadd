using Bogus;
using Domain.Entities;

namespace Domain.Tests.Entities.ReadingValue;

[Trait("ReadingValue", "CalculateRelativeSettlementFromDepth")]
public class CalculateRelativeSettlementFromDepthTests
{
    private static readonly Faker Faker = new();
    private const int MetersToMillimetersMultiplier = 1000;

    [Fact(DisplayName = "When is referential measurement, then returns zero")]
    public void WhenIsReferentialMeasurement_ThenReturnsZero()
    {
        var isReferentialMeasurement = true;
        var readingDeltaRef = Faker.Random.Decimal(-10, 10);
        var measurementDeltaRef = Faker.Random.Decimal(-10, 10);

        var result = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, readingDeltaRef, measurementDeltaRef);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When delta references are equal, then returns zero")]
    public void WhenDeltaReferencesAreEqual_ThenReturnsZero()
    {
        var isReferentialMeasurement = false;
        var deltaRef = 5.5m;
        var readingDeltaRef = deltaRef;
        var measurementDeltaRef = deltaRef;

        var result = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, readingDeltaRef, measurementDeltaRef);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When reading delta ref is greater than measurement delta ref, then returns positive settlement")]
    public void WhenReadingDeltaRefIsGreaterThanMeasurementDeltaRef_ThenReturnsPositiveSettlement()
    {
        var isReferentialMeasurement = false;
        var readingDeltaRef = 6.0m; // 6.0 meters
        var measurementDeltaRef = 5.0m; // 5.0 meters
        var expectedResult = 1000m; // (6.0 - 5.0) * 1000 = 1000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, readingDeltaRef, measurementDeltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When reading delta ref is less than measurement delta ref, then returns negative settlement")]
    public void WhenReadingDeltaRefIsLessThanMeasurementDeltaRef_ThenReturnsNegativeSettlement()
    {
        var isReferentialMeasurement = false;
        var readingDeltaRef = 4.0m; // 4.0 meters
        var measurementDeltaRef = 5.0m; // 5.0 meters
        var expectedResult = -1000m; // (4.0 - 5.0) * 1000 = -1000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, readingDeltaRef, measurementDeltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When both delta refs are zero, then returns zero")]
    public void WhenBothDeltaRefsAreZero_ThenReturnsZero()
    {
        var isReferentialMeasurement = false;
        var readingDeltaRef = 0m;
        var measurementDeltaRef = 0m;

        var result = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, readingDeltaRef, measurementDeltaRef);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When reading delta ref is zero and measurement delta ref is positive, then returns negative settlement")]
    public void WhenReadingDeltaRefIsZeroAndMeasurementDeltaRefIsPositive_ThenReturnsNegativeSettlement()
    {
        var isReferentialMeasurement = false;
        var readingDeltaRef = 0m;
        var measurementDeltaRef = 3.0m; // 3.0 meters
        var expectedResult = -3000m; // (0 - 3.0) * 1000 = -3000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, readingDeltaRef, measurementDeltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When reading delta ref is positive and measurement delta ref is zero, then returns positive settlement")]
    public void WhenReadingDeltaRefIsPositiveAndMeasurementDeltaRefIsZero_ThenReturnsPositiveSettlement()
    {
        var isReferentialMeasurement = false;
        var readingDeltaRef = 3.0m; // 3.0 meters
        var measurementDeltaRef = 0m;
        var expectedResult = 3000m; // (3.0 - 0) * 1000 = 3000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, readingDeltaRef, measurementDeltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When both delta refs are negative, then calculates correctly")]
    public void WhenBothDeltaRefsAreNegative_ThenCalculatesCorrectly()
    {
        var isReferentialMeasurement = false;
        var readingDeltaRef = -2.0m; // -2.0 meters
        var measurementDeltaRef = -5.0m; // -5.0 meters
        var expectedResult = 3000m; // (-2.0 - (-5.0)) * 1000 = 3.0 * 1000 = 3000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, readingDeltaRef, measurementDeltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When reading delta ref is negative and measurement delta ref is positive, then returns negative settlement")]
    public void WhenReadingDeltaRefIsNegativeAndMeasurementDeltaRefIsPositive_ThenReturnsNegativeSettlement()
    {
        var isReferentialMeasurement = false;
        var readingDeltaRef = -2.0m; // -2.0 meters
        var measurementDeltaRef = 3.0m; // 3.0 meters
        var expectedResult = -5000m; // (-2.0 - 3.0) * 1000 = -5.0 * 1000 = -5000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, readingDeltaRef, measurementDeltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When reading delta ref is positive and measurement delta ref is negative, then returns positive settlement")]
    public void WhenReadingDeltaRefIsPositiveAndMeasurementDeltaRefIsNegative_ThenReturnsPositiveSettlement()
    {
        var isReferentialMeasurement = false;
        var readingDeltaRef = 3.0m; // 3.0 meters
        var measurementDeltaRef = -2.0m; // -2.0 meters
        var expectedResult = 5000m; // (3.0 - (-2.0)) * 1000 = 5.0 * 1000 = 5000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, readingDeltaRef, measurementDeltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using decimal precision, then calculates accurately")]
    public void WhenUsingDecimalPrecision_ThenCalculatesAccurately()
    {
        var isReferentialMeasurement = false;
        var readingDeltaRef = 1.234567m; // 1.234567 meters
        var measurementDeltaRef = 0.987654m; // 0.987654 meters
        var expectedResult = 246.913m; // (1.234567 - 0.987654) * 1000 = 0.246913 * 1000 = 246.913 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, readingDeltaRef, measurementDeltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using very large values, then calculates correctly")]
    public void WhenUsingVeryLargeValues_ThenCalculatesCorrectly()
    {
        var isReferentialMeasurement = false;
        var readingDeltaRef = 100.0m; // 100.0 meters
        var measurementDeltaRef = 50.0m; // 50.0 meters
        var expectedResult = 50000m; // (100.0 - 50.0) * 1000 = 50.0 * 1000 = 50000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, readingDeltaRef, measurementDeltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using very small values, then calculates correctly")]
    public void WhenUsingVerySmallValues_ThenCalculatesCorrectly()
    {
        var isReferentialMeasurement = false;
        var readingDeltaRef = 0.002m; // 0.002 meters
        var measurementDeltaRef = 0.001m; // 0.001 meters
        var expectedResult = 1m; // (0.002 - 0.001) * 1000 = 0.001 * 1000 = 1 millimeter

        var result = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, readingDeltaRef, measurementDeltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When difference is 1 meter, then returns 1000 millimeters")]
    public void WhenDifferenceIs1Meter_ThenReturns1000Millimeters()
    {
        var isReferentialMeasurement = false;
        var readingDeltaRef = 6.0m; // 6.0 meters
        var measurementDeltaRef = 5.0m; // 5.0 meters
        var expectedResult = 1000m; // (6.0 - 5.0) * 1000 = 1.0 * 1000 = 1000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, readingDeltaRef, measurementDeltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using random values, then formula is consistent")]
    public void WhenUsingRandomValues_ThenFormulaIsConsistent()
    {
        var isReferentialMeasurement = false;
        var readingDeltaRef = Faker.Random.Decimal(-100, 100);
        var measurementDeltaRef = Faker.Random.Decimal(-100, 100);
        var expectedResult = (readingDeltaRef - measurementDeltaRef) * MetersToMillimetersMultiplier;

        var result = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, readingDeltaRef, measurementDeltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When swapping delta ref parameters, then result sign changes")]
    public void WhenSwappingDeltaRefParameters_ThenResultSignChanges()
    {
        var isReferentialMeasurement = false;
        var deltaRef1 = 6.0m;
        var deltaRef2 = 4.0m;

        var result1 = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, deltaRef1, deltaRef2);
        var result2 = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, deltaRef2, deltaRef1);

        result1.Should().Be(-result2);
    }

    [Fact(DisplayName = "When settlement is positive, then reading has settled more than measurement")]
    public void WhenSettlementIsPositive_ThenReadingHasSettledMoreThanMeasurement()
    {
        var isReferentialMeasurement = false;
        var readingDeltaRef = 7.0m; // Reading has settled 7 meters
        var measurementDeltaRef = 5.0m; // Measurement reference settled 5 meters
        
        var settlement = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, readingDeltaRef, measurementDeltaRef);

        settlement.Should().BePositive();
        settlement.Should().Be(2000m); // 2 meters more settlement = 2000mm
    }

    [Fact(DisplayName = "When settlement is negative, then reading has settled less than measurement")]
    public void WhenSettlementIsNegative_ThenReadingHasSettledLessThanMeasurement()
    {
        var isReferentialMeasurement = false;
        var readingDeltaRef = 3.0m; // Reading has settled 3 meters
        var measurementDeltaRef = 5.0m; // Measurement reference settled 5 meters
        
        var settlement = Domain.Entities.ReadingValue.CalculateRelativeSettlementFromDepth(
            isReferentialMeasurement, readingDeltaRef, measurementDeltaRef);

        settlement.Should().BeNegative();
        settlement.Should().Be(-2000m); // 2 meters less settlement = -2000mm
    }
}
