using Bogus;
using Domain.Entities;

namespace Domain.Tests.Entities.ReadingValue;

[Trait("ReadingValue", "CalculateUtmCoordinateDisplacement")]
public class CalculateUtmCoordinateDisplacementTests
{
    private static readonly Faker Faker = new();

    [Fact(DisplayName = "When is referential, then returns zero")]
    public void WhenIsReferential_ThenReturnsZero()
    {
        var isReferential = true;
        var readingCoordinate = Faker.Random.Decimal(100000, 999999);
        var referentialCoordinate = Faker.Random.Decimal(100000, 999999);

        var result = Domain.Entities.ReadingValue.CalculateUtmCoordinateDisplacement(
            isReferential, readingCoordinate, referentialCoordinate);

        result.Should().Be(0);
    }

    [Fact(DisplayName = "When referential coordinate is null, then returns zero")]
    public void WhenReferentialCoordinateIsNull_ThenReturnsZero()
    {
        var isReferential = false;
        var readingCoordinate = Faker.Random.Decimal(100000, 999999);
        decimal? referentialCoordinate = null;

        var result = Domain.Entities.ReadingValue.CalculateUtmCoordinateDisplacement(
            isReferential, readingCoordinate, referentialCoordinate);

        result.Should().Be(0);
    }

    [Fact(DisplayName = "When both coordinates are equal, then returns zero")]
    public void WhenBothCoordinatesAreEqual_ThenReturnsZero()
    {
        var isReferential = false;
        var coordinate = 606905.12345678m;
        var referentialCoordinate = 606905.12345678m;

        var result = Domain.Entities.ReadingValue.CalculateUtmCoordinateDisplacement(
            isReferential, coordinate, referentialCoordinate);

        result.Should().Be(0);
    }

    [Fact(DisplayName = "When reading coordinate is greater than referential, then returns positive displacement")]
    public void WhenReadingCoordinateIsGreaterThanReferential_ThenReturnsPositiveDisplacement()
    {
        var isReferential = false;
        var readingCoordinate = 606905.12345678m;
        var referentialCoordinate = 606904.12345678m;
        var expectedDisplacement = 1000m; // 1 meter * 1000 = 1000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateUtmCoordinateDisplacement(
            isReferential, readingCoordinate, referentialCoordinate);

        result.Should().Be(expectedDisplacement);
    }

    [Fact(DisplayName = "When reading coordinate is less than referential, then returns negative displacement")]
    public void WhenReadingCoordinateIsLessThanReferential_ThenReturnsNegativeDisplacement()
    {
        var isReferential = false;
        var readingCoordinate = 606904.12345678m;
        var referentialCoordinate = 606905.12345678m;
        var expectedDisplacement = -1000m; // -1 meter * 1000 = -1000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateUtmCoordinateDisplacement(
            isReferential, readingCoordinate, referentialCoordinate);

        result.Should().Be(expectedDisplacement);
    }

    [Fact(DisplayName = "When coordinates have precision beyond 8 decimal places, then rounds to 8 decimal places")]
    public void WhenCoordinatesHavePrecisionBeyond8DecimalPlaces_ThenRoundsTo8DecimalPlaces()
    {
        var isReferential = false;
        var readingCoordinate = 606905.123456789m; // 9 decimal places
        var referentialCoordinate = 606904.123456781m; // 9 decimal places
        var expectedDisplacement = 1000.00001000m; // Actual result after rounding to 8 decimal places

        var result = Domain.Entities.ReadingValue.CalculateUtmCoordinateDisplacement(
            isReferential, readingCoordinate, referentialCoordinate);

        result.Should().Be(expectedDisplacement);
    }

    [Fact(DisplayName = "When displacement is very small, then returns correct millimeter value")]
    public void WhenDisplacementIsVerySmall_ThenReturnsCorrectMillimeterValue()
    {
        var isReferential = false;
        var readingCoordinate = 606905.00000001m;
        var referentialCoordinate = 606905.00000000m;
        var expectedDisplacement = 0.00001000m; // Actual result after rounding to 8 decimal places

        var result = Domain.Entities.ReadingValue.CalculateUtmCoordinateDisplacement(
            isReferential, readingCoordinate, referentialCoordinate);

        result.Should().Be(expectedDisplacement);
    }

    [Fact(DisplayName = "When displacement is large, then returns correct millimeter value")]
    public void WhenDisplacementIsLarge_ThenReturnsCorrectMillimeterValue()
    {
        var isReferential = false;
        var readingCoordinate = 606915.12345678m;
        var referentialCoordinate = 606905.12345678m;
        var expectedDisplacement = 10000m; // 10 meters * 1000 = 10000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateUtmCoordinateDisplacement(
            isReferential, readingCoordinate, referentialCoordinate);

        result.Should().Be(expectedDisplacement);
    }
}
