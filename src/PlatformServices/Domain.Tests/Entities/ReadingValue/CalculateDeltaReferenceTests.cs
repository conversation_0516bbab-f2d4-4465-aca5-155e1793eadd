using Bogus;
using Domain.Entities;

namespace Domain.Tests.Entities.ReadingValue;

[Trait("ReadingValue", "CalculateDeltaReference")]
public class CalculateDeltaReferenceTests
{
    private static readonly Faker Faker = new();
    private const int MetersToMillimetersMultiplier = 1000;

    [Fact(DisplayName = "When is referential magnetic ring, then returns zero")]
    public void WhenIsReferentialMagneticRing_ThenReturnsZero()
    {
        var isReferentialMagneticRing = true;
        var referentialMagneticRingAbsoluteDepth = Faker.Random.Decimal(0, 10000);
        var absoluteDepth = Faker.Random.Decimal(0, 10000);

        var result = Domain.Entities.ReadingValue.CalculateDeltaReference(
            isReferentialMagneticRing, referentialMagneticRingAbsoluteDepth, absoluteDepth);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When is not referential and depths are equal, then returns zero")]
    public void WhenIsNotReferentialAndDepthsAreEqual_ThenReturnsZero()
    {
        var isReferentialMagneticRing = false;
        var depth = 5000m; // 5000 millimeters
        var referentialMagneticRingAbsoluteDepth = depth;
        var absoluteDepth = depth;

        var result = Domain.Entities.ReadingValue.CalculateDeltaReference(
            isReferentialMagneticRing, referentialMagneticRingAbsoluteDepth, absoluteDepth);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When referential depth is greater than absolute depth, then returns positive delta")]
    public void WhenReferentialDepthIsGreaterThanAbsoluteDepth_ThenReturnsPositiveDelta()
    {
        var isReferentialMagneticRing = false;
        var referentialMagneticRingAbsoluteDepth = 6000m; // 6000 millimeters
        var absoluteDepth = 5000m; // 5000 millimeters
        var expectedResult = 1m; // (6000 - 5000) / 1000 = 1 meter

        var result = Domain.Entities.ReadingValue.CalculateDeltaReference(
            isReferentialMagneticRing, referentialMagneticRingAbsoluteDepth, absoluteDepth);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When referential depth is less than absolute depth, then returns negative delta")]
    public void WhenReferentialDepthIsLessThanAbsoluteDepth_ThenReturnsNegativeDelta()
    {
        var isReferentialMagneticRing = false;
        var referentialMagneticRingAbsoluteDepth = 4000m; // 4000 millimeters
        var absoluteDepth = 5000m; // 5000 millimeters
        var expectedResult = -1m; // (4000 - 5000) / 1000 = -1 meter

        var result = Domain.Entities.ReadingValue.CalculateDeltaReference(
            isReferentialMagneticRing, referentialMagneticRingAbsoluteDepth, absoluteDepth);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When both depths are zero, then returns zero")]
    public void WhenBothDepthsAreZero_ThenReturnsZero()
    {
        var isReferentialMagneticRing = false;
        var referentialMagneticRingAbsoluteDepth = 0m;
        var absoluteDepth = 0m;

        var result = Domain.Entities.ReadingValue.CalculateDeltaReference(
            isReferentialMagneticRing, referentialMagneticRingAbsoluteDepth, absoluteDepth);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When referential depth is zero and absolute depth is positive, then returns negative delta")]
    public void WhenReferentialDepthIsZeroAndAbsoluteDepthIsPositive_ThenReturnsNegativeDelta()
    {
        var isReferentialMagneticRing = false;
        var referentialMagneticRingAbsoluteDepth = 0m;
        var absoluteDepth = 3000m; // 3000 millimeters
        var expectedResult = -3m; // (0 - 3000) / 1000 = -3 meters

        var result = Domain.Entities.ReadingValue.CalculateDeltaReference(
            isReferentialMagneticRing, referentialMagneticRingAbsoluteDepth, absoluteDepth);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When absolute depth is zero and referential depth is positive, then returns positive delta")]
    public void WhenAbsoluteDepthIsZeroAndReferentialDepthIsPositive_ThenReturnsPositiveDelta()
    {
        var isReferentialMagneticRing = false;
        var referentialMagneticRingAbsoluteDepth = 3000m; // 3000 millimeters
        var absoluteDepth = 0m;
        var expectedResult = 3m; // (3000 - 0) / 1000 = 3 meters

        var result = Domain.Entities.ReadingValue.CalculateDeltaReference(
            isReferentialMagneticRing, referentialMagneticRingAbsoluteDepth, absoluteDepth);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When both depths are negative, then calculates correctly")]
    public void WhenBothDepthsAreNegative_ThenCalculatesCorrectly()
    {
        var isReferentialMagneticRing = false;
        var referentialMagneticRingAbsoluteDepth = -2000m; // -2000 millimeters
        var absoluteDepth = -5000m; // -5000 millimeters
        var expectedResult = 3m; // (-2000 - (-5000)) / 1000 = 3000 / 1000 = 3 meters

        var result = Domain.Entities.ReadingValue.CalculateDeltaReference(
            isReferentialMagneticRing, referentialMagneticRingAbsoluteDepth, absoluteDepth);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When referential depth is negative and absolute depth is positive, then returns negative delta")]
    public void WhenReferentialDepthIsNegativeAndAbsoluteDepthIsPositive_ThenReturnsNegativeDelta()
    {
        var isReferentialMagneticRing = false;
        var referentialMagneticRingAbsoluteDepth = -2000m; // -2000 millimeters
        var absoluteDepth = 3000m; // 3000 millimeters
        var expectedResult = -5m; // (-2000 - 3000) / 1000 = -5000 / 1000 = -5 meters

        var result = Domain.Entities.ReadingValue.CalculateDeltaReference(
            isReferentialMagneticRing, referentialMagneticRingAbsoluteDepth, absoluteDepth);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When referential depth is positive and absolute depth is negative, then returns positive delta")]
    public void WhenReferentialDepthIsPositiveAndAbsoluteDepthIsNegative_ThenReturnsPositiveDelta()
    {
        var isReferentialMagneticRing = false;
        var referentialMagneticRingAbsoluteDepth = 3000m; // 3000 millimeters
        var absoluteDepth = -2000m; // -2000 millimeters
        var expectedResult = 5m; // (3000 - (-2000)) / 1000 = 5000 / 1000 = 5 meters

        var result = Domain.Entities.ReadingValue.CalculateDeltaReference(
            isReferentialMagneticRing, referentialMagneticRingAbsoluteDepth, absoluteDepth);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using decimal precision, then calculates accurately")]
    public void WhenUsingDecimalPrecision_ThenCalculatesAccurately()
    {
        var isReferentialMagneticRing = false;
        var referentialMagneticRingAbsoluteDepth = 1234.567m; // 1234.567 millimeters
        var absoluteDepth = 987.654m; // 987.654 millimeters
        var expectedResult = 0.246913m; // (1234.567 - 987.654) / 1000 = 246.913 / 1000 = 0.246913 meters

        var result = Domain.Entities.ReadingValue.CalculateDeltaReference(
            isReferentialMagneticRing, referentialMagneticRingAbsoluteDepth, absoluteDepth);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using very large values, then calculates correctly")]
    public void WhenUsingVeryLargeValues_ThenCalculatesCorrectly()
    {
        var isReferentialMagneticRing = false;
        var referentialMagneticRingAbsoluteDepth = 100000m; // 100000 millimeters = 100 meters
        var absoluteDepth = 50000m; // 50000 millimeters = 50 meters
        var expectedResult = 50m; // (100000 - 50000) / 1000 = 50000 / 1000 = 50 meters

        var result = Domain.Entities.ReadingValue.CalculateDeltaReference(
            isReferentialMagneticRing, referentialMagneticRingAbsoluteDepth, absoluteDepth);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using very small values, then calculates correctly")]
    public void WhenUsingVerySmallValues_ThenCalculatesCorrectly()
    {
        var isReferentialMagneticRing = false;
        var referentialMagneticRingAbsoluteDepth = 2m; // 2 millimeters
        var absoluteDepth = 1m; // 1 millimeter
        var expectedResult = 0.001m; // (2 - 1) / 1000 = 1 / 1000 = 0.001 meters

        var result = Domain.Entities.ReadingValue.CalculateDeltaReference(
            isReferentialMagneticRing, referentialMagneticRingAbsoluteDepth, absoluteDepth);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When difference is 1000 millimeters, then returns 1 meter")]
    public void WhenDifferenceIs1000Millimeters_ThenReturns1Meter()
    {
        var isReferentialMagneticRing = false;
        var referentialMagneticRingAbsoluteDepth = 6000m; // 6000 millimeters
        var absoluteDepth = 5000m; // 5000 millimeters
        var expectedResult = 1m; // (6000 - 5000) / 1000 = 1 meter

        var result = Domain.Entities.ReadingValue.CalculateDeltaReference(
            isReferentialMagneticRing, referentialMagneticRingAbsoluteDepth, absoluteDepth);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using random values, then formula is consistent")]
    public void WhenUsingRandomValues_ThenFormulaIsConsistent()
    {
        var isReferentialMagneticRing = false;
        var referentialMagneticRingAbsoluteDepth = Faker.Random.Decimal(-10000, 10000);
        var absoluteDepth = Faker.Random.Decimal(-10000, 10000);
        var expectedResult = (referentialMagneticRingAbsoluteDepth - absoluteDepth) / MetersToMillimetersMultiplier;

        var result = Domain.Entities.ReadingValue.CalculateDeltaReference(
            isReferentialMagneticRing, referentialMagneticRingAbsoluteDepth, absoluteDepth);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When swapping depth parameters, then result sign changes")]
    public void WhenSwappingDepthParameters_ThenResultSignChanges()
    {
        var isReferentialMagneticRing = false;
        var depth1 = 6000m;
        var depth2 = 4000m;

        var result1 = Domain.Entities.ReadingValue.CalculateDeltaReference(
            isReferentialMagneticRing, depth1, depth2);
        var result2 = Domain.Entities.ReadingValue.CalculateDeltaReference(
            isReferentialMagneticRing, depth2, depth1);

        result1.Should().Be(-result2);
    }
}
