using Bogus;
using Domain.Entities;

namespace Domain.Tests.Entities.ReadingValue;

[Trait("ReadingValue", "CalculateQuotaAndPressure")]
public class CalculateQuotaAndPressureTests
{
    private static readonly Faker Faker = new();
    private const decimal Gravity = 9.81M;

    [Fact(DisplayName = "When depth is zero, then quota equals top quota")]
    public void WhenDepthIsZero_ThenQuotaEqualsTopQuota()
    {
        var depth = 0m;
        var topQuota = 100m;
        var baseQuota = 50m;

        var (quota, pressure) = Domain.Entities.ReadingValue.CalculateQuotaAndPressure(
            depth, topQuota, baseQuota);

        quota.Should().Be(100m);
    }

    [Fact(DisplayName = "When depth is positive, then quota is below top quota")]
    public void WhenDepthIsPositive_ThenQuotaIsBelowTopQuota()
    {
        var depth = 10m;
        var topQuota = 100m;
        var baseQuota = 50m;
        var expectedQuota = 90m; // topQuota - depth = 100 - 10 = 90

        var (quota, pressure) = Domain.Entities.ReadingValue.CalculateQuotaAndPressure(
            depth, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
    }

    [Fact(DisplayName = "When depth is negative, then quota is above top quota")]
    public void WhenDepthIsNegative_ThenQuotaIsAboveTopQuota()
    {
        var depth = -10m;
        var topQuota = 100m;
        var baseQuota = 50m;
        var expectedQuota = 110m; // topQuota - depth = 100 - (-10) = 110

        var (quota, pressure) = Domain.Entities.ReadingValue.CalculateQuotaAndPressure(
            depth, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
    }

    [Fact(DisplayName = "When calculated quota equals base quota, then pressure is zero")]
    public void WhenCalculatedQuotaEqualsBaseQuota_ThenPressureIsZero()
    {
        var depth = 50m; // This will make quota = 100 - 50 = 50 (same as baseQuota)
        var topQuota = 100m;
        var baseQuota = 50m;

        var (quota, pressure) = Domain.Entities.ReadingValue.CalculateQuotaAndPressure(
            depth, topQuota, baseQuota);

        quota.Should().Be(50m);
        pressure.Should().Be(0m);
    }

    [Fact(DisplayName = "When calculated quota is above base quota, then pressure is positive")]
    public void WhenCalculatedQuotaIsAboveBaseQuota_ThenPressureIsPositive()
    {
        var depth = 30m; // This will make quota = 100 - 30 = 70
        var topQuota = 100m;
        var baseQuota = 50m;
        var expectedQuota = 70m;
        var expectedPressure = 196.2m; // (quota - baseQuota) * gravity = (70 - 50) * 9.81 = 196.2

        var (quota, pressure) = Domain.Entities.ReadingValue.CalculateQuotaAndPressure(
            depth, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
        pressure.Should().Be(expectedPressure);
    }

    [Fact(DisplayName = "When calculated quota is below base quota, then pressure is negative")]
    public void WhenCalculatedQuotaIsBelowBaseQuota_ThenPressureIsNegative()
    {
        var depth = 60m; // This will make quota = 100 - 60 = 40
        var topQuota = 100m;
        var baseQuota = 50m;
        var expectedQuota = 40m;
        var expectedPressure = -98.1m; // (quota - baseQuota) * gravity = (40 - 50) * 9.81 = -98.1

        var (quota, pressure) = Domain.Entities.ReadingValue.CalculateQuotaAndPressure(
            depth, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
        pressure.Should().Be(expectedPressure);
    }

    [Fact(DisplayName = "When depth equals top quota minus base quota, then quota equals base quota")]
    public void WhenDepthEqualsTopQuotaMinusBaseQuota_ThenQuotaEqualsBaseQuota()
    {
        var topQuota = 100m;
        var baseQuota = 30m;
        var depth = 70m; // topQuota - baseQuota = 100 - 30 = 70
        var expectedQuota = 30m; // topQuota - depth = 100 - 70 = 30

        var (quota, pressure) = Domain.Entities.ReadingValue.CalculateQuotaAndPressure(
            depth, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
        quota.Should().Be(baseQuota);
        pressure.Should().Be(0m);
    }

    [Fact(DisplayName = "When using negative quotas, then calculates correctly")]
    public void WhenUsingNegativeQuotas_ThenCalculatesCorrectly()
    {
        var depth = 5m;
        var topQuota = -10m;
        var baseQuota = -20m;
        var expectedQuota = -15m; // topQuota - depth = -10 - 5 = -15
        var expectedPressure = 49.05m; // (quota - baseQuota) * gravity = (-15 - (-20)) * 9.81 = 49.05

        var (quota, pressure) = Domain.Entities.ReadingValue.CalculateQuotaAndPressure(
            depth, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
        pressure.Should().Be(expectedPressure);
    }

    [Fact(DisplayName = "When using decimal precision, then calculates accurately")]
    public void WhenUsingDecimalPrecision_ThenCalculatesAccurately()
    {
        var depth = 10.123456m;
        var topQuota = 100.654321m;
        var baseQuota = 50.987654m;
        var expectedQuota = 90.530865m; // topQuota - depth = 100.654321 - 10.123456 = 90.530865
        var expectedPressure = 387.91889991m; // (quota - baseQuota) * gravity = (90.530865 - 50.987654) * 9.81 = 387.91889991

        var (quota, pressure) = Domain.Entities.ReadingValue.CalculateQuotaAndPressure(
            depth, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
        pressure.Should().Be(expectedPressure);
    }

    [Fact(DisplayName = "When top quota is less than base quota, then calculates correctly")]
    public void WhenTopQuotaIsLessThanBaseQuota_ThenCalculatesCorrectly()
    {
        var depth = 10m;
        var topQuota = 50m;
        var baseQuota = 100m;
        var expectedQuota = 40m; // topQuota - depth = 50 - 10 = 40
        var expectedPressure = -588.6m; // (quota - baseQuota) * gravity = (40 - 100) * 9.81 = -588.6

        var (quota, pressure) = Domain.Entities.ReadingValue.CalculateQuotaAndPressure(
            depth, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
        pressure.Should().Be(expectedPressure);
    }

    [Fact(DisplayName = "When using large values, then calculates correctly")]
    public void WhenUsingLargeValues_ThenCalculatesCorrectly()
    {
        var depth = 500m;
        var topQuota = 1500m;
        var baseQuota = 500m;
        var expectedQuota = 1000m; // topQuota - depth = 1500 - 500 = 1000
        var expectedPressure = 4905m; // (quota - baseQuota) * gravity = (1000 - 500) * 9.81 = 4905

        var (quota, pressure) = Domain.Entities.ReadingValue.CalculateQuotaAndPressure(
            depth, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
        pressure.Should().Be(expectedPressure);
    }

    [Fact(DisplayName = "When using very small values, then calculates correctly")]
    public void WhenUsingVerySmallValues_ThenCalculatesCorrectly()
    {
        var depth = 0.001m;
        var topQuota = 0.002m;
        var baseQuota = 0.0005m;
        var expectedQuota = 0.001m; // topQuota - depth = 0.002 - 0.001 = 0.001
        var expectedPressure = 0.004905m; // (quota - baseQuota) * gravity = (0.001 - 0.0005) * 9.81 = 0.004905

        var (quota, pressure) = Domain.Entities.ReadingValue.CalculateQuotaAndPressure(
            depth, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
        pressure.Should().Be(expectedPressure);
    }
}
