using Bogus;
using Domain.Entities;

namespace Domain.Tests.Entities.ReadingValue;

[Trait("ReadingValue", "CalculateZDisplacement")]
public class CalculateZDisplacementTests
{
    private static readonly Faker Faker = new();

    [Fact(DisplayName = "When is referential, then returns zero")]
    public void WhenIsReferential_ThenReturnsZero()
    {
        var isReferential = true;
        var readingQuota = Faker.Random.Decimal(0, 1000);
        var referentialQuota = Faker.Random.Decimal(0, 1000);

        var result = Domain.Entities.ReadingValue.CalculateZDisplacement(
            isReferential, readingQuota, referentialQuota);

        result.Should().Be(0);
    }

    [Fact(DisplayName = "When referential quota is null, then returns zero")]
    public void WhenReferentialQuotaIsNull_ThenReturnsZero()
    {
        var isReferential = false;
        var readingQuota = Faker.Random.Decimal(0, 1000);
        decimal? referentialQuota = null;

        var result = Domain.Entities.ReadingValue.CalculateZDisplacement(
            isReferential, readingQuota, referentialQuota);

        result.Should().Be(0);
    }

    [Fact(DisplayName = "When both quotas are equal, then returns zero")]
    public void WhenBothQuotasAreEqual_ThenReturnsZero()
    {
        var isReferential = false;
        var quota = 100.1234567890m;
        var referentialQuota = 100.1234567890m;

        var result = Domain.Entities.ReadingValue.CalculateZDisplacement(
            isReferential, quota, referentialQuota);

        result.Should().Be(0);
    }

    [Fact(DisplayName = "When reading quota is greater than referential, then returns positive displacement")]
    public void WhenReadingQuotaIsGreaterThanReferential_ThenReturnsPositiveDisplacement()
    {
        var isReferential = false;
        var readingQuota = 101.1234567890m;
        var referentialQuota = 100.1234567890m;
        var expectedDisplacement = 1000m; // 1 meter * 1000 = 1000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateZDisplacement(
            isReferential, readingQuota, referentialQuota);

        result.Should().Be(expectedDisplacement);
    }

    [Fact(DisplayName = "When reading quota is less than referential, then returns negative displacement")]
    public void WhenReadingQuotaIsLessThanReferential_ThenReturnsNegativeDisplacement()
    {
        var isReferential = false;
        var readingQuota = 100.1234567890m;
        var referentialQuota = 101.1234567890m;
        var expectedDisplacement = -1000m; // -1 meter * 1000 = -1000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateZDisplacement(
            isReferential, readingQuota, referentialQuota);

        result.Should().Be(expectedDisplacement);
    }

    [Fact(DisplayName = "When quotas have precision beyond 10 decimal places, then rounds to 10 decimal places")]
    public void WhenQuotasHavePrecisionBeyond10DecimalPlaces_ThenRoundsTo10DecimalPlaces()
    {
        var isReferential = false;
        var readingQuota = 100.12345678901m; // 11 decimal places
        var referentialQuota = 100.12345678891m; // 11 decimal places
        var expectedDisplacement = 0.0000001000m; // Actual result after rounding to 10 decimal places

        var result = Domain.Entities.ReadingValue.CalculateZDisplacement(
            isReferential, readingQuota, referentialQuota);

        result.Should().Be(expectedDisplacement);
    }

    [Fact(DisplayName = "When displacement is very small, then returns correct millimeter value")]
    public void WhenDisplacementIsVerySmall_ThenReturnsCorrectMillimeterValue()
    {
        var isReferential = false;
        var readingQuota = 100.0000000001m;
        var referentialQuota = 100.0000000000m;
        var expectedDisplacement = 0.0000001000m; // Actual result after rounding to 10 decimal places

        var result = Domain.Entities.ReadingValue.CalculateZDisplacement(
            isReferential, readingQuota, referentialQuota);

        result.Should().Be(expectedDisplacement);
    }

    [Fact(DisplayName = "When displacement is large, then returns correct millimeter value")]
    public void WhenDisplacementIsLarge_ThenReturnsCorrectMillimeterValue()
    {
        var isReferential = false;
        var readingQuota = 110.1234567890m;
        var referentialQuota = 100.1234567890m;
        var expectedDisplacement = 10000m; // 10 meters * 1000 = 10000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateZDisplacement(
            isReferential, readingQuota, referentialQuota);

        result.Should().Be(expectedDisplacement);
    }

    [Fact(DisplayName = "When quotas are negative, then calculates correctly")]
    public void WhenQuotasAreNegative_ThenCalculatesCorrectly()
    {
        var isReferential = false;
        var readingQuota = -99.1234567890m;
        var referentialQuota = -100.1234567890m;
        var expectedDisplacement = 1000m; // 1 meter * 1000 = 1000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateZDisplacement(
            isReferential, readingQuota, referentialQuota);

        result.Should().Be(expectedDisplacement);
    }

    [Fact(DisplayName = "When quotas have different signs, then calculates correctly")]
    public void WhenQuotasHaveDifferentSigns_ThenCalculatesCorrectly()
    {
        var isReferential = false;
        var readingQuota = 1.0000000000m;
        var referentialQuota = -1.0000000000m;
        var expectedDisplacement = 2000m; // 2 meters * 1000 = 2000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateZDisplacement(
            isReferential, readingQuota, referentialQuota);

        result.Should().Be(expectedDisplacement);
    }
}
