using Bogus;
using Domain.Entities;

namespace Domain.Tests.Entities.ReadingValue;

[Trait("ReadingValue", "CalculateQuotaAndDepth")]
public class CalculateQuotaAndDepthTests
{
    private static readonly Faker Faker = new();
    private const decimal Gravity = 9.81M;

    [Fact(DisplayName = "When pressure is zero, then quota equals base quota")]
    public void WhenPressureIsZero_ThenQuotaEqualsBaseQuota()
    {
        var pressure = 0m;
        var topQuota = 100m;
        var baseQuota = 50m;

        var (quota, depth) = Domain.Entities.ReadingValue.CalculateQuotaAndDepth(
            pressure, topQuota, baseQuota);

        quota.Should().Be(50m);
    }

    [Fact(DisplayName = "When pressure is positive, then quota is above base quota")]
    public void WhenPressureIsPositive_ThenQuotaIsAboveBaseQuota()
    {
        var pressure = 98.1m; // This represents 10m of water column
        var topQuota = 100m;
        var baseQuota = 50m;
        var expectedQuota = 60m; // baseQuota + (pressure / gravity) = 50 + (98.1 / 9.81) = 60

        var (quota, depth) = Domain.Entities.ReadingValue.CalculateQuotaAndDepth(
            pressure, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
    }

    [Fact(DisplayName = "When pressure is negative, then quota is below base quota")]
    public void WhenPressureIsNegative_ThenQuotaIsBelowBaseQuota()
    {
        var pressure = -98.1m; // This represents -10m of water column
        var topQuota = 100m;
        var baseQuota = 50m;
        var expectedQuota = 40m; // baseQuota + (pressure / gravity) = 50 + (-98.1 / 9.81) = 40

        var (quota, depth) = Domain.Entities.ReadingValue.CalculateQuotaAndDepth(
            pressure, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
    }

    [Fact(DisplayName = "When calculated quota equals top quota, then depth is zero")]
    public void WhenCalculatedQuotaEqualsTopQuota_ThenDepthIsZero()
    {
        var topQuota = 100m;
        var baseQuota = 50m;
        var pressure = 490.5m; // This will make quota = 50 + (490.5 / 9.81) = 100
        var expectedQuota = 100m;

        var (quota, depth) = Domain.Entities.ReadingValue.CalculateQuotaAndDepth(
            pressure, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
        depth.Should().Be(0m);
    }

    [Fact(DisplayName = "When calculated quota is below top quota, then depth is positive")]
    public void WhenCalculatedQuotaIsBelowTopQuota_ThenDepthIsPositive()
    {
        var pressure = 196.2m; // This will make quota = 50 + (196.2 / 9.81) = 70
        var topQuota = 100m;
        var baseQuota = 50m;
        var expectedQuota = 70m;
        var expectedDepth = 30m; // topQuota - quota = 100 - 70 = 30

        var (quota, depth) = Domain.Entities.ReadingValue.CalculateQuotaAndDepth(
            pressure, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
        depth.Should().Be(expectedDepth);
    }

    [Fact(DisplayName = "When calculated quota is above top quota, then depth is negative")]
    public void WhenCalculatedQuotaIsAboveTopQuota_ThenDepthIsNegative()
    {
        var pressure = 588.6m; // This will make quota = 50 + (588.6 / 9.81) = 110
        var topQuota = 100m;
        var baseQuota = 50m;
        var expectedQuota = 110m;
        var expectedDepth = -10m; // topQuota - quota = 100 - 110 = -10

        var (quota, depth) = Domain.Entities.ReadingValue.CalculateQuotaAndDepth(
            pressure, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
        depth.Should().Be(expectedDepth);
    }

    [Fact(DisplayName = "When using negative base quota, then calculates correctly")]
    public void WhenUsingNegativeBaseQuota_ThenCalculatesCorrectly()
    {
        var pressure = 98.1m;
        var topQuota = 10m;
        var baseQuota = -20m;
        var expectedQuota = -10m; // baseQuota + (pressure / gravity) = -20 + (98.1 / 9.81) = -10
        var expectedDepth = 20m; // topQuota - quota = 10 - (-10) = 20

        var (quota, depth) = Domain.Entities.ReadingValue.CalculateQuotaAndDepth(
            pressure, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
        depth.Should().Be(expectedDepth);
    }

    [Fact(DisplayName = "When using decimal precision, then calculates accurately")]
    public void WhenUsingDecimalPrecision_ThenCalculatesAccurately()
    {
        var pressure = 123.456m;
        var topQuota = 100.654321m;
        var baseQuota = 50.987654m;
        var expectedQuota = 63.572363480122324159021406728m; // baseQuota + (pressure / gravity) = 50.987654 + (123.456 / 9.81)
        var expectedDepth = 37.081957519877675840978593272m; // topQuota - quota = 100.654321 - 63.572363480122324159021406728

        var (quota, depth) = Domain.Entities.ReadingValue.CalculateQuotaAndDepth(
            pressure, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
        depth.Should().Be(expectedDepth);
    }

    [Fact(DisplayName = "When top quota is less than base quota, then calculates correctly")]
    public void WhenTopQuotaIsLessThanBaseQuota_ThenCalculatesCorrectly()
    {
        var pressure = 98.1m;
        var topQuota = 50m;
        var baseQuota = 100m;
        var expectedQuota = 110m; // baseQuota + (pressure / gravity) = 100 + (98.1 / 9.81) = 110
        var expectedDepth = -60m; // topQuota - quota = 50 - 110 = -60

        var (quota, depth) = Domain.Entities.ReadingValue.CalculateQuotaAndDepth(
            pressure, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
        depth.Should().Be(expectedDepth);
    }

    [Fact(DisplayName = "When using large pressure values, then calculates correctly")]
    public void WhenUsingLargePressureValues_ThenCalculatesCorrectly()
    {
        var pressure = 9810m; // This represents 1000m of water column
        var topQuota = 1500m;
        var baseQuota = 500m;
        var expectedQuota = 1500m; // baseQuota + (pressure / gravity) = 500 + (9810 / 9.81) = 1500
        var expectedDepth = 0m; // topQuota - quota = 1500 - 1500 = 0

        var (quota, depth) = Domain.Entities.ReadingValue.CalculateQuotaAndDepth(
            pressure, topQuota, baseQuota);

        quota.Should().Be(expectedQuota);
        depth.Should().Be(expectedDepth);
    }

    [Fact(DisplayName = "When using very small pressure values, then calculates correctly")]
    public void WhenUsingVerySmallPressureValues_ThenCalculatesCorrectly()
    {
        var pressure = 0.009810m; // This represents 0.001m of water column
        var topQuota = 0.002m;
        var baseQuota = 0.0005m;
        var expectedQuota = 0.0015m; // baseQuota + (pressure / gravity) = 0.0005 + (0.009810 / 9.81) = 0.0015
        var expectedDepth = 0.0005m; // topQuota - quota = 0.002 - 0.0015 = 0.0005

        var (quota, depth) = Domain.Entities.ReadingValue.CalculateQuotaAndDepth(
            pressure, topQuota, baseQuota);

        quota.Should().BeApproximately(expectedQuota, 0.000001m);
        depth.Should().BeApproximately(expectedDepth, 0.000001m);
    }

    [Fact(DisplayName = "When pressure calculation is inverted, then original values are recovered")]
    public void WhenPressureCalculationIsInverted_ThenOriginalValuesAreRecovered()
    {
        // Test the inverse relationship with CalculateDepthAndPressure
        var originalQuota = 75m;
        var topQuota = 100m;
        var baseQuota = 50m;

        // First calculate depth and pressure from quota
        var (originalDepth, originalPressure) = Domain.Entities.ReadingValue.CalculateDepthAndPressure(
            originalQuota, topQuota, baseQuota);

        // Then calculate quota and depth back from pressure
        var (calculatedQuota, calculatedDepth) = Domain.Entities.ReadingValue.CalculateQuotaAndDepth(
            originalPressure, topQuota, baseQuota);

        calculatedQuota.Should().Be(originalQuota);
        calculatedDepth.Should().Be(originalDepth);
    }
}
