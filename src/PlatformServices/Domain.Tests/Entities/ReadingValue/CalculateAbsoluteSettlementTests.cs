using Bogus;
using Domain.Entities;

namespace Domain.Tests.Entities.ReadingValue;

[Trait("ReadingValue", "CalculateAbsoluteSettlement")]
public class CalculateAbsoluteSettlementTests
{
    private static readonly Faker Faker = new();
    private const int MetersToMillimetersMultiplier = 1000;

    [Fact(DisplayName = "When depth equals difference between top quota and measurement quota in millimeters, then returns zero")]
    public void WhenDepthEqualsDifferenceBetweenTopQuotaAndMeasurementQuotaInMillimeters_ThenReturnsZero()
    {
        var measurementQuota = 0.5m; // 0.5 meters
        var topQuota = 1.0m; // 1.0 meter
        var depth = 500m; // 500 millimeters = 0.5 meters (difference between quotas)

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteSettlement(
            depth, measurementQuota, topQuota);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When depth is less than quota difference, then returns positive settlement")]
    public void WhenDepthIsLessThanQuotaDifference_ThenReturnsPositiveSettlement()
    {
        var measurementQuota = 0.5m; // 0.5 meters
        var topQuota = 1.0m; // 1.0 meter
        var depth = 400m; // 400 millimeters (less than 500mm difference)
        var expectedResult = 100m; // (1.0 - 0.5) * 1000 - 400 = 500 - 400 = 100 millimeters

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteSettlement(
            depth, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When depth is greater than quota difference, then returns negative settlement")]
    public void WhenDepthIsGreaterThanQuotaDifference_ThenReturnsNegativeSettlement()
    {
        var measurementQuota = 0.5m; // 0.5 meters
        var topQuota = 1.0m; // 1.0 meter
        var depth = 600m; // 600 millimeters (greater than 500mm difference)
        var expectedResult = -100m; // (1.0 - 0.5) * 1000 - 600 = 500 - 600 = -100 millimeters

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteSettlement(
            depth, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When measurement quota equals top quota, then returns negative depth")]
    public void WhenMeasurementQuotaEqualsTopQuota_ThenReturnsNegativeDepth()
    {
        var measurementQuota = 1.0m; // 1.0 meter
        var topQuota = 1.0m; // 1.0 meter
        var depth = 100m; // 100 millimeters
        var expectedResult = -100m; // (1.0 - 1.0) * 1000 - 100 = 0 - 100 = -100 millimeters

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteSettlement(
            depth, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When depth is zero, then returns quota difference in millimeters")]
    public void WhenDepthIsZero_ThenReturnsQuotaDifferenceInMillimeters()
    {
        var measurementQuota = 0.3m; // 0.3 meters
        var topQuota = 0.8m; // 0.8 meters
        var depth = 0m;
        var expectedResult = 500m; // (0.8 - 0.3) * 1000 = 0.5 * 1000 = 500 millimeters

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteSettlement(
            depth, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When measurement quota is greater than top quota, then calculates correctly")]
    public void WhenMeasurementQuotaIsGreaterThanTopQuota_ThenCalculatesCorrectly()
    {
        var measurementQuota = 1.5m; // 1.5 meters
        var topQuota = 1.0m; // 1.0 meter
        var depth = 200m; // 200 millimeters
        var expectedResult = -700m; // (1.0 - 1.5) * 1000 - 200 = -500 - 200 = -700 millimeters

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteSettlement(
            depth, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using negative quotas, then calculates correctly")]
    public void WhenUsingNegativeQuotas_ThenCalculatesCorrectly()
    {
        var measurementQuota = -0.2m; // -0.2 meters
        var topQuota = -0.1m; // -0.1 meter
        var depth = 50m; // 50 millimeters
        var expectedResult = 50m; // (-0.1 - (-0.2)) * 1000 - 50 = 0.1 * 1000 - 50 = 100 - 50 = 50

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteSettlement(
            depth, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When top quota is negative and measurement quota is positive, then calculates correctly")]
    public void WhenTopQuotaIsNegativeAndMeasurementQuotaIsPositive_ThenCalculatesCorrectly()
    {
        var measurementQuota = 0.1m; // 0.1 meter
        var topQuota = -0.1m; // -0.1 meter
        var depth = 100m; // 100 millimeters
        var expectedResult = -300m; // (-0.1 - 0.1) * 1000 - 100 = -0.2 * 1000 - 100 = -200 - 100 = -300

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteSettlement(
            depth, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using decimal precision, then calculates accurately")]
    public void WhenUsingDecimalPrecision_ThenCalculatesAccurately()
    {
        var measurementQuota = 0.123456m; // 0.123456 meters
        var topQuota = 0.654321m; // 0.654321 meters
        var depth = 123.456m; // 123.456 millimeters
        var expectedResult = 407.409m; // (0.654321 - 0.123456) * 1000 - 123.456 = 530.865 - 123.456 = 407.409

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteSettlement(
            depth, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using very large values, then calculates correctly")]
    public void WhenUsingVeryLargeValues_ThenCalculatesCorrectly()
    {
        var measurementQuota = 10m; // 10 meters
        var topQuota = 15m; // 15 meters
        var depth = 2000m; // 2000 millimeters
        var expectedResult = 3000m; // (15 - 10) * 1000 - 2000 = 5000 - 2000 = 3000

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteSettlement(
            depth, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using very small values, then calculates correctly")]
    public void WhenUsingVerySmallValues_ThenCalculatesCorrectly()
    {
        var measurementQuota = 0.0001m; // 0.0001 meters
        var topQuota = 0.0002m; // 0.0002 meters
        var depth = 0.05m; // 0.05 millimeters
        var expectedResult = 0.05m; // (0.0002 - 0.0001) * 1000 - 0.05 = 0.1 - 0.05 = 0.05

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteSettlement(
            depth, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When depth is negative, then adds to settlement")]
    public void WhenDepthIsNegative_ThenAddsToSettlement()
    {
        var measurementQuota = 0.5m; // 0.5 meters
        var topQuota = 1.0m; // 1.0 meter
        var depth = -100m; // -100 millimeters
        var expectedResult = 600m; // (1.0 - 0.5) * 1000 - (-100) = 500 + 100 = 600

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteSettlement(
            depth, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using random values, then formula is consistent")]
    public void WhenUsingRandomValues_ThenFormulaIsConsistent()
    {
        var depth = Faker.Random.Decimal(-1000, 1000);
        var measurementQuota = Faker.Random.Decimal(-10, 10);
        var topQuota = Faker.Random.Decimal(-10, 10);
        var expectedResult = (topQuota * MetersToMillimetersMultiplier) - 
                           (measurementQuota * MetersToMillimetersMultiplier) - 
                           depth;

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteSettlement(
            depth, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When comparing with CalculateAbsoluteDepth, then they are inverse operations")]
    public void WhenComparingWithCalculateAbsoluteDepth_ThenTheyAreInverseOperations()
    {
        var measurementQuota = 0.75m;
        var topQuota = 1.25m;
        var originalDepth = 300m;

        // Calculate settlement from depth
        var settlement = Domain.Entities.ReadingValue.CalculateAbsoluteSettlement(
            originalDepth, measurementQuota, topQuota);

        // Calculate depth back from settlement
        var calculatedDepth = Domain.Entities.ReadingValue.CalculateAbsoluteDepth(
            settlement, measurementQuota, topQuota);

        calculatedDepth.Should().Be(originalDepth);
    }

    [Fact(DisplayName = "When settlement is positive, then actual depth is less than theoretical depth")]
    public void WhenSettlementIsPositive_ThenActualDepthIsLessThanTheoreticalDepth()
    {
        var measurementQuota = 0.5m;
        var topQuota = 1.0m;
        var actualDepth = 400m; // Less than theoretical 500mm
        
        var settlement = Domain.Entities.ReadingValue.CalculateAbsoluteSettlement(
            actualDepth, measurementQuota, topQuota);

        settlement.Should().BePositive();
        settlement.Should().Be(100m); // 500 - 400 = 100mm settlement
    }
}
