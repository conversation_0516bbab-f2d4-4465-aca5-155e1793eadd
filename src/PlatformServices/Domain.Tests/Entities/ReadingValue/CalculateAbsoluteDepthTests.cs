using Bogus;
using Domain.Entities;

namespace Domain.Tests.Entities.ReadingValue;

[Trait("ReadingValue", "CalculateAbsoluteDepth")]
public class CalculateAbsoluteDepthTests
{
    private static readonly Faker Faker = new();
    private const int MetersToMillimetersMultiplier = 1000;

    [Fact(DisplayName = "When absolute settlement is zero, then returns difference between top quota and measurement quota in millimeters")]
    public void WhenAbsoluteSettlementIsZero_ThenReturnsDifferenceBetweenTopQuotaAndMeasurementQuotaInMillimeters()
    {
        var absoluteSettlement = 0m;
        var measurementQuota = 0.5m; // 0.5 meters
        var topQuota = 1.0m; // 1.0 meter
        var expectedResult = 500m; // (1.0 - 0.5) * 1000 = 500 millimeters

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteDepth(
            absoluteSettlement, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When measurement quota equals top quota and settlement is zero, then returns zero")]
    public void WhenMeasurementQuotaEqualsTopQuotaAndSettlementIsZero_ThenReturnsZero()
    {
        var absoluteSettlement = 0m;
        var measurementQuota = 1.0m;
        var topQuota = 1.0m;

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteDepth(
            absoluteSettlement, measurementQuota, topQuota);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When absolute settlement is positive, then subtracts from depth")]
    public void WhenAbsoluteSettlementIsPositive_ThenSubtractsFromDepth()
    {
        var absoluteSettlement = 100m; // 100 millimeters
        var measurementQuota = 0.5m; // 0.5 meters
        var topQuota = 1.0m; // 1.0 meter
        var expectedResult = 400m; // (1.0 - 0.5) * 1000 - 100 = 500 - 100 = 400 millimeters

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteDepth(
            absoluteSettlement, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When absolute settlement is negative, then adds to depth")]
    public void WhenAbsoluteSettlementIsNegative_ThenAddsToDepth()
    {
        var absoluteSettlement = -100m; // -100 millimeters
        var measurementQuota = 0.5m; // 0.5 meters
        var topQuota = 1.0m; // 1.0 meter
        var expectedResult = 600m; // (1.0 - 0.5) * 1000 - (-100) = 500 + 100 = 600 millimeters

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteDepth(
            absoluteSettlement, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When measurement quota is greater than top quota, then returns negative depth")]
    public void WhenMeasurementQuotaIsGreaterThanTopQuota_ThenReturnsNegativeDepth()
    {
        var absoluteSettlement = 0m;
        var measurementQuota = 1.5m; // 1.5 meters
        var topQuota = 1.0m; // 1.0 meter
        var expectedResult = -500m; // (1.0 - 1.5) * 1000 = -500 millimeters

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteDepth(
            absoluteSettlement, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using decimal precision, then calculates accurately")]
    public void WhenUsingDecimalPrecision_ThenCalculatesAccurately()
    {
        var absoluteSettlement = 123.456m;
        var measurementQuota = 0.123456m;
        var topQuota = 0.654321m;
        var expectedResult = 407.409m; // (0.654321 - 0.123456) * 1000 - 123.456 = 530.865 - 123.456 = 407.409

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteDepth(
            absoluteSettlement, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When quotas are negative, then calculates correctly")]
    public void WhenQuotasAreNegative_ThenCalculatesCorrectly()
    {
        var absoluteSettlement = 50m;
        var measurementQuota = -0.2m; // -0.2 meters
        var topQuota = -0.1m; // -0.1 meter
        var expectedResult = 50m; // (-0.1 - (-0.2)) * 1000 - 50 = 0.1 * 1000 - 50 = 100 - 50 = 50

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteDepth(
            absoluteSettlement, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When top quota is negative and measurement quota is positive, then calculates correctly")]
    public void WhenTopQuotaIsNegativeAndMeasurementQuotaIsPositive_ThenCalculatesCorrectly()
    {
        var absoluteSettlement = 0m;
        var measurementQuota = 0.1m; // 0.1 meter
        var topQuota = -0.1m; // -0.1 meter
        var expectedResult = -200m; // (-0.1 - 0.1) * 1000 = -0.2 * 1000 = -200 millimeters

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteDepth(
            absoluteSettlement, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using very large values, then calculates correctly")]
    public void WhenUsingVeryLargeValues_ThenCalculatesCorrectly()
    {
        var absoluteSettlement = 5000m; // 5000 millimeters
        var measurementQuota = 10m; // 10 meters
        var topQuota = 15m; // 15 meters
        var expectedResult = 0m; // (15 - 10) * 1000 - 5000 = 5000 - 5000 = 0

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteDepth(
            absoluteSettlement, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using very small values, then calculates correctly")]
    public void WhenUsingVerySmallValues_ThenCalculatesCorrectly()
    {
        var absoluteSettlement = 0.001m; // 0.001 millimeters
        var measurementQuota = 0.0001m; // 0.0001 meters
        var topQuota = 0.0002m; // 0.0002 meters
        var expectedResult = 0.099m; // (0.0002 - 0.0001) * 1000 - 0.001 = 0.1 - 0.001 = 0.099

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteDepth(
            absoluteSettlement, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When absolute settlement equals quota difference in millimeters, then returns zero")]
    public void WhenAbsoluteSettlementEqualsQuotaDifferenceInMillimeters_ThenReturnsZero()
    {
        var measurementQuota = 0.5m; // 0.5 meters
        var topQuota = 1.0m; // 1.0 meter
        var quotaDifferenceInMm = (topQuota - measurementQuota) * MetersToMillimetersMultiplier; // 500mm
        var absoluteSettlement = quotaDifferenceInMm; // 500mm

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteDepth(
            absoluteSettlement, measurementQuota, topQuota);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When absolute settlement is greater than quota difference, then returns negative depth")]
    public void WhenAbsoluteSettlementIsGreaterThanQuotaDifference_ThenReturnsNegativeDepth()
    {
        var measurementQuota = 0.5m; // 0.5 meters
        var topQuota = 1.0m; // 1.0 meter
        var quotaDifferenceInMm = (topQuota - measurementQuota) * MetersToMillimetersMultiplier; // 500mm
        var absoluteSettlement = quotaDifferenceInMm + 100m; // 600mm
        var expectedResult = -100m; // 500 - 600 = -100

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteDepth(
            absoluteSettlement, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using random values, then formula is consistent")]
    public void WhenUsingRandomValues_ThenFormulaIsConsistent()
    {
        var absoluteSettlement = Faker.Random.Decimal(-1000, 1000);
        var measurementQuota = Faker.Random.Decimal(-10, 10);
        var topQuota = Faker.Random.Decimal(-10, 10);
        var expectedResult = (topQuota * MetersToMillimetersMultiplier) - 
                           (measurementQuota * MetersToMillimetersMultiplier) - 
                           absoluteSettlement;

        var result = Domain.Entities.ReadingValue.CalculateAbsoluteDepth(
            absoluteSettlement, measurementQuota, topQuota);

        result.Should().Be(expectedResult);
    }
}
