using Bogus;
using Domain.Entities;

namespace Domain.Tests.Entities.ReadingValue;

[Trait("ReadingValue", "CalculateAccumulatedDisplacement")]
public class CalculateAccumulatedDisplacementTests
{
    private static readonly Faker Faker = new();

    [Fact(DisplayName = "When both values are zero, then returns zero")]
    public void WhenBothValuesAreZero_ThenReturnsZero()
    {
        var averageDisplacement = 0m;
        var previouslyAccumulatedDisplacement = 0m;

        var result = Domain.Entities.ReadingValue.CalculateAccumulatedDisplacement(
            averageDisplacement, previouslyAccumulatedDisplacement);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When average displacement is zero, then returns previously accumulated displacement")]
    public void WhenAverageDisplacementIsZero_ThenReturnsPreviouslyAccumulatedDisplacement()
    {
        var averageDisplacement = 0m;
        var previouslyAccumulatedDisplacement = 100m;

        var result = Domain.Entities.ReadingValue.CalculateAccumulatedDisplacement(
            averageDisplacement, previouslyAccumulatedDisplacement);

        result.Should().Be(100m);
    }

    [Fact(DisplayName = "When previously accumulated displacement is zero, then returns average displacement")]
    public void WhenPreviouslyAccumulatedDisplacementIsZero_ThenReturnsAverageDisplacement()
    {
        var averageDisplacement = 50m;
        var previouslyAccumulatedDisplacement = 0m;

        var result = Domain.Entities.ReadingValue.CalculateAccumulatedDisplacement(
            averageDisplacement, previouslyAccumulatedDisplacement);

        result.Should().Be(50m);
    }

    [Fact(DisplayName = "When both values are positive, then returns sum")]
    public void WhenBothValuesArePositive_ThenReturnsSum()
    {
        var averageDisplacement = 25m;
        var previouslyAccumulatedDisplacement = 75m;
        var expectedResult = 100m; // 25 + 75 = 100

        var result = Domain.Entities.ReadingValue.CalculateAccumulatedDisplacement(
            averageDisplacement, previouslyAccumulatedDisplacement);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When both values are negative, then returns sum")]
    public void WhenBothValuesAreNegative_ThenReturnsSum()
    {
        var averageDisplacement = -25m;
        var previouslyAccumulatedDisplacement = -75m;
        var expectedResult = -100m; // -25 + (-75) = -100

        var result = Domain.Entities.ReadingValue.CalculateAccumulatedDisplacement(
            averageDisplacement, previouslyAccumulatedDisplacement);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When average displacement is positive and previously accumulated is negative, then calculates correctly")]
    public void WhenAverageDisplacementIsPositiveAndPreviouslyAccumulatedIsNegative_ThenCalculatesCorrectly()
    {
        var averageDisplacement = 50m;
        var previouslyAccumulatedDisplacement = -30m;
        var expectedResult = 20m; // 50 + (-30) = 20

        var result = Domain.Entities.ReadingValue.CalculateAccumulatedDisplacement(
            averageDisplacement, previouslyAccumulatedDisplacement);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When average displacement is negative and previously accumulated is positive, then calculates correctly")]
    public void WhenAverageDisplacementIsNegativeAndPreviouslyAccumulatedIsPositive_ThenCalculatesCorrectly()
    {
        var averageDisplacement = -30m;
        var previouslyAccumulatedDisplacement = 50m;
        var expectedResult = 20m; // -30 + 50 = 20

        var result = Domain.Entities.ReadingValue.CalculateAccumulatedDisplacement(
            averageDisplacement, previouslyAccumulatedDisplacement);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When values cancel each other out, then returns zero")]
    public void WhenValuesCancelEachOtherOut_ThenReturnsZero()
    {
        var averageDisplacement = 100m;
        var previouslyAccumulatedDisplacement = -100m;

        var result = Domain.Entities.ReadingValue.CalculateAccumulatedDisplacement(
            averageDisplacement, previouslyAccumulatedDisplacement);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When using decimal precision, then calculates accurately")]
    public void WhenUsingDecimalPrecision_ThenCalculatesAccurately()
    {
        var averageDisplacement = 123.456789m;
        var previouslyAccumulatedDisplacement = 98.765432m;
        var expectedResult = 222.222221m; // 123.456789 + 98.765432 = 222.222221

        var result = Domain.Entities.ReadingValue.CalculateAccumulatedDisplacement(
            averageDisplacement, previouslyAccumulatedDisplacement);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using very large values, then calculates correctly")]
    public void WhenUsingVeryLargeValues_ThenCalculatesCorrectly()
    {
        var averageDisplacement = 500000m;
        var previouslyAccumulatedDisplacement = 499000m;
        var expectedResult = 999000m; // 500000 + 499000 = 999000

        var result = Domain.Entities.ReadingValue.CalculateAccumulatedDisplacement(
            averageDisplacement, previouslyAccumulatedDisplacement);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using very small values, then calculates correctly")]
    public void WhenUsingVerySmallValues_ThenCalculatesCorrectly()
    {
        var averageDisplacement = 0.000001m;
        var previouslyAccumulatedDisplacement = 0.000002m;
        var expectedResult = 0.000003m; // 0.000001 + 0.000002 = 0.000003

        var result = Domain.Entities.ReadingValue.CalculateAccumulatedDisplacement(
            averageDisplacement, previouslyAccumulatedDisplacement);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using random values, then formula is consistent")]
    public void WhenUsingRandomValues_ThenFormulaIsConsistent()
    {
        var averageDisplacement = Faker.Random.Decimal(-1000, 1000);
        var previouslyAccumulatedDisplacement = Faker.Random.Decimal(-1000, 1000);
        var expectedResult = averageDisplacement + previouslyAccumulatedDisplacement;

        var result = Domain.Entities.ReadingValue.CalculateAccumulatedDisplacement(
            averageDisplacement, previouslyAccumulatedDisplacement);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When parameters are swapped, then result is the same")]
    public void WhenParametersAreSwapped_ThenResultIsTheSame()
    {
        var value1 = 150m;
        var value2 = 100m;

        var result1 = Domain.Entities.ReadingValue.CalculateAccumulatedDisplacement(value1, value2);
        var result2 = Domain.Entities.ReadingValue.CalculateAccumulatedDisplacement(value2, value1);

        result1.Should().Be(result2);
    }

    [Fact(DisplayName = "When accumulating multiple times, then builds correctly")]
    public void WhenAccumulatingMultipleTimes_ThenBuildsCorrectly()
    {
        var initialAccumulated = 0m;
        var displacement1 = 10m;
        var displacement2 = 20m;
        var displacement3 = -5m;

        var accumulated1 = Domain.Entities.ReadingValue.CalculateAccumulatedDisplacement(
            displacement1, initialAccumulated);
        var accumulated2 = Domain.Entities.ReadingValue.CalculateAccumulatedDisplacement(
            displacement2, accumulated1);
        var accumulated3 = Domain.Entities.ReadingValue.CalculateAccumulatedDisplacement(
            displacement3, accumulated2);

        accumulated1.Should().Be(10m);
        accumulated2.Should().Be(30m);
        accumulated3.Should().Be(25m);
    }

    [Fact(DisplayName = "When building accumulation sequence, then each step adds to previous")]
    public void WhenBuildingAccumulationSequence_ThenEachStepAddsToPrevious()
    {
        var displacements = new[] { 5m, -2m, 8m, -3m, 1m };
        var accumulated = 0m;
        var expectedValues = new[] { 5m, 3m, 11m, 8m, 9m };

        for (int i = 0; i < displacements.Length; i++)
        {
            accumulated = Domain.Entities.ReadingValue.CalculateAccumulatedDisplacement(
                displacements[i], accumulated);
            accumulated.Should().Be(expectedValues[i]);
        }
    }
}
