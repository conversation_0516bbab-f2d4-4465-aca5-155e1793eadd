using Bogus;
using Domain.Entities;

namespace Domain.Tests.Entities.ReadingValue;

[Trait("ReadingValue", "CalculateTotalPlanimetricDisplacement")]
public class CalculateTotalPlanimetricDisplacementTests
{
    private static readonly Faker Faker = new();

    [Fact(DisplayName = "When both displacements are zero, then returns zero")]
    public void WhenBothDisplacementsAreZero_ThenReturnsZero()
    {
        var northDisplacement = 0m;
        var eastDisplacement = 0m;

        var result = Domain.Entities.ReadingValue.CalculateTotalPlanimetricDisplacement(
            northDisplacement, eastDisplacement);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When only north displacement is provided, then returns absolute north value")]
    public void WhenOnlyNorthDisplacementIsProvided_ThenReturnsAbsoluteNorthValue()
    {
        var northDisplacement = 3m;
        var eastDisplacement = 0m;

        var result = Domain.Entities.ReadingValue.CalculateTotalPlanimetricDisplacement(
            northDisplacement, eastDisplacement);

        result.Should().Be(3m);
    }

    [Fact(DisplayName = "When only east displacement is provided, then returns absolute east value")]
    public void WhenOnlyEastDisplacementIsProvided_ThenReturnsAbsoluteEastValue()
    {
        var northDisplacement = 0m;
        var eastDisplacement = 4m;

        var result = Domain.Entities.ReadingValue.CalculateTotalPlanimetricDisplacement(
            northDisplacement, eastDisplacement);

        result.Should().Be(4m);
    }

    [Fact(DisplayName = "When both displacements are positive, then calculates pythagorean distance")]
    public void WhenBothDisplacementsArePositive_ThenCalculatesPythagoreanDistance()
    {
        var northDisplacement = 3m;
        var eastDisplacement = 4m;
        var expectedResult = 5m; // sqrt(3² + 4²) = sqrt(9 + 16) = sqrt(25) = 5

        var result = Domain.Entities.ReadingValue.CalculateTotalPlanimetricDisplacement(
            northDisplacement, eastDisplacement);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When both displacements are negative, then calculates pythagorean distance")]
    public void WhenBothDisplacementsAreNegative_ThenCalculatesPythagoreanDistance()
    {
        var northDisplacement = -3m;
        var eastDisplacement = -4m;
        var expectedResult = 5m; // sqrt((-3)² + (-4)²) = sqrt(9 + 16) = sqrt(25) = 5

        var result = Domain.Entities.ReadingValue.CalculateTotalPlanimetricDisplacement(
            northDisplacement, eastDisplacement);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When displacements have different signs, then calculates pythagorean distance")]
    public void WhenDisplacementsHaveDifferentSigns_ThenCalculatesPythagoreanDistance()
    {
        var northDisplacement = 3m;
        var eastDisplacement = -4m;
        var expectedResult = 5m; // sqrt(3² + (-4)²) = sqrt(9 + 16) = sqrt(25) = 5

        var result = Domain.Entities.ReadingValue.CalculateTotalPlanimetricDisplacement(
            northDisplacement, eastDisplacement);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When result has more than 6 decimal places, then rounds to 6 decimal places")]
    public void WhenResultHasMoreThan6DecimalPlaces_ThenRoundsTo6DecimalPlaces()
    {
        var northDisplacement = 1m;
        var eastDisplacement = 1m;
        var expectedResult = 1.414214m; // sqrt(1² + 1²) = sqrt(2) ≈ 1.4142135... rounded to 6 decimal places

        var result = Domain.Entities.ReadingValue.CalculateTotalPlanimetricDisplacement(
            northDisplacement, eastDisplacement);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When displacements are very small, then calculates correctly")]
    public void WhenDisplacementsAreVerySmall_ThenCalculatesCorrectly()
    {
        var northDisplacement = 0.000001m;
        var eastDisplacement = 0.000001m;
        var expectedResult = 0.000001m; // sqrt((0.000001)² + (0.000001)²) ≈ 0.000001414... rounded to 6 decimal places

        var result = Domain.Entities.ReadingValue.CalculateTotalPlanimetricDisplacement(
            northDisplacement, eastDisplacement);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When displacements are large, then calculates correctly")]
    public void WhenDisplacementsAreLarge_ThenCalculatesCorrectly()
    {
        var northDisplacement = 1000m;
        var eastDisplacement = 1000m;
        var expectedResult = 1414.213562m; // sqrt(1000² + 1000²) = sqrt(2000000) ≈ 1414.213562... rounded to 6 decimal places

        var result = Domain.Entities.ReadingValue.CalculateTotalPlanimetricDisplacement(
            northDisplacement, eastDisplacement);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using random values, then result is always positive")]
    public void WhenUsingRandomValues_ThenResultIsAlwaysPositive()
    {
        var northDisplacement = Faker.Random.Decimal(-1000, 1000);
        var eastDisplacement = Faker.Random.Decimal(-1000, 1000);

        var result = Domain.Entities.ReadingValue.CalculateTotalPlanimetricDisplacement(
            northDisplacement, eastDisplacement);

        result.Should().BeGreaterOrEqualTo(0);
    }
}
