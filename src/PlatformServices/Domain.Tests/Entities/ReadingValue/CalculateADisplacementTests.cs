using Bogus;
using Domain.Entities;

namespace Domain.Tests.Entities.ReadingValue;

[Trait("ReadingValue", "CalculateADisplacement")]
public class CalculateADisplacementTests
{
    private static readonly Faker Faker = new();

    [Fact(DisplayName = "When instrument azimuth is null, then returns null")]
    public void WhenInstrumentAzimuthIsNull_ThenReturnsNull()
    {
        var northDisplacement = Faker.Random.Decimal(-1000, 1000);
        var eastDisplacement = Faker.Random.Decimal(-1000, 1000);
        double? instrumentAzimuth = null;

        var result = Domain.Entities.ReadingValue.CalculateADisplacement(
            northDisplacement, eastDisplacement, instrumentAzimuth);

        result.Should().BeNull();
    }

    [Fact(DisplayName = "When azimuth is zero and only north displacement exists, then returns north displacement")]
    public void WhenAzimuthIsZeroAndOnlyNorthDisplacementExists_ThenReturnsNorthDisplacement()
    {
        var northDisplacement = 100m;
        var eastDisplacement = 0m;
        var instrumentAzimuth = 0.0; // 0 degrees

        var result = Domain.Entities.ReadingValue.CalculateADisplacement(
            northDisplacement, eastDisplacement, instrumentAzimuth);

        result.Should().Be(100m);
    }

    [Fact(DisplayName = "When azimuth is zero and only east displacement exists, then returns zero")]
    public void WhenAzimuthIsZeroAndOnlyEastDisplacementExists_ThenReturnsZero()
    {
        var northDisplacement = 0m;
        var eastDisplacement = 100m;
        var instrumentAzimuth = 0.0; // 0 degrees

        var result = Domain.Entities.ReadingValue.CalculateADisplacement(
            northDisplacement, eastDisplacement, instrumentAzimuth);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When azimuth is 90 degrees and only north displacement exists, then returns zero")]
    public void WhenAzimuthIs90DegreesAndOnlyNorthDisplacementExists_ThenReturnsZero()
    {
        var northDisplacement = 100m;
        var eastDisplacement = 0m;
        var instrumentAzimuth = 90.0; // 90 degrees

        var result = Domain.Entities.ReadingValue.CalculateADisplacement(
            northDisplacement, eastDisplacement, instrumentAzimuth);

        result.Should().BeApproximately(0m, 0.000001m);
    }

    [Fact(DisplayName = "When azimuth is 90 degrees and only east displacement exists, then returns east displacement")]
    public void WhenAzimuthIs90DegreesAndOnlyEastDisplacementExists_ThenReturnsEastDisplacement()
    {
        var northDisplacement = 0m;
        var eastDisplacement = 100m;
        var instrumentAzimuth = 90.0; // 90 degrees

        var result = Domain.Entities.ReadingValue.CalculateADisplacement(
            northDisplacement, eastDisplacement, instrumentAzimuth);

        result.Should().BeApproximately(100m, 0.000001m);
    }

    [Fact(DisplayName = "When azimuth is 45 degrees with equal displacements, then calculates correctly")]
    public void WhenAzimuthIs45DegreesWithEqualDisplacements_ThenCalculatesCorrectly()
    {
        var northDisplacement = 100m;
        var eastDisplacement = 100m;
        var instrumentAzimuth = 45.0; // 45 degrees
        var expectedResult = 141.421356m; // 100 * cos(45°) + 100 * sin(45°) = 100 * √2/2 + 100 * √2/2 = 100√2

        var result = Domain.Entities.ReadingValue.CalculateADisplacement(
            northDisplacement, eastDisplacement, instrumentAzimuth);

        result.Should().BeApproximately(expectedResult, 0.000001m);
    }

    [Fact(DisplayName = "When both displacements are zero, then returns zero")]
    public void WhenBothDisplacementsAreZero_ThenReturnsZero()
    {
        var northDisplacement = 0m;
        var eastDisplacement = 0m;
        var instrumentAzimuth = Faker.Random.Double(0, 2 * Math.PI);

        var result = Domain.Entities.ReadingValue.CalculateADisplacement(
            northDisplacement, eastDisplacement, instrumentAzimuth);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When displacements are negative, then calculates correctly")]
    public void WhenDisplacementsAreNegative_ThenCalculatesCorrectly()
    {
        var northDisplacement = -100m;
        var eastDisplacement = -100m;
        var instrumentAzimuth = 45.0; // 45 degrees
        var expectedResult = -141.421356m; // -100 * cos(45°) + -100 * sin(45°) = -100√2

        var result = Domain.Entities.ReadingValue.CalculateADisplacement(
            northDisplacement, eastDisplacement, instrumentAzimuth);

        result.Should().BeApproximately(expectedResult, 0.000001m);
    }

    [Fact(DisplayName = "When result has more than 6 decimal places, then rounds to 6 decimal places")]
    public void WhenResultHasMoreThan6DecimalPlaces_ThenRoundsTo6DecimalPlaces()
    {
        var northDisplacement = 1m;
        var eastDisplacement = 1m;
        var instrumentAzimuth = 30.0; // 30 degrees
        // cos(30°) ≈ 0.866025, sin(30°) = 0.5
        // result = 1 * 0.866025 + 1 * 0.5 = 1.366025

        var result = Domain.Entities.ReadingValue.CalculateADisplacement(
            northDisplacement, eastDisplacement, instrumentAzimuth);

        result.Should().NotBeNull();
        var resultString = result!.Value.ToString();
        if (resultString.Contains('.'))
        {
            resultString.Split('.')[1].Length.Should().BeLessOrEqualTo(6);
        }
    }

    [Fact(DisplayName = "When azimuth is 180 degrees, then returns negative north displacement")]
    public void WhenAzimuthIs180Degrees_ThenReturnsNegativeNorthDisplacement()
    {
        var northDisplacement = 100m;
        var eastDisplacement = 0m;
        var instrumentAzimuth = 180.0; // 180 degrees

        var result = Domain.Entities.ReadingValue.CalculateADisplacement(
            northDisplacement, eastDisplacement, instrumentAzimuth);

        result.Should().BeApproximately(-100m, 0.000001m);
    }

    [Fact(DisplayName = "When azimuth is 270 degrees, then returns negative east displacement")]
    public void WhenAzimuthIs270Degrees_ThenReturnsNegativeEastDisplacement()
    {
        var northDisplacement = 0m;
        var eastDisplacement = 100m;
        var instrumentAzimuth = 270.0; // 270 degrees

        var result = Domain.Entities.ReadingValue.CalculateADisplacement(
            northDisplacement, eastDisplacement, instrumentAzimuth);

        result.Should().BeApproximately(-100m, 0.000001m);
    }
}
