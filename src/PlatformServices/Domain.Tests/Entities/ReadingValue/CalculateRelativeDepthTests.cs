using Bogus;
using Domain.Entities;

namespace Domain.Tests.Entities.ReadingValue;

[Trait("ReadingValue", "CalculateRelativeDepth")]
public class CalculateRelativeDepthTests
{
    private static readonly Faker Faker = new();
    private const int MetersToMillimetersMultiplier = 1000;

    [Fact(DisplayName = "When quota equals top quota, then returns zero")]
    public void WhenQuotaEqualsTopQuota_ThenReturnsZero()
    {
        var topQuota = 100m; // 100 meters
        var quota = 100000m; // 100000 millimeters = 100 meters

        var result = Domain.Entities.ReadingValue.CalculateRelativeDepth(
            topQuota, quota);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When quota is less than top quota in millimeters, then returns positive depth")]
    public void WhenQuotaIsLessThanTopQuotaInMillimeters_ThenReturnsPositiveDepth()
    {
        var topQuota = 100m; // 100 meters
        var quota = 90000m; // 90000 millimeters = 90 meters
        var expectedResult = 10000m; // (100 * 1000) - 90000 = 100000 - 90000 = 10000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeDepth(
            topQuota, quota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When quota is greater than top quota in millimeters, then returns negative depth")]
    public void WhenQuotaIsGreaterThanTopQuotaInMillimeters_ThenReturnsNegativeDepth()
    {
        var topQuota = 100m; // 100 meters
        var quota = 110000m; // 110000 millimeters = 110 meters
        var expectedResult = -10000m; // (100 * 1000) - 110000 = 100000 - 110000 = -10000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeDepth(
            topQuota, quota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When both values are zero, then returns zero")]
    public void WhenBothValuesAreZero_ThenReturnsZero()
    {
        var topQuota = 0m;
        var quota = 0m;

        var result = Domain.Entities.ReadingValue.CalculateRelativeDepth(
            topQuota, quota);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When top quota is zero and quota is positive, then returns negative depth")]
    public void WhenTopQuotaIsZeroAndQuotaIsPositive_ThenReturnsNegativeDepth()
    {
        var topQuota = 0m;
        var quota = 50000m; // 50000 millimeters = 50 meters
        var expectedResult = -50000m; // (0 * 1000) - 50000 = -50000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeDepth(
            topQuota, quota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When top quota is positive and quota is zero, then returns positive depth")]
    public void WhenTopQuotaIsPositiveAndQuotaIsZero_ThenReturnsPositiveDepth()
    {
        var topQuota = 50m; // 50 meters
        var quota = 0m;
        var expectedResult = 50000m; // (50 * 1000) - 0 = 50000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeDepth(
            topQuota, quota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When top quota is negative, then calculates correctly")]
    public void WhenTopQuotaIsNegative_ThenCalculatesCorrectly()
    {
        var topQuota = -10m; // -10 meters
        var quota = -5000m; // -5000 millimeters = -5 meters
        var expectedResult = -5000m; // (-10 * 1000) - (-5000) = -10000 + 5000 = -5000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeDepth(
            topQuota, quota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When quota is negative and top quota is positive, then returns positive depth")]
    public void WhenQuotaIsNegativeAndTopQuotaIsPositive_ThenReturnsPositiveDepth()
    {
        var topQuota = 10m; // 10 meters
        var quota = -5000m; // -5000 millimeters = -5 meters
        var expectedResult = 15000m; // (10 * 1000) - (-5000) = 10000 + 5000 = 15000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeDepth(
            topQuota, quota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using decimal precision, then calculates accurately")]
    public void WhenUsingDecimalPrecision_ThenCalculatesAccurately()
    {
        var topQuota = 123.456m; // 123.456 meters
        var quota = 98765.432m; // 98765.432 millimeters = 98.765432 meters
        var expectedResult = 24690.568m; // (123.456 * 1000) - 98765.432 = 123456 - 98765.432 = 24690.568

        var result = Domain.Entities.ReadingValue.CalculateRelativeDepth(
            topQuota, quota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using very large values, then calculates correctly")]
    public void WhenUsingVeryLargeValues_ThenCalculatesCorrectly()
    {
        var topQuota = 1000m; // 1000 meters
        var quota = 500000m; // 500000 millimeters = 500 meters
        var expectedResult = 500000m; // (1000 * 1000) - 500000 = 1000000 - 500000 = 500000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeDepth(
            topQuota, quota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using very small values, then calculates correctly")]
    public void WhenUsingVerySmallValues_ThenCalculatesCorrectly()
    {
        var topQuota = 0.001m; // 0.001 meters
        var quota = 0.5m; // 0.5 millimeters = 0.0005 meters
        var expectedResult = 0.5m; // (0.001 * 1000) - 0.5 = 1 - 0.5 = 0.5 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeDepth(
            topQuota, quota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When quota in millimeters equals top quota in meters times 1000, then returns zero")]
    public void WhenQuotaInMillimetersEqualsTopQuotaInMetersTimes1000_ThenReturnsZero()
    {
        var topQuota = 75.5m; // 75.5 meters
        var quota = 75500m; // 75500 millimeters = 75.5 meters

        var result = Domain.Entities.ReadingValue.CalculateRelativeDepth(
            topQuota, quota);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When top quota is 1 meter and quota is 1 millimeter, then returns 999 millimeters")]
    public void WhenTopQuotaIs1MeterAndQuotaIs1Millimeter_ThenReturns999Millimeters()
    {
        var topQuota = 1m; // 1 meter
        var quota = 1m; // 1 millimeter
        var expectedResult = 999m; // (1 * 1000) - 1 = 1000 - 1 = 999 millimeters

        var result = Domain.Entities.ReadingValue.CalculateRelativeDepth(
            topQuota, quota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using random values, then formula is consistent")]
    public void WhenUsingRandomValues_ThenFormulaIsConsistent()
    {
        var topQuota = Faker.Random.Decimal(-100, 100);
        var quota = Faker.Random.Decimal(-100000, 100000);
        var expectedResult = (topQuota * MetersToMillimetersMultiplier) - quota;

        var result = Domain.Entities.ReadingValue.CalculateRelativeDepth(
            topQuota, quota);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When depth is 1 meter, then quota should be 1000 millimeters less than top quota")]
    public void WhenDepthIs1Meter_ThenQuotaShouldBe1000MillimetersLessThanTopQuota()
    {
        var topQuota = 100m; // 100 meters
        var expectedDepth = 1000m; // 1000 millimeters = 1 meter
        var quota = (topQuota * MetersToMillimetersMultiplier) - expectedDepth; // 100000 - 1000 = 99000

        var result = Domain.Entities.ReadingValue.CalculateRelativeDepth(
            topQuota, quota);

        result.Should().Be(expectedDepth);
    }

    [Fact(DisplayName = "When result is negative, then quota is above top quota level")]
    public void WhenResultIsNegative_ThenQuotaIsAboveTopQuotaLevel()
    {
        var topQuota = 50m; // 50 meters = 50000 millimeters
        var quota = 60000m; // 60000 millimeters = 60 meters (above top quota)

        var result = Domain.Entities.ReadingValue.CalculateRelativeDepth(
            topQuota, quota);

        result.Should().BeNegative();
        result.Should().Be(-10000m); // 50000 - 60000 = -10000
    }
}
