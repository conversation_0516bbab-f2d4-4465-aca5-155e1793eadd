using Bogus;
using Domain.Entities;

namespace Domain.Tests.Entities.ReadingValue;

[Trait("ReadingValue", "CalculateDeviation")]
public class CalculateDeviationTests
{
    private static readonly Faker Faker = new();

    [Fact(DisplayName = "When accumulated displacement is null, then returns null")]
    public void WhenAccumulatedDisplacementIsNull_ThenReturnsNull()
    {
        decimal? accumulatedDisplacement = null;
        var referentialAccumulatedDisplacement = Faker.Random.Decimal(-1000, 1000);

        var result = Domain.Entities.ReadingValue.CalculateDeviation(
            accumulatedDisplacement, referentialAccumulatedDisplacement);

        result.Should().BeNull();
    }

    [Fact(DisplayName = "When referential accumulated displacement is null, then returns null")]
    public void WhenReferentialAccumulatedDisplacementIsNull_ThenReturnsNull()
    {
        var accumulatedDisplacement = Faker.Random.Decimal(-1000, 1000);
        decimal? referentialAccumulatedDisplacement = null;

        var result = Domain.Entities.ReadingValue.CalculateDeviation(
            accumulatedDisplacement, referentialAccumulatedDisplacement);

        result.Should().BeNull();
    }

    [Fact(DisplayName = "When both accumulated displacements are null, then returns null")]
    public void WhenBothAccumulatedDisplacementsAreNull_ThenReturnsNull()
    {
        decimal? accumulatedDisplacement = null;
        decimal? referentialAccumulatedDisplacement = null;

        var result = Domain.Entities.ReadingValue.CalculateDeviation(
            accumulatedDisplacement, referentialAccumulatedDisplacement);

        result.Should().BeNull();
    }

    [Fact(DisplayName = "When both displacements are equal, then returns zero")]
    public void WhenBothDisplacementsAreEqual_ThenReturnsZero()
    {
        var displacement = 100m;
        var accumulatedDisplacement = displacement;
        var referentialAccumulatedDisplacement = displacement;

        var result = Domain.Entities.ReadingValue.CalculateDeviation(
            accumulatedDisplacement, referentialAccumulatedDisplacement);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When accumulated displacement is greater than referential, then returns positive deviation")]
    public void WhenAccumulatedDisplacementIsGreaterThanReferential_ThenReturnsPositiveDeviation()
    {
        var accumulatedDisplacement = 150m;
        var referentialAccumulatedDisplacement = 100m;
        var expectedDeviation = 50m; // 150 - 100 = 50

        var result = Domain.Entities.ReadingValue.CalculateDeviation(
            accumulatedDisplacement, referentialAccumulatedDisplacement);

        result.Should().Be(expectedDeviation);
    }

    [Fact(DisplayName = "When accumulated displacement is less than referential, then returns negative deviation")]
    public void WhenAccumulatedDisplacementIsLessThanReferential_ThenReturnsNegativeDeviation()
    {
        var accumulatedDisplacement = 100m;
        var referentialAccumulatedDisplacement = 150m;
        var expectedDeviation = -50m; // 100 - 150 = -50

        var result = Domain.Entities.ReadingValue.CalculateDeviation(
            accumulatedDisplacement, referentialAccumulatedDisplacement);

        result.Should().Be(expectedDeviation);
    }

    [Fact(DisplayName = "When both displacements are zero, then returns zero")]
    public void WhenBothDisplacementsAreZero_ThenReturnsZero()
    {
        var accumulatedDisplacement = 0m;
        var referentialAccumulatedDisplacement = 0m;

        var result = Domain.Entities.ReadingValue.CalculateDeviation(
            accumulatedDisplacement, referentialAccumulatedDisplacement);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When accumulated displacement is zero and referential is positive, then returns negative deviation")]
    public void WhenAccumulatedDisplacementIsZeroAndReferentialIsPositive_ThenReturnsNegativeDeviation()
    {
        var accumulatedDisplacement = 0m;
        var referentialAccumulatedDisplacement = 100m;
        var expectedDeviation = -100m; // 0 - 100 = -100

        var result = Domain.Entities.ReadingValue.CalculateDeviation(
            accumulatedDisplacement, referentialAccumulatedDisplacement);

        result.Should().Be(expectedDeviation);
    }

    [Fact(DisplayName = "When accumulated displacement is positive and referential is zero, then returns positive deviation")]
    public void WhenAccumulatedDisplacementIsPositiveAndReferentialIsZero_ThenReturnsPositiveDeviation()
    {
        var accumulatedDisplacement = 100m;
        var referentialAccumulatedDisplacement = 0m;
        var expectedDeviation = 100m; // 100 - 0 = 100

        var result = Domain.Entities.ReadingValue.CalculateDeviation(
            accumulatedDisplacement, referentialAccumulatedDisplacement);

        result.Should().Be(expectedDeviation);
    }

    [Fact(DisplayName = "When both displacements are negative, then calculates correctly")]
    public void WhenBothDisplacementsAreNegative_ThenCalculatesCorrectly()
    {
        var accumulatedDisplacement = -50m;
        var referentialAccumulatedDisplacement = -100m;
        var expectedDeviation = 50m; // -50 - (-100) = 50

        var result = Domain.Entities.ReadingValue.CalculateDeviation(
            accumulatedDisplacement, referentialAccumulatedDisplacement);

        result.Should().Be(expectedDeviation);
    }

    [Fact(DisplayName = "When accumulated displacement is negative and referential is positive, then returns negative deviation")]
    public void WhenAccumulatedDisplacementIsNegativeAndReferentialIsPositive_ThenReturnsNegativeDeviation()
    {
        var accumulatedDisplacement = -50m;
        var referentialAccumulatedDisplacement = 100m;
        var expectedDeviation = -150m; // -50 - 100 = -150

        var result = Domain.Entities.ReadingValue.CalculateDeviation(
            accumulatedDisplacement, referentialAccumulatedDisplacement);

        result.Should().Be(expectedDeviation);
    }

    [Fact(DisplayName = "When accumulated displacement is positive and referential is negative, then returns positive deviation")]
    public void WhenAccumulatedDisplacementIsPositiveAndReferentialIsNegative_ThenReturnsPositiveDeviation()
    {
        var accumulatedDisplacement = 100m;
        var referentialAccumulatedDisplacement = -50m;
        var expectedDeviation = 150m; // 100 - (-50) = 150

        var result = Domain.Entities.ReadingValue.CalculateDeviation(
            accumulatedDisplacement, referentialAccumulatedDisplacement);

        result.Should().Be(expectedDeviation);
    }

    [Fact(DisplayName = "When using decimal precision, then calculates accurately")]
    public void WhenUsingDecimalPrecision_ThenCalculatesAccurately()
    {
        var accumulatedDisplacement = 123.456789m;
        var referentialAccumulatedDisplacement = 98.765432m;
        var expectedDeviation = 24.691357m; // 123.456789 - 98.765432 = 24.691357

        var result = Domain.Entities.ReadingValue.CalculateDeviation(
            accumulatedDisplacement, referentialAccumulatedDisplacement);

        result.Should().Be(expectedDeviation);
    }

    [Fact(DisplayName = "When using very large values, then calculates correctly")]
    public void WhenUsingVeryLargeValues_ThenCalculatesCorrectly()
    {
        var accumulatedDisplacement = 1000000m;
        var referentialAccumulatedDisplacement = 999000m;
        var expectedDeviation = 1000m; // 1000000 - 999000 = 1000

        var result = Domain.Entities.ReadingValue.CalculateDeviation(
            accumulatedDisplacement, referentialAccumulatedDisplacement);

        result.Should().Be(expectedDeviation);
    }

    [Fact(DisplayName = "When using very small values, then calculates correctly")]
    public void WhenUsingVerySmallValues_ThenCalculatesCorrectly()
    {
        var accumulatedDisplacement = 0.000002m;
        var referentialAccumulatedDisplacement = 0.000001m;
        var expectedDeviation = 0.000001m; // 0.000002 - 0.000001 = 0.000001

        var result = Domain.Entities.ReadingValue.CalculateDeviation(
            accumulatedDisplacement, referentialAccumulatedDisplacement);

        result.Should().Be(expectedDeviation);
    }

    [Fact(DisplayName = "When using random values, then formula is consistent")]
    public void WhenUsingRandomValues_ThenFormulaIsConsistent()
    {
        var accumulatedDisplacement = Faker.Random.Decimal(-1000, 1000);
        var referentialAccumulatedDisplacement = Faker.Random.Decimal(-1000, 1000);
        var expectedDeviation = accumulatedDisplacement - referentialAccumulatedDisplacement;

        var result = Domain.Entities.ReadingValue.CalculateDeviation(
            accumulatedDisplacement, referentialAccumulatedDisplacement);

        result.Should().Be(expectedDeviation);
    }

    [Fact(DisplayName = "When swapping parameters, then result sign changes")]
    public void WhenSwappingParameters_ThenResultSignChanges()
    {
        var value1 = 150m;
        var value2 = 100m;

        var result1 = Domain.Entities.ReadingValue.CalculateDeviation(value1, value2);
        var result2 = Domain.Entities.ReadingValue.CalculateDeviation(value2, value1);

        result1.Should().Be(-result2);
    }
}
