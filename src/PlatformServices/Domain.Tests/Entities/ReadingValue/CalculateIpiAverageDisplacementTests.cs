using Bogus;
using Domain.Entities;

namespace Domain.Tests.Entities.ReadingValue;

[Trait("ReadingValue", "CalculateIpiAverageDisplacement")]
public class CalculateIpiAverageDisplacementTests
{
    private static readonly Faker Faker = new();

    [Fact(DisplayName = "When axis reading is zero degrees, then returns zero")]
    public void WhenAxisReadingIsZeroDegrees_ThenReturnsZero()
    {
        var length = 100.0;
        var axisReading = 0.0; // 0 degrees

        var result = Domain.Entities.ReadingValue.CalculateIpiAverageDisplacement(
            length, axisReading);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When axis reading is 90 degrees, then returns length")]
    public void WhenAxisReadingIs90Degrees_ThenReturnsLength()
    {
        var length = 100.0;
        var axisReading = 90.0; // 90 degrees

        var result = Domain.Entities.ReadingValue.CalculateIpiAverageDisplacement(
            length, axisReading);

        result.Should().Be(100m);
    }

    [Fact(DisplayName = "When axis reading is 180 degrees, then returns zero")]
    public void WhenAxisReadingIs180Degrees_ThenReturnsZero()
    {
        var length = 100.0;
        var axisReading = 180.0; // 180 degrees

        var result = Domain.Entities.ReadingValue.CalculateIpiAverageDisplacement(
            length, axisReading);

        result.Should().BeApproximately(0m, 0.000001m);
    }

    [Fact(DisplayName = "When axis reading is 270 degrees, then returns negative length")]
    public void WhenAxisReadingIs270Degrees_ThenReturnsNegativeLength()
    {
        var length = 100.0;
        var axisReading = 270.0; // 270 degrees

        var result = Domain.Entities.ReadingValue.CalculateIpiAverageDisplacement(
            length, axisReading);

        result.Should().BeApproximately(-100m, 0.000001m);
    }

    [Fact(DisplayName = "When axis reading is 30 degrees, then calculates correctly")]
    public void WhenAxisReadingIs30Degrees_ThenCalculatesCorrectly()
    {
        var length = 100.0;
        var axisReading = 30.0; // 30 degrees
        var expectedResult = 50m; // 100 * sin(30°) = 100 * 0.5 = 50

        var result = Domain.Entities.ReadingValue.CalculateIpiAverageDisplacement(
            length, axisReading);

        result.Should().BeApproximately(expectedResult, 0.000001m);
    }

    [Fact(DisplayName = "When axis reading is 45 degrees, then calculates correctly")]
    public void WhenAxisReadingIs45Degrees_ThenCalculatesCorrectly()
    {
        var length = 100.0;
        var axisReading = 45.0; // 45 degrees
        var expectedResult = 70.710678m; // 100 * sin(45°) = 100 * √2/2 ≈ 70.710678

        var result = Domain.Entities.ReadingValue.CalculateIpiAverageDisplacement(
            length, axisReading);

        result.Should().BeApproximately(expectedResult, 0.000001m);
    }

    [Fact(DisplayName = "When axis reading is 60 degrees, then calculates correctly")]
    public void WhenAxisReadingIs60Degrees_ThenCalculatesCorrectly()
    {
        var length = 100.0;
        var axisReading = 60.0; // 60 degrees
        var expectedResult = 86.602540m; // 100 * sin(60°) = 100 * √3/2 ≈ 86.602540

        var result = Domain.Entities.ReadingValue.CalculateIpiAverageDisplacement(
            length, axisReading);

        result.Should().BeApproximately(expectedResult, 0.000001m);
    }

    [Fact(DisplayName = "When length is zero, then returns zero")]
    public void WhenLengthIsZero_ThenReturnsZero()
    {
        var length = 0.0;
        var axisReading = Faker.Random.Double(0, 360);

        var result = Domain.Entities.ReadingValue.CalculateIpiAverageDisplacement(
            length, axisReading);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When length is negative, then calculates correctly")]
    public void WhenLengthIsNegative_ThenCalculatesCorrectly()
    {
        var length = -100.0;
        var axisReading = 90.0; // 90 degrees
        var expectedResult = -100m; // -100 * sin(90°) = -100 * 1 = -100

        var result = Domain.Entities.ReadingValue.CalculateIpiAverageDisplacement(
            length, axisReading);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When axis reading is negative, then calculates correctly")]
    public void WhenAxisReadingIsNegative_ThenCalculatesCorrectly()
    {
        var length = 100.0;
        var axisReading = -30.0; // -30 degrees
        var expectedResult = -50m; // 100 * sin(-30°) = 100 * (-0.5) = -50

        var result = Domain.Entities.ReadingValue.CalculateIpiAverageDisplacement(
            length, axisReading);

        result.Should().BeApproximately(expectedResult, 0.000001m);
    }

    [Fact(DisplayName = "When axis reading is greater than 360 degrees, then calculates correctly")]
    public void WhenAxisReadingIsGreaterThan360Degrees_ThenCalculatesCorrectly()
    {
        var length = 100.0;
        var axisReading = 450.0; // 450 degrees = 90 degrees (450 - 360)
        var expectedResult = 100m; // 100 * sin(450°) = 100 * sin(90°) = 100

        var result = Domain.Entities.ReadingValue.CalculateIpiAverageDisplacement(
            length, axisReading);

        result.Should().BeApproximately(expectedResult, 0.000001m);
    }

    [Fact(DisplayName = "When using decimal precision, then calculates accurately")]
    public void WhenUsingDecimalPrecision_ThenCalculatesAccurately()
    {
        var length = 123.456789;
        var axisReading = 37.5; // 37.5 degrees
        var sinValue = Math.Sin(37.5 * Math.PI / 180.0);
        var expectedResult = (decimal)(length * sinValue);

        var result = Domain.Entities.ReadingValue.CalculateIpiAverageDisplacement(
            length, axisReading);

        result.Should().BeApproximately(expectedResult, 0.000001m);
    }

    [Fact(DisplayName = "When using very small length, then calculates correctly")]
    public void WhenUsingVerySmallLength_ThenCalculatesCorrectly()
    {
        var length = 0.001;
        var axisReading = 90.0; // 90 degrees
        var expectedResult = 0.001m; // 0.001 * sin(90°) = 0.001 * 1 = 0.001

        var result = Domain.Entities.ReadingValue.CalculateIpiAverageDisplacement(
            length, axisReading);

        result.Should().BeApproximately(expectedResult, 0.000001m);
    }

    [Fact(DisplayName = "When using very large length, then calculates correctly")]
    public void WhenUsingVeryLargeLength_ThenCalculatesCorrectly()
    {
        var length = 10000.0;
        var axisReading = 30.0; // 30 degrees
        var expectedResult = 5000m; // 10000 * sin(30°) = 10000 * 0.5 = 5000

        var result = Domain.Entities.ReadingValue.CalculateIpiAverageDisplacement(
            length, axisReading);

        result.Should().BeApproximately(expectedResult, 0.000001m);
    }

    [Fact(DisplayName = "When axis reading is very small, then calculates correctly")]
    public void WhenAxisReadingIsVerySmall_ThenCalculatesCorrectly()
    {
        var length = 100.0;
        var axisReading = 0.001; // 0.001 degrees
        var sinValue = Math.Sin(0.001 * Math.PI / 180.0);
        var expectedResult = (decimal)(length * sinValue);

        var result = Domain.Entities.ReadingValue.CalculateIpiAverageDisplacement(
            length, axisReading);

        result.Should().BeApproximately(expectedResult, 0.000001m);
    }

    [Fact(DisplayName = "When using random values, then formula is consistent")]
    public void WhenUsingRandomValues_ThenFormulaIsConsistent()
    {
        var length = Faker.Random.Double(1, 1000);
        var axisReading = Faker.Random.Double(0, 360);
        var sinValue = Math.Sin(axisReading * Math.PI / 180.0);
        var expectedResult = (decimal)(length * sinValue);

        var result = Domain.Entities.ReadingValue.CalculateIpiAverageDisplacement(
            length, axisReading);

        result.Should().BeApproximately(expectedResult, 0.000001m);
    }
}
