using Bogus;
using Domain.Entities;

namespace Domain.Tests.Entities.ReadingValue;

[Trait("ReadingValue", "CalculateQuota")]
public class CalculateQuotaTests
{
    private static readonly Faker Faker = new();
    private const int MetersToMillimetersMultiplier = 1000;

    [Fact(DisplayName = "When is referential measurement, then returns measurement quota in millimeters")]
    public void WhenIsReferentialMeasurement_ThenReturnsMeasurementQuotaInMillimeters()
    {
        var isReferentialMeasurement = true;
        var measurementQuota = 5.5m; // 5.5 meters
        var referentialMeasurementQuota = Faker.Random.Decimal(0, 10);
        var deltaRef = Faker.Random.Decimal(-10, 10);
        var expectedResult = 5500m; // 5.5 * 1000 = 5500 millimeters

        var result = Domain.Entities.ReadingValue.CalculateQuota(
            isReferentialMeasurement, measurementQuota, referentialMeasurementQuota, deltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When is not referential and delta ref is zero, then returns referential measurement quota in millimeters")]
    public void WhenIsNotReferentialAndDeltaRefIsZero_ThenReturnsReferentialMeasurementQuotaInMillimeters()
    {
        var isReferentialMeasurement = false;
        var measurementQuota = 3.0m; // 3.0 meters (not used when not referential)
        var referentialMeasurementQuota = 7.5m; // 7.5 meters
        var deltaRef = 0m;
        var expectedResult = 7500m; // 7.5 * 1000 + 0 * 1000 = 7500 millimeters

        var result = Domain.Entities.ReadingValue.CalculateQuota(
            isReferentialMeasurement, measurementQuota, referentialMeasurementQuota, deltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When is not referential and delta ref is positive, then adds delta ref to referential quota")]
    public void WhenIsNotReferentialAndDeltaRefIsPositive_ThenAddsDeltaRefToReferentialQuota()
    {
        var isReferentialMeasurement = false;
        var measurementQuota = 3.0m; // 3.0 meters (not used when not referential)
        var referentialMeasurementQuota = 5.0m; // 5.0 meters
        var deltaRef = 2.0m; // 2.0 meters
        var expectedResult = 7000m; // (5.0 * 1000) + (2.0 * 1000) = 5000 + 2000 = 7000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateQuota(
            isReferentialMeasurement, measurementQuota, referentialMeasurementQuota, deltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When is not referential and delta ref is negative, then subtracts delta ref from referential quota")]
    public void WhenIsNotReferentialAndDeltaRefIsNegative_ThenSubtractsDeltaRefFromReferentialQuota()
    {
        var isReferentialMeasurement = false;
        var measurementQuota = 3.0m; // 3.0 meters (not used when not referential)
        var referentialMeasurementQuota = 5.0m; // 5.0 meters
        var deltaRef = -1.5m; // -1.5 meters
        var expectedResult = 3500m; // (5.0 * 1000) + (-1.5 * 1000) = 5000 - 1500 = 3500 millimeters

        var result = Domain.Entities.ReadingValue.CalculateQuota(
            isReferentialMeasurement, measurementQuota, referentialMeasurementQuota, deltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When measurement quota is zero and is referential, then returns zero")]
    public void WhenMeasurementQuotaIsZeroAndIsReferential_ThenReturnsZero()
    {
        var isReferentialMeasurement = true;
        var measurementQuota = 0m;
        var referentialMeasurementQuota = Faker.Random.Decimal(0, 10);
        var deltaRef = Faker.Random.Decimal(-10, 10);

        var result = Domain.Entities.ReadingValue.CalculateQuota(
            isReferentialMeasurement, measurementQuota, referentialMeasurementQuota, deltaRef);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When referential measurement quota is zero and is not referential, then returns delta ref in millimeters")]
    public void WhenReferentialMeasurementQuotaIsZeroAndIsNotReferential_ThenReturnsDeltaRefInMillimeters()
    {
        var isReferentialMeasurement = false;
        var measurementQuota = 3.0m; // 3.0 meters (not used when not referential)
        var referentialMeasurementQuota = 0m;
        var deltaRef = 2.5m; // 2.5 meters
        var expectedResult = 2500m; // (0 * 1000) + (2.5 * 1000) = 2500 millimeters

        var result = Domain.Entities.ReadingValue.CalculateQuota(
            isReferentialMeasurement, measurementQuota, referentialMeasurementQuota, deltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using negative quotas, then calculates correctly")]
    public void WhenUsingNegativeQuotas_ThenCalculatesCorrectly()
    {
        var isReferentialMeasurement = false;
        var measurementQuota = -1.0m; // -1.0 meter (not used when not referential)
        var referentialMeasurementQuota = -2.0m; // -2.0 meters
        var deltaRef = -1.0m; // -1.0 meter
        var expectedResult = -3000m; // (-2.0 * 1000) + (-1.0 * 1000) = -2000 - 1000 = -3000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateQuota(
            isReferentialMeasurement, measurementQuota, referentialMeasurementQuota, deltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When referential measurement quota is negative and delta ref is positive, then calculates correctly")]
    public void WhenReferentialMeasurementQuotaIsNegativeAndDeltaRefIsPositive_ThenCalculatesCorrectly()
    {
        var isReferentialMeasurement = false;
        var measurementQuota = 1.0m; // 1.0 meter (not used when not referential)
        var referentialMeasurementQuota = -3.0m; // -3.0 meters
        var deltaRef = 2.0m; // 2.0 meters
        var expectedResult = -1000m; // (-3.0 * 1000) + (2.0 * 1000) = -3000 + 2000 = -1000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateQuota(
            isReferentialMeasurement, measurementQuota, referentialMeasurementQuota, deltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using decimal precision, then calculates accurately")]
    public void WhenUsingDecimalPrecision_ThenCalculatesAccurately()
    {
        var isReferentialMeasurement = false;
        var measurementQuota = 1.111111m; // 1.111111 meters (not used when not referential)
        var referentialMeasurementQuota = 2.345678m; // 2.345678 meters
        var deltaRef = 0.987654m; // 0.987654 meters
        var expectedResult = 3333.332m; // (2.345678 * 1000) + (0.987654 * 1000) = 2345.678 + 987.654 = 3333.332

        var result = Domain.Entities.ReadingValue.CalculateQuota(
            isReferentialMeasurement, measurementQuota, referentialMeasurementQuota, deltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using very large values, then calculates correctly")]
    public void WhenUsingVeryLargeValues_ThenCalculatesCorrectly()
    {
        var isReferentialMeasurement = false;
        var measurementQuota = 100.0m; // 100.0 meters (not used when not referential)
        var referentialMeasurementQuota = 500.0m; // 500.0 meters
        var deltaRef = 250.0m; // 250.0 meters
        var expectedResult = 750000m; // (500.0 * 1000) + (250.0 * 1000) = 500000 + 250000 = 750000 millimeters

        var result = Domain.Entities.ReadingValue.CalculateQuota(
            isReferentialMeasurement, measurementQuota, referentialMeasurementQuota, deltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using very small values, then calculates correctly")]
    public void WhenUsingVerySmallValues_ThenCalculatesCorrectly()
    {
        var isReferentialMeasurement = false;
        var measurementQuota = 0.001m; // 0.001 meter (not used when not referential)
        var referentialMeasurementQuota = 0.002m; // 0.002 meters
        var deltaRef = 0.001m; // 0.001 meter
        var expectedResult = 3m; // (0.002 * 1000) + (0.001 * 1000) = 2 + 1 = 3 millimeters

        var result = Domain.Entities.ReadingValue.CalculateQuota(
            isReferentialMeasurement, measurementQuota, referentialMeasurementQuota, deltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When is referential measurement with negative quota, then returns negative quota in millimeters")]
    public void WhenIsReferentialMeasurementWithNegativeQuota_ThenReturnsNegativeQuotaInMillimeters()
    {
        var isReferentialMeasurement = true;
        var measurementQuota = -3.5m; // -3.5 meters
        var referentialMeasurementQuota = Faker.Random.Decimal(0, 10);
        var deltaRef = Faker.Random.Decimal(-10, 10);
        var expectedResult = -3500m; // -3.5 * 1000 = -3500 millimeters

        var result = Domain.Entities.ReadingValue.CalculateQuota(
            isReferentialMeasurement, measurementQuota, referentialMeasurementQuota, deltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using random values for referential measurement, then formula is consistent")]
    public void WhenUsingRandomValuesForReferentialMeasurement_ThenFormulaIsConsistent()
    {
        var isReferentialMeasurement = true;
        var measurementQuota = Faker.Random.Decimal(-100, 100);
        var referentialMeasurementQuota = Faker.Random.Decimal(-100, 100);
        var deltaRef = Faker.Random.Decimal(-100, 100);
        var expectedResult = measurementQuota * MetersToMillimetersMultiplier;

        var result = Domain.Entities.ReadingValue.CalculateQuota(
            isReferentialMeasurement, measurementQuota, referentialMeasurementQuota, deltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using random values for non-referential measurement, then formula is consistent")]
    public void WhenUsingRandomValuesForNonReferentialMeasurement_ThenFormulaIsConsistent()
    {
        var isReferentialMeasurement = false;
        var measurementQuota = Faker.Random.Decimal(-100, 100);
        var referentialMeasurementQuota = Faker.Random.Decimal(-100, 100);
        var deltaRef = Faker.Random.Decimal(-100, 100);
        var expectedResult = (referentialMeasurementQuota * MetersToMillimetersMultiplier) + 
                           (deltaRef * MetersToMillimetersMultiplier);

        var result = Domain.Entities.ReadingValue.CalculateQuota(
            isReferentialMeasurement, measurementQuota, referentialMeasurementQuota, deltaRef);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When delta ref cancels out referential quota, then returns zero")]
    public void WhenDeltaRefCancelsOutReferentialQuota_ThenReturnsZero()
    {
        var isReferentialMeasurement = false;
        var measurementQuota = 5.0m; // 5.0 meters (not used when not referential)
        var referentialMeasurementQuota = 3.0m; // 3.0 meters
        var deltaRef = -3.0m; // -3.0 meters (cancels out referential quota)

        var result = Domain.Entities.ReadingValue.CalculateQuota(
            isReferentialMeasurement, measurementQuota, referentialMeasurementQuota, deltaRef);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When comparing referential vs non-referential with same measurement quota, then results differ")]
    public void WhenComparingReferentialVsNonReferentialWithSameMeasurementQuota_ThenResultsDiffer()
    {
        var measurementQuota = 5.0m;
        var referentialMeasurementQuota = 3.0m;
        var deltaRef = 1.0m;

        var referentialResult = Domain.Entities.ReadingValue.CalculateQuota(
            true, measurementQuota, referentialMeasurementQuota, deltaRef);
        var nonReferentialResult = Domain.Entities.ReadingValue.CalculateQuota(
            false, measurementQuota, referentialMeasurementQuota, deltaRef);

        referentialResult.Should().Be(5000m); // measurementQuota * 1000
        nonReferentialResult.Should().Be(4000m); // (referentialMeasurementQuota + deltaRef) * 1000
        referentialResult.Should().NotBe(nonReferentialResult);
    }
}
