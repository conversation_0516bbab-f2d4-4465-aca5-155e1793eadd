using Bogus;
using Domain.Entities;

namespace Domain.Tests.Entities.ReadingValue;

[Trait("ReadingValue", "CalculateConventionalAverageDisplacement")]
public class CalculateConventionalAverageDisplacementTests
{
    private static readonly Faker Faker = new();

    [Fact(DisplayName = "When positive and negative are equal, then returns zero")]
    public void WhenPositiveAndNegativeAreEqual_ThenReturnsZero()
    {
        var positive = 100m;
        var negative = 100m;

        var result = Domain.Entities.ReadingValue.CalculateConventionalAverageDisplacement(
            positive, negative);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When positive is greater than negative, then returns positive displacement")]
    public void WhenPositiveIsGreaterThanNegative_ThenReturnsPositiveDisplacement()
    {
        var positive = 150m;
        var negative = 100m;
        var expectedResult = 25m; // (150 - 100) / 2 = 25

        var result = Domain.Entities.ReadingValue.CalculateConventionalAverageDisplacement(
            positive, negative);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When positive is less than negative, then returns negative displacement")]
    public void WhenPositiveIsLessThanNegative_ThenReturnsNegativeDisplacement()
    {
        var positive = 100m;
        var negative = 150m;
        var expectedResult = -25m; // (100 - 150) / 2 = -25

        var result = Domain.Entities.ReadingValue.CalculateConventionalAverageDisplacement(
            positive, negative);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When both values are zero, then returns zero")]
    public void WhenBothValuesAreZero_ThenReturnsZero()
    {
        var positive = 0m;
        var negative = 0m;

        var result = Domain.Entities.ReadingValue.CalculateConventionalAverageDisplacement(
            positive, negative);

        result.Should().Be(0m);
    }

    [Fact(DisplayName = "When positive is zero and negative is positive, then returns negative displacement")]
    public void WhenPositiveIsZeroAndNegativeIsPositive_ThenReturnsNegativeDisplacement()
    {
        var positive = 0m;
        var negative = 100m;
        var expectedResult = -50m; // (0 - 100) / 2 = -50

        var result = Domain.Entities.ReadingValue.CalculateConventionalAverageDisplacement(
            positive, negative);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When positive is positive and negative is zero, then returns positive displacement")]
    public void WhenPositiveIsPositiveAndNegativeIsZero_ThenReturnsPositiveDisplacement()
    {
        var positive = 100m;
        var negative = 0m;
        var expectedResult = 50m; // (100 - 0) / 2 = 50

        var result = Domain.Entities.ReadingValue.CalculateConventionalAverageDisplacement(
            positive, negative);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When both values are negative, then calculates correctly")]
    public void WhenBothValuesAreNegative_ThenCalculatesCorrectly()
    {
        var positive = -50m;
        var negative = -100m;
        var expectedResult = 25m; // (-50 - (-100)) / 2 = 50 / 2 = 25

        var result = Domain.Entities.ReadingValue.CalculateConventionalAverageDisplacement(
            positive, negative);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When positive is negative and negative is positive, then returns negative displacement")]
    public void WhenPositiveIsNegativeAndNegativeIsPositive_ThenReturnsNegativeDisplacement()
    {
        var positive = -50m;
        var negative = 100m;
        var expectedResult = -75m; // (-50 - 100) / 2 = -150 / 2 = -75

        var result = Domain.Entities.ReadingValue.CalculateConventionalAverageDisplacement(
            positive, negative);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using decimal precision, then calculates accurately")]
    public void WhenUsingDecimalPrecision_ThenCalculatesAccurately()
    {
        var positive = 123.456789m;
        var negative = 98.765432m;
        var expectedResult = 12.345679m; // (123.456789 - 98.765432) / 2 = 24.691357 / 2 = 12.345679 (rounded)

        var result = Domain.Entities.ReadingValue.CalculateConventionalAverageDisplacement(
            positive, negative);

        result.Should().BeApproximately(expectedResult, 0.000001m);
    }

    [Fact(DisplayName = "When using very large values, then calculates correctly")]
    public void WhenUsingVeryLargeValues_ThenCalculatesCorrectly()
    {
        var positive = 1000000m;
        var negative = 999000m;
        var expectedResult = 500m; // (1000000 - 999000) / 2 = 1000 / 2 = 500

        var result = Domain.Entities.ReadingValue.CalculateConventionalAverageDisplacement(
            positive, negative);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using very small values, then calculates correctly")]
    public void WhenUsingVerySmallValues_ThenCalculatesCorrectly()
    {
        var positive = 0.000002m;
        var negative = 0.000001m;
        var expectedResult = 0.0000005m; // (0.000002 - 0.000001) / 2 = 0.000001 / 2 = 0.0000005

        var result = Domain.Entities.ReadingValue.CalculateConventionalAverageDisplacement(
            positive, negative);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When difference is odd, then result has decimal")]
    public void WhenDifferenceIsOdd_ThenResultHasDecimal()
    {
        var positive = 101m;
        var negative = 100m;
        var expectedResult = 0.5m; // (101 - 100) / 2 = 1 / 2 = 0.5

        var result = Domain.Entities.ReadingValue.CalculateConventionalAverageDisplacement(
            positive, negative);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When using random values, then formula is consistent")]
    public void WhenUsingRandomValues_ThenFormulaIsConsistent()
    {
        var positive = Faker.Random.Decimal(-1000, 1000);
        var negative = Faker.Random.Decimal(-1000, 1000);
        var expectedResult = (positive - negative) / 2m;

        var result = Domain.Entities.ReadingValue.CalculateConventionalAverageDisplacement(
            positive, negative);

        result.Should().Be(expectedResult);
    }

    [Fact(DisplayName = "When swapping positive and negative values, then result sign changes")]
    public void WhenSwappingPositiveAndNegativeValues_ThenResultSignChanges()
    {
        var value1 = 150m;
        var value2 = 100m;

        var result1 = Domain.Entities.ReadingValue.CalculateConventionalAverageDisplacement(
            value1, value2);
        var result2 = Domain.Entities.ReadingValue.CalculateConventionalAverageDisplacement(
            value2, value1);

        result1.Should().Be(-result2);
    }
}
