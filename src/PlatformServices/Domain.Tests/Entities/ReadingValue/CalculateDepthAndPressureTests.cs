using Bogus;
using Domain.Entities;

namespace Domain.Tests.Entities.ReadingValue;

[Trait("ReadingValue", "CalculateDepthAndPressure")]
public class CalculateDepthAndPressureTests
{
    private static readonly Faker Faker = new();
    private const decimal Gravity = 9.81M;

    [Fact(DisplayName = "When quota equals top quota, then depth is zero")]
    public void WhenQuotaEqualsTopQuota_ThenDepthIsZero()
    {
        var quota = 100m;
        var topQuota = 100m;
        var baseQuota = 50m;

        var (depth, pressure) = Domain.Entities.ReadingValue.CalculateDepthAndPressure(
            quota, topQuota, baseQuota);

        depth.Should().Be(0m);
    }

    [Fact(DisplayName = "When quota is below top quota, then depth is positive")]
    public void WhenQuotaIsBelowTopQuota_ThenDepthIsPositive()
    {
        var quota = 90m;
        var topQuota = 100m;
        var baseQuota = 50m;
        var expectedDepth = 10m; // topQuota - quota = 100 - 90 = 10

        var (depth, pressure) = Domain.Entities.ReadingValue.CalculateDepthAndPressure(
            quota, topQuota, baseQuota);

        depth.Should().Be(expectedDepth);
    }

    [Fact(DisplayName = "When quota is above top quota, then depth is negative")]
    public void WhenQuotaIsAboveTopQuota_ThenDepthIsNegative()
    {
        var quota = 110m;
        var topQuota = 100m;
        var baseQuota = 50m;
        var expectedDepth = -10m; // topQuota - quota = 100 - 110 = -10

        var (depth, pressure) = Domain.Entities.ReadingValue.CalculateDepthAndPressure(
            quota, topQuota, baseQuota);

        depth.Should().Be(expectedDepth);
    }

    [Fact(DisplayName = "When quota equals base quota, then pressure is zero")]
    public void WhenQuotaEqualsBaseQuota_ThenPressureIsZero()
    {
        var quota = 50m;
        var topQuota = 100m;
        var baseQuota = 50m;

        var (depth, pressure) = Domain.Entities.ReadingValue.CalculateDepthAndPressure(
            quota, topQuota, baseQuota);

        pressure.Should().Be(0m);
    }

    [Fact(DisplayName = "When quota is above base quota, then pressure is positive")]
    public void WhenQuotaIsAboveBaseQuota_ThenPressureIsPositive()
    {
        var quota = 60m;
        var topQuota = 100m;
        var baseQuota = 50m;
        var expectedPressure = 98.1m; // (quota - baseQuota) * gravity = (60 - 50) * 9.81 = 98.1

        var (depth, pressure) = Domain.Entities.ReadingValue.CalculateDepthAndPressure(
            quota, topQuota, baseQuota);

        pressure.Should().Be(expectedPressure);
    }

    [Fact(DisplayName = "When quota is below base quota, then pressure is negative")]
    public void WhenQuotaIsBelowBaseQuota_ThenPressureIsNegative()
    {
        var quota = 40m;
        var topQuota = 100m;
        var baseQuota = 50m;
        var expectedPressure = -98.1m; // (quota - baseQuota) * gravity = (40 - 50) * 9.81 = -98.1

        var (depth, pressure) = Domain.Entities.ReadingValue.CalculateDepthAndPressure(
            quota, topQuota, baseQuota);

        pressure.Should().Be(expectedPressure);
    }

    [Fact(DisplayName = "When all quotas are equal, then depth and pressure are zero")]
    public void WhenAllQuotasAreEqual_ThenDepthAndPressureAreZero()
    {
        var quota = 100m;
        var topQuota = 100m;
        var baseQuota = 100m;

        var (depth, pressure) = Domain.Entities.ReadingValue.CalculateDepthAndPressure(
            quota, topQuota, baseQuota);

        depth.Should().Be(0m);
        pressure.Should().Be(0m);
    }

    [Fact(DisplayName = "When quotas are negative, then calculates correctly")]
    public void WhenQuotasAreNegative_ThenCalculatesCorrectly()
    {
        var quota = -10m;
        var topQuota = -5m;
        var baseQuota = -15m;
        var expectedDepth = 5m; // topQuota - quota = -5 - (-10) = 5
        var expectedPressure = 49.05m; // (quota - baseQuota) * gravity = (-10 - (-15)) * 9.81 = 49.05

        var (depth, pressure) = Domain.Entities.ReadingValue.CalculateDepthAndPressure(
            quota, topQuota, baseQuota);

        depth.Should().Be(expectedDepth);
        pressure.Should().Be(expectedPressure);
    }

    [Fact(DisplayName = "When using decimal precision, then calculates accurately")]
    public void WhenUsingDecimalPrecision_ThenCalculatesAccurately()
    {
        var quota = 100.123456m;
        var topQuota = 100.654321m;
        var baseQuota = 99.987654m;
        var expectedDepth = 0.530865m; // topQuota - quota = 100.654321 - 100.123456 = 0.530865
        var expectedPressure = 1.33221762m; // (quota - baseQuota) * gravity = (100.123456 - 99.987654) * 9.81 = 1.33221762

        var (depth, pressure) = Domain.Entities.ReadingValue.CalculateDepthAndPressure(
            quota, topQuota, baseQuota);

        depth.Should().Be(expectedDepth);
        pressure.Should().Be(expectedPressure);
    }

    [Fact(DisplayName = "When top quota is less than base quota, then calculates correctly")]
    public void WhenTopQuotaIsLessThanBaseQuota_ThenCalculatesCorrectly()
    {
        var quota = 75m;
        var topQuota = 50m;
        var baseQuota = 100m;
        var expectedDepth = -25m; // topQuota - quota = 50 - 75 = -25
        var expectedPressure = -245.25m; // (quota - baseQuota) * gravity = (75 - 100) * 9.81 = -245.25

        var (depth, pressure) = Domain.Entities.ReadingValue.CalculateDepthAndPressure(
            quota, topQuota, baseQuota);

        depth.Should().Be(expectedDepth);
        pressure.Should().Be(expectedPressure);
    }

    [Fact(DisplayName = "When using large values, then calculates correctly")]
    public void WhenUsingLargeValues_ThenCalculatesCorrectly()
    {
        var quota = 1000m;
        var topQuota = 1500m;
        var baseQuota = 500m;
        var expectedDepth = 500m; // topQuota - quota = 1500 - 1000 = 500
        var expectedPressure = 4905m; // (quota - baseQuota) * gravity = (1000 - 500) * 9.81 = 4905

        var (depth, pressure) = Domain.Entities.ReadingValue.CalculateDepthAndPressure(
            quota, topQuota, baseQuota);

        depth.Should().Be(expectedDepth);
        pressure.Should().Be(expectedPressure);
    }

    [Fact(DisplayName = "When using very small values, then calculates correctly")]
    public void WhenUsingVerySmallValues_ThenCalculatesCorrectly()
    {
        var quota = 0.001m;
        var topQuota = 0.002m;
        var baseQuota = 0.0005m;
        var expectedDepth = 0.001m; // topQuota - quota = 0.002 - 0.001 = 0.001
        var expectedPressure = 0.004905m; // (quota - baseQuota) * gravity = (0.001 - 0.0005) * 9.81 = 0.004905

        var (depth, pressure) = Domain.Entities.ReadingValue.CalculateDepthAndPressure(
            quota, topQuota, baseQuota);

        depth.Should().Be(expectedDepth);
        pressure.Should().Be(expectedPressure);
    }
}
