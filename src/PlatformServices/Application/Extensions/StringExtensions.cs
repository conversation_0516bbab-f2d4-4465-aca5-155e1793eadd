using System.Linq;

namespace Application.Extensions;

public static class StringExtensions
{
    /// <summary>
    /// Appends a change history string to the target string, separated by a specified character or string.
    /// If the change history is null or empty, returns the target string unchanged.
    /// </summary>
    /// <param name="target">The original string to append to.</param>
    /// <param name="changeHistory">The change history string to append.</param>
    /// <param name="separatorChar">The separator to use between the target and change history. Defaults to a space.</param>
    /// <returns>The combined string with change history appended, or the original string if change history is empty.</returns>
    public static string AppendChangeHistory(
        this string target,
        string changeHistory,
        string separatorChar = " ")
    {
        if (string.IsNullOrEmpty(changeHistory))
        {
            return target;
        }

        if (!string.IsNullOrEmpty(target))
        {
            target += separatorChar;
        }

        target += changeHistory;
        return target;
    }
}