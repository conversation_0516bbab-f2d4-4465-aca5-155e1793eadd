using System;
using System.Collections;
using Domain.Extensions;
using KellermanSoftware.CompareNetObjects;
using KellermanSoftware.CompareNetObjects.TypeComparers;

namespace Application.Extensions.Comparer;

public class ResponsibleComparer : BaseTypeComparer
{
    private readonly RootComparer _rootComparer;

    public ResponsibleComparer(RootComparer rootComparer) : base(rootComparer)
    {
        _rootComparer = rootComparer;
    }

    public override bool IsTypeMatch(Type type1, Type type2)
    {
        return type1 == typeof(Domain.Entities.Responsible) &&
               type2 == typeof(Domain.Entities.Responsible);
    }

    public override void CompareType(CompareParms parms)
    {
        var obj1 = (Domain.Entities.Responsible)parms.Object1;
        var obj2 = (Domain.Entities.Responsible)parms.Object2;

        Compare(parms, obj1, obj2);
    }

    private void Compare(
        CompareParms parms,
        Domain.Entities.Responsible original,
        Domain.Entities.Responsible modified)
    {
        var originalReviewProps = original.GetType().GetProperties();
        var membersToIgnore = parms.Config.MembersToIgnore;
        var maxDateDiffMilliseconds =
            parms.Config.MaxMillisecondsDateDifference;

        foreach (var prop in originalReviewProps)
        {
            var propName = $"{parms.BreadCrumb}.{prop.Name}";

            if (membersToIgnore.Contains(propName) ||
                membersToIgnore.Contains(prop.Name))
            {
                continue;
            }

            var originalValue = prop.GetValue(original);
            var modifiedValue = prop.GetValue(modified);

            var propType = Nullable.GetUnderlyingType(prop.PropertyType) ??
                           prop.PropertyType;

            if (propType.IsEnum)
            {
                var originalDescription = originalValue != null
                    ? ((Enum)originalValue).GetDescription()
                    : null;
                var modifiedDescription = modifiedValue != null
                    ? ((Enum)modifiedValue).GetDescription()
                    : null;

                if (!string.Equals(originalDescription, modifiedDescription))
                {
                    AddDifference(parms, prop.Name, originalDescription,
                        modifiedDescription);
                }

                continue;
            }

            if (originalValue is DateTime originalDateTime &&
                modifiedValue is DateTime modifiedDateTime)
            {
                if (Math.Abs((modifiedDateTime - originalDateTime)
                        .TotalMilliseconds) > maxDateDiffMilliseconds)
                {
                    AddDifference(parms, prop.Name, originalValue,
                        modifiedValue);
                }

                continue;
            }

            if (!Equals(originalValue, modifiedValue))
            {
                if (originalValue is Domain.ValueObjects.File ||
                    modifiedValue is Domain.ValueObjects.File)
                {
                    AddDifference(
                        parms,
                        prop.Name,
                        originalValue == null
                            ? originalValue
                            : ((Domain.ValueObjects.File)originalValue).Name,
                        modifiedValue == null
                            ? modifiedValue
                            : ((Domain.ValueObjects.File)modifiedValue).Name
                    );
                    continue;
                }

                if (originalValue is IList originalList &&
                    modifiedValue is IList modifiedList)
                {
                    var newParams = new CompareParms()
                    {
                        Config = parms.Config,
                        Object1 = originalList,
                        Object2 = modifiedList,
                        Result = parms.Result,
                        BreadCrumb = $"{parms.BreadCrumb}.{prop.Name}"
                    };

                    _rootComparer.Compare(newParams);
                    continue;
                }

                AddDifference(parms, prop.Name, originalValue, modifiedValue);
            }
        }
    }

    private static void AddDifference(
        CompareParms parms,
        string propName,
        object originalValue,
        object modifiedValue)
    {
        var difference = new Difference
        {
            PropertyName = $"{parms.BreadCrumb}.{propName}",
            Object1Value = originalValue?.ToString(),
            Object2Value = modifiedValue?.ToString(),
            ParentObject1 = parms.Object1,
            ParentObject2 = parms.Object2
        };

        parms.Result.Differences.Add(difference);
    }
}