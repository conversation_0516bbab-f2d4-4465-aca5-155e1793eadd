using System;
using ClosedXML.Excel;
using System.IO;
using System.Linq;
using System.Threading;
using Domain.Core;

namespace Application.Extensions
{
    public static class XLWorkbookExtensions
    {
        private static readonly string[] DateFields = {
            Reading.GetTemplateFile.TemplateHeaders.Names.DateAndTime,
            Instrument.GetTemplateFile.TemplateHeaders.Names.InstallationDate,
        };
        
        /// <summary>
        /// Sets the column names for the worksheet.
        /// </summary>
        /// <param name="workbook">The Excel workbook to add the worksheet to.</param>
        /// <param name="columns">The array of column names to set.</param>
        /// <param name="worksheetName">The name of the worksheet to create.</param>
        /// <returns>The created worksheet with styled headers.</returns>
        public static IXLWorksheet SetColumnNames(
            this XLWorkbook workbook, 
            string[] columns, 
            string worksheetName)
        {
            ArgumentNullException.ThrowIfNull(workbook);
            ArgumentNullException.ThrowIfNull(columns);
            ArgumentNullException.ThrowIfNull(worksheetName);
            
            Thread.CurrentThread.ChangeCulture(Locale.PtBr);

            var ws = workbook.Worksheets.Add(worksheetName);

            ws.Row(1).Style
                .Font.SetBold(true)
                .Font.SetFontSize(12)
                .Fill.SetBackgroundColor(XLColor.DarkGreen)
                .Font.SetFontColor(XLColor.White);

            for (int i = 1; i <= columns.Length; i++)
            {
                ws.Column(i).Cell(1).Value = columns[i - 1];
                
                if (DateFields.Contains(columns[i - 1]))
                {
                    ws.Column(i).Style.DateFormat.NumberFormatId =
                        Convert.ToInt32(XLPredefinedFormat.DateTime.Text);
                }
            }

            ws.Columns().AdjustToContents();

            return ws;
        }

        /// <summary>
        /// Sets the column names and comments for the worksheet
        /// </summary>
        /// <param name="workbook">The Excel workbook to add the worksheet to.</param>
        /// <param name="columns">An array of tuples containing names and comments of each column.</param>
        /// <param name="worksheetName">The name of the worksheet to create.</param>
        /// <returns>The created worksheet with styled headers and comments.</returns>
        public static IXLWorksheet SetColumnNamesAndComments(
            this XLWorkbook workbook,
            (string Name, string Comment)[] columns,
            string worksheetName)
        {
            ArgumentNullException.ThrowIfNull(workbook);
            ArgumentNullException.ThrowIfNull(columns);
            ArgumentNullException.ThrowIfNull(worksheetName);
            
            Thread.CurrentThread.ChangeCulture(Locale.PtBr);

            var ws = workbook.Worksheets.Add(worksheetName);

            ws.Row(1).Style
                .Font.SetBold(true)
                .Font.SetFontSize(12)
                .Fill.SetBackgroundColor(XLColor.DarkGreen)
                .Font.SetFontColor(XLColor.White);

            for (int i = 1; i <= columns.Length; i++)
            {
                ws.Column(i).Cell(1).Value = columns[i - 1].Name;
                _ = ws.Column(i).Cell(1).GetComment().AddText(columns[i - 1].Comment);
                
                if (DateFields.Contains(columns[i - 1].Name))
                {
                    ws.Column(i).Style.DateFormat.NumberFormatId =
                        Convert.ToInt32(XLPredefinedFormat.DateTime.Text);
                }
            }

            ws.Columns().AdjustToContents();

            return ws;
        }

        /// <summary>
        /// Saves the Excel workbook as a byte array.
        /// </summary>
        /// <param name="workbook">The Excel workbook to save.</param>
        /// <returns>A byte array representing the saved workbook.</returns>
        public static byte[] SaveAsBytes(this XLWorkbook workbook)
        {
            using var stream = new MemoryStream();
            workbook.SaveAs(stream);
            return stream.ToArray();
        }
    }
}
