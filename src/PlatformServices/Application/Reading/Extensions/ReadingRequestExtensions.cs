using System;
using System.Linq;
using Application.Reading.Add;
using Domain.Entities;
using Model.Reading._Shared;
using Model.Reading._Shared.ConvertReadings;

namespace Application.Reading.Extensions;

public static class ReadingRequestExtensions
{
    public static Domain.Entities.Reading ConvertToEntity(
        this ReadingRequest readingRequest)
    {
        if (readingRequest == null)
        {
            return null;
        }
        
        var reading = new Domain.Entities.Reading()
        {
            Instrument = new() { Id = readingRequest.Instrument.Id },
            IsReferential = readingRequest.IsReferential,
            InstrumentBaseQuota = readingRequest.DbInstrument.BaseQuota,
            InstrumentTopQuota = readingRequest.DbInstrument.TopQuota,
            InstrumentAzimuth = readingRequest.DbInstrument.Azimuth,
            InstrumentMeasurementFrequency =
                readingRequest.DbInstrument.MeasurementFrequency,
            InstrumentUpperLimit = readingRequest.DbInstrument.UpperLimit,
            InstrumentLowerLimit = readingRequest.DbInstrument.LowerLimit,
            IsAutomated = readingRequest.IsAutomated
                .GetValueOrDefault()
        };

        if (readingRequest.IsReferential != null)
        {
            reading.ReferenceReading = new()
            {
                Id = (bool)readingRequest.IsReferential
                    ? reading.Id
                    : readingRequest.ReferenceReading.Id
            };
        }

        reading.Values = readingRequest.Values
            .Select(value => value.ConvertToEntity(
                reading.Id,
                readingRequest.DbInstrument))
            .ToList();

        return reading;
    }
}


public static class ConvertedReadingExtensions
{
    public static Domain.Entities.Reading ConvertToEntity(
        this ConvertedReading readingRequest,
        Domain.Entities.Instrument instrument)
    {
        var reading = new Domain.Entities.Reading()
        {
            Instrument = new() { Id = instrument.Id },
            IsReferential = readingRequest.IsReferential,
            InstrumentBaseQuota = instrument.BaseQuota,
            InstrumentTopQuota = instrument.TopQuota,
            InstrumentAzimuth = instrument.Azimuth,
            InstrumentUpperLimit = instrument.UpperLimit,
            InstrumentLowerLimit = instrument.LowerLimit,
        };

        reading.Values = readingRequest.Values
            .Select(value =>
            {
                var readingValue = new ReadingValue()
                {
                    Reading = new() { Id = reading.Id },
                    AAxisPga = value.AAxisPga,
                    Measurement = value.Measurement == null
                        ? null
                        : new() { Id = value.Measurement.Id },
                    Nature = value.Nature == null
                        ? null
                        : new() { Id = value.Nature.Id },
                    AAxisReading = value.AAxisReading,
                    AbsoluteSettlement = value.AbsoluteSettlement,
                    Intensity = value.Intensity,
                    Pluviometry = value.Pluviometry,
                    NegativeA = value.NegativeA,
                    AccumulatedDisplacementA = value.AccumulatedDisplacementA,
                    NegativeB = value.NegativeB,
                    NorthCoordinate = value.NorthCoordinate,
                    NorthDisplacement = value.NorthDisplacement,
                    AccumulatedDisplacementB = value.AccumulatedDisplacementB,
                    PositiveA = value.PositiveA,
                    ADisplacement = value.ADisplacement,
                    AverageDisplacementA = value.AverageDisplacementA,
                    AverageDisplacementB = value.AverageDisplacementB,
                    BAxisPga = value.BAxisPga,
                    BAxisReading = value.BAxisReading,
                    BDisplacement = value.BDisplacement,
                    Date = value.Date,
                    Datum = value.Datum,
                    DeltaRef = value.DeltaRef,
                    Depth = value.Depth,
                    DeviationA = value.DeviationA,
                    DeviationB = value.DeviationB,
                    Dry = value.Dry,
                    EastCoordinate = value.EastCoordinate,
                    EastDisplacement = value.EastDisplacement,
                    PositiveB = value.PositiveB,
                    Pressure = value.Pressure,
                    Quota = value.Quota,
                    RelativeDepth = value.RelativeDepth,
                    RelativeSettlement = value.RelativeSettlement,
                    TotalPlanimetricDisplacement = value.TotalPlanimetricDisplacement,
                    ZAxisPga = value.ZAxisPga,
                    ZDisplacement = value.ZDisplacement,
                    SecurityLevels = instrument
                        .SecurityLevels
                        ?.Select(x => new SecurityLevels()
                        {
                            AbruptVariation = x.AbruptVariation,
                            Alert = x.Alert,
                            Attention = x.Attention,
                            Axis = x.Axis,
                            Emergency = x.Emergency,
                            MaximumDailyRainfall = x.MaximumDailyRainfall,
                            RainIntensity = x.RainIntensity
                        })
                        .ToList()
                };
        
                if (readingValue.Measurement == null)
                {
                    return readingValue;
                }
        
                var measurement = instrument
                    .Measurements?
                    .FirstOrDefault(x => x.Id == readingValue.Measurement.Id);
        
                readingValue.MeasurementDeltaRef = measurement?.DeltaRef;
                readingValue.MeasurementQuota = measurement?.Quota;
                readingValue.MeasurementLength = measurement?.Length;
                readingValue.MeasurementIsReferential = measurement?.IsReferential;
        
                if (measurement?.SecurityLevels != null)
                {
                    readingValue.SecurityLevels.Add(
                        new()
                        {
                            AbruptVariation = measurement.SecurityLevels
                                .AbruptVariation,
                            Alert = measurement.SecurityLevels.Alert,
                            Attention = measurement.SecurityLevels
                                .Attention,
                            Emergency = measurement.SecurityLevels
                                .Emergency,
                            Axis = measurement.SecurityLevels.Axis,
                            MaximumDailyRainfall = measurement.SecurityLevels
                                .MaximumDailyRainfall,
                            RainIntensity = measurement
                                .SecurityLevels.RainIntensity
                        });
                }
        
                return readingValue;
            })
            .ToList();

        return reading;
    }
}