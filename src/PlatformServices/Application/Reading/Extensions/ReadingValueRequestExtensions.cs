using System;
using System.Linq;
using Domain.Entities;
using Model.Reading._Shared.ReadingValue;

namespace Application.Reading.Extensions;

public static class ReadingValueRequestExtensions
{
    public static ReadingValue ConvertToEntity(
        this ReadingValueRequest request,
        Guid readingId,
        Domain.Entities.Instrument instrument)
    {
        var readingValue = new ReadingValue()
        {
            Reading = new() { Id = readingId },
            AAxisPga = request.AAxisPga,
            Measurement = request.Measurement == null
                ? null
                : new() { Id = request.Measurement.Id },
            Nature = request.Nature == null
                ? null
                : new() { Id = request.Nature.Id },
            AAxisReading = request.AAxisReading,
            AbsoluteSettlement = request.AbsoluteSettlement,
            Intensity = request.Intensity,
            Pluviometry = request.Pluviometry,
            NegativeA = request.NegativeA,
            AccumulatedDisplacementA = request.AccumulatedDisplacementA,
            NegativeB = request.NegativeB,
            NorthCoordinate = request.NorthCoordinate,
            NorthDisplacement = request.NorthDisplacement,
            AccumulatedDisplacementB = request.AccumulatedDisplacementB,
            PositiveA = request.PositiveA,
            ADisplacement = request.ADisplacement,
            AverageDisplacementA = request.AverageDisplacementA,
            AverageDisplacementB = request.AverageDisplacementB,
            BAxisPga = request.BAxisPga,
            BAxisReading = request.BAxisReading,
            BDisplacement = request.BDisplacement,
            Date = request.Date,
            Datum = request.Datum,
            DeltaRef = request.DeltaRef,
            Depth = request.Depth,
            DeviationA = request.DeviationA,
            DeviationB = request.DeviationB,
            Dry = request.Dry,
            EastCoordinate = request.EastCoordinate,
            EastDisplacement = request.EastDisplacement,
            PositiveB = request.PositiveB,
            Pressure = request.Pressure,
            Quota = request.Quota,
            RelativeDepth = request.RelativeDepth,
            RelativeSettlement = request.RelativeSettlement,
            TotalPlanimetricDisplacement = request.TotalPlanimetricDisplacement,
            ZAxisPga = request.ZAxisPga,
            ZDisplacement = request.ZDisplacement,
            SecurityLevels = instrument
                .SecurityLevels
                ?.Select(x => new SecurityLevels()
                {
                    AbruptVariation = x.AbruptVariation,
                    Alert = x.Alert,
                    Attention = x.Attention,
                    Axis = x.Axis,
                    Emergency = x.Emergency,
                    MaximumDailyRainfall = x.MaximumDailyRainfall,
                    RainIntensity = x.RainIntensity
                })
                .ToList()
        };

        if (readingValue.Measurement == null)
        {
            return readingValue;
        }

        var measurement = instrument
            .Measurements?
            .FirstOrDefault(x => x.Id == readingValue.Measurement.Id);

        readingValue.MeasurementDeltaRef = measurement?.DeltaRef;
        readingValue.MeasurementQuota = measurement?.Quota;
        readingValue.MeasurementLength = measurement?.Length;
        readingValue.MeasurementIsReferential = measurement?.IsReferential;

        if (measurement?.SecurityLevels != null)
        {
            readingValue.SecurityLevels.Add(
                new()
                {
                    AbruptVariation = measurement.SecurityLevels
                        .AbruptVariation,
                    Alert = measurement.SecurityLevels.Alert,
                    Attention = measurement.SecurityLevels
                        .Attention,
                    Emergency = measurement.SecurityLevels
                        .Emergency,
                    Axis = measurement.SecurityLevels.Axis,
                    MaximumDailyRainfall = measurement.SecurityLevels
                        .MaximumDailyRainfall,
                    RainIntensity = measurement
                        .SecurityLevels.RainIntensity
                });
        }

        return readingValue;
    }
}
