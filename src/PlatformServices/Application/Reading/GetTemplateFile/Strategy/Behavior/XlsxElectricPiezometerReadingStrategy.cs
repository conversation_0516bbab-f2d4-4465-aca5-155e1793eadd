using Application.Extensions;
using ClosedXML.Excel;
using static Application.Reading.GetTemplateFile.TemplateHeaders;

namespace Application.Reading.GetTemplateFile.Strategy.Behavior
{
    public class XlsxElectricPiezometerReadingStrategy : IFileStrategy
    {
        public byte[] GetFile()
        {
            var workbook = new XLWorkbook();

            var columns = new (string, string)[]
            {
                (Names.InstrumentIdentifier, Comments.InstrumentIdentifier),
                (Names.PressureCellIdentifier, Comments.PressureCellIdentifier),
                (Names.DateAndTime, Comments.DateAndTime),
                (Names.Quota, Comments.Quota),
                (Names.Dry, Comments.Dry)
            };

            workbook.SetColumnNamesAndComments(columns, "Leituras de PZE");
            return workbook.SaveAsBytes();
        }
    }
}
