using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Application.Core;
using Application.Extensions;
using Application.Reading.Add.Strategy.Factory;
using Application.Reading.Extensions;
using Database.Repositories.Instrument;
using Database.Repositories.Reading;
using Database.Repositories.User;
using Model.Reading.Add.Request;
using static Application.Core.UseCaseResponseFactory<System.Collections.Generic.IEnumerable<System.Guid>>;

namespace Application.Reading.Add;

public sealed class AddReadingUseCase : IAddReadingUseCase
{
    private readonly IReadingRepository _readingRepository;
    private readonly IInstrumentRepository _instrumentRepository;
    private readonly IUserRepository _userRepository;
    private readonly AddReadingsRequestValidator _requestValidator = new();

    public AddReadingUseCase(
        IReadingRepository readingRepository,
        IInstrumentRepository instrumentRepository,
        IUserRepository userRepository)
    {
        _readingRepository = readingRepository;
        _instrumentRepository = instrumentRepository;
        _userRepository = userRepository;
    }

    public async Task<UseCaseResponse<IEnumerable<Guid>>> Execute(
        AddReadingsRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(null);
            }

            await request.AddRequesterStructures(_userRepository);

            var instruments = await
                _instrumentRepository.GetAsync(request.GetInstrumentIds());
            
            request.SetDbInstruments(instruments);

            var refReadingsFromDatabase =
                await _readingRepository.GetReferentialsAsync(
                    request.GetInstrumentIds());

            var refReadingsFromRequest =  request.GetReferentialReadings()?
                .Select(reading => reading?.ConvertToEntity())?
                .ToList();

            request.SetReferentialReadings(refReadingsFromDatabase, refReadingsFromRequest);

            var validationResults = new ConcurrentBag<string>();

            await Parallel.ForEachAsync(
                source: request.Readings,
                body: async (reading, _) =>
                {
                    var validatorStrategy = reading.DbInstrument.Type
                        .GetStrategy();

                    if (validatorStrategy == null)
                    {
                        return;
                    }

                    var message = await validatorStrategy.ValidateAsync(
                        _readingRepository,
                        reading,
                        request);

                    if (!string.IsNullOrEmpty(message))
                    {
                        validationResults.Add(message);
                    }
                });

            if (validationResults.Any())
            {
                return BadRequest(null, "000", string.Join(Environment.NewLine, validationResults));
            }

            var validationResult =
                await _requestValidator.ValidateAsync(request);

            if (!validationResult.IsValid)
            {
                return BadRequest(
                    null,
                    validationResult.Errors.ToErrorMessages());
            }

            var existingReadings = await
                _readingRepository
                    .GetExistingReadings(
                        request.ConvertToExistingReadingsQuery());

            if (existingReadings.Any(item => item.Exists))
            {
                return BadRequest(
                    null,
                    "000",
                    existingReadings.GetErrorMessages(instruments));
            }

            var commonReadingsFromRequest = request.GetCommonReadings()
                .Select(reading => reading.ConvertToEntity())
                .ToList();

            var allReadingsToInsert = commonReadingsFromRequest
                .Concat(refReadingsFromRequest)
                .OrderByDescending(reading => reading.IsReferential)
                .ToList();

            await _readingRepository.AddAsync(allReadingsToInsert);

            return Ok(allReadingsToInsert?.Select(x => x.Id));
        }
        catch (Exception e)
        {
            return InternalServerError(null, errors: e.ToErrorMessages("000"));
        }
    }
}