using Application.Reading.Add.Strategy.Behavior;
using Domain.Enums;

namespace Application.Reading.Add.Strategy.Factory
{
    public static class ValidatorFactory
    {
        private static readonly ConventionalInclinometerValidator
            ConventionalInclinometerValidator = new();

        private static readonly IPIInclinometerValidator
            IpiInclinometerValidator = new();

        private static readonly SurfaceLandmarkValidator
            SurfaceLandmarkValidator = new();
        
        private static readonly PrismValidator PrismValidator = new();

        private static readonly PluviometerValidator PluviometerValidator =
            new();
        
        public static IValidatorStrategy GetStrategy(this InstrumentType type)
        {
            return type switch
            {
                InstrumentType.ConventionalInclinometer => ConventionalInclinometerValidator,
                InstrumentType.IPIInclinometer => IpiInclinometerValidator,
                InstrumentType.SurfaceLandmark => SurfaceLandmarkValidator,
                InstrumentType.Prism => PrismValidator,
                InstrumentType.Pluviometer => PluviometerValidator,
                _ => null
            };
        }
    }
}
