using Application.Reading.ConvertFileToAdd.Strategy.Behavior;
using Database.Repositories.Nature;
using Domain.Enums;

namespace Application.Reading.ConvertFileToAdd.Strategy.Factory
{
    public static class FileToJsonReadingConverterFactory
    {
        public static IFileToJsonReadingConverterStrategy GetStrategy(
            InstrumentType type, 
            INatureRepository natureRepository)
        {
            return type switch
            {
                InstrumentType.IPIInclinometer => new AddIPIInclinometerReadingStrategy(),
                InstrumentType.ConventionalInclinometer => new AddConventionalInclinometerReadingStrategy(),
                InstrumentType.ElectricPiezometer => new AddElectricPiezometerReadingStrategy(),
                InstrumentType.OpenStandpipePiezometer => new AddOpenStandpipePiezometerReadingStrategy(),
                InstrumentType.Geophone => new AddGeophoneReadingStrategy(natureRepository),
                InstrumentType.SettlementGauge => new AddSettlementGaugeReadingStrategy(),
                InstrumentType.SurfaceLandmark or InstrumentType.Prism => new AddSurfaceLandmarkAndPrismReadingStrategy(),
                InstrumentType.WaterLevelIndicator => new AddWaterLevelIndicatorReadingStrategy(),
                InstrumentType.LinimetricRuler => new AddLinimetricRulerReadingStrategy(),
                InstrumentType.Pluviometer => new AddPluviometerReadingStrategy(),
                InstrumentType.Pluviograph => new AddPluviographReadingStrategy(),
                _ => null,
            };
        }
    }
}
