using Application.Extensions;
using Application.Reading.ConvertFileToAdd.Extensions;
using Model.Extensions;
using Model.Reading._Shared.ConvertReadings;
using Model.Reading._Shared.ReadingValue;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Domain.Entities;
using Domain.Extensions;

namespace Application.Reading.ConvertFileToAdd.Strategy.Behavior
{
#pragma warning disable S101
    public sealed class IpiInclinometerReadingConverter : IReadingConverter
#pragma warning restore S101
    {
        public Task ConvertRowAsync
            (DataRow row,
            List<ConvertedReading> readings,
            Domain.Entities.Instrument instrument,
            Domain.Entities.Reading referentialReading)
        {
            var identifier = row[0].ToString();
            var isReferential = row[2].ToString().ToBool();

            var readingInList = readings
                .Find(i => i.Instrument.Identifier == identifier);

            var shouldUseListReading = readingInList != null
                && readingInList.Values.Count < instrument.Measurements.Count;

            var reading = shouldUseListReading ? readingInList : new ConvertedReading()
            {
                Instrument = instrument.Convert(),
                IsReferential = isReferential
            };

            if (!(bool)reading.IsReferential)
            {
                reading.IsReferential = isReferential;
            }

            if (!shouldUseListReading)
            {
                readings.Add(reading);
            }

            var measurement = instrument.Measurements[0];

            if (instrument.Measurements.Count > 1)
            {
                var measurementIdentifier = row[1].ToString();

                if (string.IsNullOrEmpty(measurementIdentifier))
                {
                    throw new InvalidCastException("Unable to convert measurement identifier to string.");
                }

                measurement = instrument.Measurements
                    .Find(x => x.Identifier == measurementIdentifier)
                    ?? throw new InvalidOperationException("Unable to find instrument measurement.");
            }

            // If the reading already has a value for the measurement, skip it.
            if (reading.Values.Exists(x => x.Measurement?.Id == measurement.Id))
            {
                return Task.CompletedTask;
            }

            var length = Convert.ToDouble(measurement.Length.Value);
            _ = double.TryParse(row[4].ToString(), out double aAxisReading);
            _ = double.TryParse(row[5].ToString(), out double bAxisReading);

            var averageADisplacement = ReadingValue.CalculateIpiAverageDisplacement(length, aAxisReading);
            var averageBDisplacement = ReadingValue.CalculateIpiAverageDisplacement(length, bAxisReading);

            var readingValueRequest = new ReadingValueRequest
            {
                Depth = measurement.Depth,
                Quota = measurement.Quota,
                Date = row[3].ToString().ToUtcDateTime(),
                AAxisReading = (decimal)aAxisReading,
                BAxisReading = (decimal)bAxisReading,
                AverageDisplacementA = averageADisplacement,
                AverageDisplacementB = averageBDisplacement,
                Measurement = new()
                {
                    Id = measurement.Id,
                    Identifier = measurement.Identifier
                }
            };

            reading.Values.Add(readingValueRequest);
            reading.Values = reading.Values.OrderBy(x => x.Depth).ToList();

            reading.CalculateInclinometerAccumulatedValues();

            var referentialValues = referentialReading?.Values?.Find(x => x.Measurement.Id == measurement.Id);

            if (reading.IsReferential.Value || referentialValues == null)
            {
                reading.IsReferential = true;

                reading.CalculateDeviation(null);
            }
            else
            {
                reading.CalculateDeviation(referentialReading);
            }

            return Task.CompletedTask;
        }
    }
}
