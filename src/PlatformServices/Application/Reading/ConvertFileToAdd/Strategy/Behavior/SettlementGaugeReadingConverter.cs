using Application.Extensions;
using Model.Reading._Shared.ConvertReadings;
using Model.Reading._Shared.ReadingValue;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Domain.Entities;
using Domain.Extensions;

namespace Application.Reading.ConvertFileToAdd.Strategy.Behavior
{
    public sealed class SettlementGaugeReadingConverter : IReadingConverter
    {
        public Task ConvertRowAsync(
            DataRow row,
            List<ConvertedReading> readings,
            Domain.Entities.Instrument instrument,
            Domain.Entities.Reading referentialReading)
        {
            var identifier = row[0].ToString();

            var readingInList = readings
                .Find(i => i.Instrument.Identifier == identifier);

            var shouldUseListReading = readingInList != null
                && readingInList.Values.Count < instrument.Measurements.Count;

            var reading = shouldUseListReading ? readingInList : new ConvertedReading()
            {
                Instrument = instrument.Convert()
            };

            if (!shouldUseListReading)
            {
                readings.Add(reading);
            }

            var measurement = instrument.Measurements[0];

            var referentialMeasurement = instrument.Measurements?.Find(m => m.IsReferential.HasValue && m.IsReferential.Value);

            if (instrument.Measurements.Count > 1)
            {
                var measurementIdentifier = row[1].ToString();

                if (string.IsNullOrEmpty(measurementIdentifier))
                {
                    throw new InvalidCastException("Unable to convert measurement identifier to string.");
                }

                measurement = instrument.Measurements
                    .Find(x => x.Identifier == measurementIdentifier)
                    ?? throw new InvalidOperationException("Unable to find instrument measurement.");
            }

            if (referentialMeasurement == null)
            {
                throw new InvalidOperationException("Unable to find referential measurement for the instrument.");
            }

            if (!instrument.Depth.HasValue)
            {
                throw new InvalidOperationException("Unable to find instrument depth. Depth is needed to calculate delta reference.");
            }

            if (!instrument.TopQuota.HasValue)
            {
                throw new InvalidOperationException("Unable to find instrument top quota. Top Quota is needed to calculate delta reference.");
            }

            // If the reading already has a value for the measurement, skip it.
            if (reading.Values.Exists(x => x.Measurement?.Id == measurement.Id))
            {
                return Task.CompletedTask;
            }

            var depthHasBeenConverted = decimal.TryParse(row[3].ToString(), out decimal depth);
            var absoluteSettlementHasBeenConverted = decimal.TryParse(row[4].ToString(), out decimal absoluteSettlement);

            if (!depthHasBeenConverted)
            {
                depth = ReadingValue.CalculateAbsoluteDepth(absoluteSettlement, measurement.Quota, instrument.TopQuota.Value);
            }

            if (!absoluteSettlementHasBeenConverted)
            {
                depth = Math.Round(depth, ReadingValue.QuotaDecimalPlaces);
                absoluteSettlement = ReadingValue.CalculateAbsoluteSettlement(depth, measurement.Quota, instrument.TopQuota.Value);
            }

            var readingValueRequest = new ReadingValueRequest
            {
                Date = row[2].ToString().ToUtcDateTime(),
                Depth = depth,
                AbsoluteSettlement = absoluteSettlement,
                Measurement = new()
                {
                    Id = measurement.Id,
                    Identifier = measurement.Identifier
                }
            };

            reading.Values.Add(readingValueRequest);

            var referentialMagneticRingReading = reading.Values.Find(value => value.Measurement.Id == referentialMeasurement.Id);

            if (referentialMagneticRingReading != null)
            {
                foreach (var value in reading.Values)
                {
                    var isReferentialMeasurement = referentialMagneticRingReading.Measurement.Id == value.Measurement.Id;

                    measurement = instrument.Measurements
                        .Find(x => x.Identifier == value.Measurement.Identifier);

                    var deltaReference = ReadingValue.CalculateDeltaReference(isReferentialMeasurement, referentialMagneticRingReading.Depth, value.Depth.Value);
                    var relativeSettlement = ReadingValue.CalculateRelativeSettlementFromDepth(isReferentialMeasurement, deltaReference, measurement.DeltaRef ?? 0);
                    var quota = ReadingValue.CalculateQuota(isReferentialMeasurement, measurement.Quota, referentialMeasurement.Quota, deltaReference);
                    var relativeDepth = ReadingValue.CalculateRelativeDepth(instrument.TopQuota.Value, quota);

                    value.DeltaRef = deltaReference;
                    value.RelativeSettlement = relativeSettlement;
                    value.Quota = quota;
                    value.RelativeDepth = relativeDepth;
                }
            }

            return Task.CompletedTask;
        }

        
    }
}
