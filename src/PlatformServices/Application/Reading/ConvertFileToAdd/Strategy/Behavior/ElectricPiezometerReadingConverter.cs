using Application.Extensions;
using Model.Reading._Shared.ConvertReadings;
using Model.Reading._Shared.ReadingValue;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Domain.Entities;
using Domain.Extensions;

namespace Application.Reading.ConvertFileToAdd.Strategy.Behavior
{
    public sealed class ElectricPiezometerReadingConverter : IReadingConverter
    {
        public Task ConvertRowAsync(
            DataRow row, 
            List<ConvertedReading> readings, 
            Domain.Entities.Instrument instrument,
            Domain.Entities.Reading referentialReading)
        {
            var identifier = row[0].ToString();
            var date = row[2].ToString().ToUtcDateTime();

            var readingFromList = readings
                .Find(i => i.Instrument.Identifier == identifier && i.Values.Any(value => value.Date == date));

            var shouldUseReadingFromList = readingFromList != null
                && readingFromList.Values.Count < instrument.Measurements.Count;

            var reading = shouldUseReadingFromList ? readingFromList : new ConvertedReading()
            {
                Instrument = instrument.Convert()
            };

            if (!shouldUseReadingFromList)
            {
                readings.Add(reading);
            }

            var measurement = instrument.Measurements[0];

            if (instrument.Measurements.Count > 1)
            {
                var measurementIdentifier = row[1].ToString();

                if (string.IsNullOrEmpty(measurementIdentifier))
                {
                    throw new InvalidCastException("Unable to convert measurement identifier to string.");
                }

                measurement = instrument.Measurements
                    .Find(x => x.Identifier == measurementIdentifier)
                    ?? throw new InvalidOperationException("Unable to find instrument measurement.");
            }

            // If the reading already has a value for the measurement, skip it.
            if (reading.Values.Exists(x => x.Measurement?.Id == measurement.Id))
            {
                return Task.CompletedTask;
            }

            var isDry = row[4].ToString().ToBool();

            var readingValueRequest = new ReadingValueRequest
            {
                Date = date,
                Dry = isDry,
                Measurement = new()
                {
                    Id = measurement.Id,
                    Identifier = measurement.Identifier
                }
            };

            reading.Values.Add(readingValueRequest);

            if (isDry)
            {
                return Task.CompletedTask;
            }

            var quotaHasBeenConverted = decimal.TryParse(row[3].ToString(), out decimal quota);

            if (!quotaHasBeenConverted)
            {
                readingValueRequest.Quota = 0;
                return Task.CompletedTask;
            }

            readingValueRequest.Quota = quotaHasBeenConverted ? Math.Round(quota, ReadingValue.QuotaDecimalPlaces) : null;

            return Task.CompletedTask;
        }
    }
}
