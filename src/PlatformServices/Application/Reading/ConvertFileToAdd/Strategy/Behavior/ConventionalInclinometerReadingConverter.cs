using Application.Extensions;
using Application.Reading.ConvertFileToAdd.Extensions;
using Model.Reading._Shared.ConvertReadings;
using Model.Reading._Shared.ReadingValue;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Domain.Entities;
using Domain.Extensions;

namespace Application.Reading.ConvertFileToAdd.Strategy.Behavior
{
    public sealed class ConventionalInclinometerReadingConverter : IReadingConverter
    {
        public Task ConvertRowAsync(
            DataRow row,
            List<ConvertedReading> readings,
            Domain.Entities.Instrument instrument,
            Domain.Entities.Reading referentialReading)
        {
            var identifier = row[0].ToString();
            var isReferential = row[2].ToString().ToBool();

            var readingInList = readings
                .Find(i => i.Instrument.Identifier == identifier);

            var shouldUseListReading = readingInList != null
                && readingInList.Values.Count < instrument.Measurements.Count;

            var reading = shouldUseListReading ? readingInList : new ConvertedReading()
            {
                Instrument = instrument.Convert(),
                IsReferential = isReferential
            };

            if (!(bool)reading.IsReferential)
            {
                reading.IsReferential = isReferential;
            }

            if (!shouldUseListReading)
            {
                readings.Add(reading);
            }

            var measurement = instrument.Measurements[0];

            if (instrument.Measurements.Count > 1)
            {
                var measurementIdentifier = row[1].ToString();

                if (string.IsNullOrEmpty(measurementIdentifier))
                {
                    throw new InvalidCastException("Unable to convert measurement identifier to string.");
                }

                measurement = instrument.Measurements
                    .Find(x => x.Identifier == measurementIdentifier)
                    ?? throw new InvalidOperationException("Unable to find instrument measurement.");
            }

            // If the reading already has a value for the measurement, skip it.
            if (reading.Values.Exists(x => x.Measurement?.Id == measurement.Id))
            {
                return Task.CompletedTask;
            }

            var averageDisplacementAHasBeenConverted = decimal.TryParse(row[8].ToString(), out decimal averageDisplacementA);
            var positiveAHasBeenConverted = decimal.TryParse(row[4].ToString(), out decimal positiveA);
            var negativeAHasBeenConverted = decimal.TryParse(row[5].ToString(), out decimal negativeA);

            if (!averageDisplacementAHasBeenConverted
                && positiveAHasBeenConverted
                && negativeAHasBeenConverted)
            {
                averageDisplacementA = ReadingValue.CalculateConventionalAverageDisplacement(positiveA, negativeA);
            }

            var averageDisplacementBHasBeenConverted = decimal.TryParse(row[9].ToString(), out decimal averageDisplacementB);
            var positiveBHasBeenConverted = decimal.TryParse(row[6].ToString(), out decimal positiveB);
            var negativeBHasBeenConverted = decimal.TryParse(row[7].ToString(), out decimal negativeB);

            if (!averageDisplacementBHasBeenConverted
                && positiveBHasBeenConverted
                && negativeBHasBeenConverted)
            {
                averageDisplacementB = ReadingValue.CalculateConventionalAverageDisplacement(positiveB, negativeB);
            }

            var readingValueRequest = new ReadingValueRequest
            {
                Date = row[3].ToString().ToUtcDateTime(),
                Depth = measurement.Depth,
                Quota = measurement.Quota,
                PositiveA = positiveA,
                NegativeA = negativeA,
                PositiveB = positiveB,
                NegativeB = negativeB,
                AverageDisplacementA = averageDisplacementA,
                AverageDisplacementB = averageDisplacementB,
                Measurement = new()
                {
                    Id = measurement.Id,
                    Identifier = measurement.Identifier
                },
            };

            reading.Values.Add(readingValueRequest);
            reading.Values = reading.Values.OrderBy(x => x.Depth).ToList();

            reading.CalculateInclinometerAccumulatedValues();

            var referentialValues = referentialReading?.Values?.Find(x => x.Measurement.Id == measurement.Id);

            if (reading.IsReferential.Value || referentialValues == null)
            {
                reading.IsReferential = true;

                reading.CalculateDeviation(null);
            }
            else
            {
                reading.CalculateDeviation(referentialReading);
            }

            return Task.CompletedTask;
        }
    }
}
