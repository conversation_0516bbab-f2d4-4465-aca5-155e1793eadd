using Application.Extensions;
using Model.Reading._Shared.ConvertReadings;
using Model.Reading._Shared.ReadingValue;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Domain.Entities;
using Domain.Extensions;

namespace Application.Reading.ConvertFileToAdd.Strategy.Behavior
{
    public sealed class WaterLevelIndicatorReadingConverter : IReadingConverter
    {
        public Task ConvertRowAsync(
            DataRow row, 
            List<ConvertedReading> readings, 
            Domain.Entities.Instrument instrument,
            Domain.Entities.Reading referentialReading)
        {
            var reading = new ConvertedReading()
            {
                Instrument = instrument.Convert()
            };

            readings.Add(reading);

            var readingValueRequest = new ReadingValueRequest
            {
                Date = row[1].ToString().ToUtcDateTime(),
                Dry = row[5].ToString().ToBool()
            };

            reading.Values.Add(readingValueRequest);

            if ((bool)readingValueRequest.Dry)
            {
                return Task.CompletedTask;
            }

            var quotaHasBeenConverted = decimal.TryParse(row[2].ToString(), out decimal quota);
            var depthHasBeenConverted = decimal.TryParse(row[3].ToString(), out decimal depth);
            var pressureHasBeenConverted = decimal.TryParse(row[4].ToString(), out decimal pressure);

            if (!quotaHasBeenConverted
                && !depthHasBeenConverted
                && !pressureHasBeenConverted
                && (!instrument.TopQuota.HasValue || !instrument.BaseQuota.HasValue))
            {
                readingValueRequest.Quota = 0;
                return Task.CompletedTask;
            }

            if (quotaHasBeenConverted)
            {
                quota = Math.Round(quota, ReadingValue.QuotaDecimalPlaces);
                (depth, pressure) = ReadingValue.CalculateDepthAndPressure(quota, instrument.TopQuota.Value, instrument.BaseQuota.Value);
            }
            else if (depthHasBeenConverted)
            {
                depth = Math.Round(depth, ReadingValue.QuotaDecimalPlaces);
                (quota, pressure) = ReadingValue.CalculateQuotaAndPressure(depth, instrument.TopQuota.Value, instrument.BaseQuota.Value);
            }
            else
            {
                pressure = Math.Round(pressure, ReadingValue.QuotaDecimalPlaces);
                (quota, depth) = ReadingValue.CalculateQuotaAndDepth(pressure, instrument.TopQuota.Value, instrument.BaseQuota.Value);
            }

            readingValueRequest.Quota = quota;
            readingValueRequest.Depth = depth;
            readingValueRequest.Pressure = pressure;

            return Task.CompletedTask;
        }
    }
}
