using Application.Extensions;
using Database.Repositories.Nature;
using Model.Reading._Shared.ConvertReadings;
using Model.Reading._Shared.ReadingValue;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Domain.Entities;
using Domain.Extensions;

namespace Application.Reading.ConvertFileToAdd.Strategy.Behavior
{
    public class GeophoneReadingConverter : IReadingConverter
    {
        private readonly INatureRepository _natureRepository;

        public GeophoneReadingConverter(INatureRepository natureRepository)
        {
            _natureRepository = natureRepository;
        }

        public async Task ConvertRowAsync(
            DataRow row, 
            List<ConvertedReading> readings, 
            Domain.Entities.Instrument instrument,
            Domain.Entities.Reading referentialReading)
        {
            var reading = new ConvertedReading()
            {
                Instrument = instrument.Convert()
            };

            readings.Add(reading);

            var readingValueRequest = new ReadingValueRequest
            {
                Date = row[1].ToString().ToUtcDateTime(),
                AAxisPga = decimal.TryParse(row[3].ToString(), out decimal aAxisPga) ? aAxisPga : 0,
                BAxisPga = decimal.TryParse(row[4].ToString(), out decimal bAxisPga) ? bAxisPga : 0,
                ZAxisPga = decimal.TryParse(row[5].ToString(), out decimal zAxisPga) ? zAxisPga : 0,
                EastCoordinate = decimal.TryParse(row[6].ToString(), out decimal eastCoordinate) ? Math.Round(eastCoordinate, ReadingValue.UtmCoordinatesDecimalPlaces) : 0,
                NorthCoordinate = decimal.TryParse(row[7].ToString(), out decimal northCoordinate) ? Math.Round(northCoordinate, ReadingValue.UtmCoordinatesDecimalPlaces) : 0
            };

            var natureDescription = string.IsNullOrEmpty(row[2].ToString()) ? "Desconhecido" : row[2].ToString();

            var nature = await _natureRepository
                .GetByDescriptionAsync(natureDescription, instrument.Structure.Id);

            if (nature == null)
            {
                nature = new Domain.Entities.Nature()
                {
                    Id = Guid.NewGuid(),
                    Description = natureDescription,
                    Structure = new()
                    {
                        Id = instrument.Structure.Id
                    }
                };

                await _natureRepository.AddAsync(nature);
            }

            readingValueRequest.Nature = new()
            {
                Id = nature.Id,
                Description = nature.Description
            };

            reading.Values.Add(readingValueRequest);
        }
    }
}
