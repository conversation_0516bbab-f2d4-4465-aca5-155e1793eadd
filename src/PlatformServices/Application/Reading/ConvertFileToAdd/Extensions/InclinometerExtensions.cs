using Model.Reading._Shared.ConvertReadings;
using System.Linq;
using Domain.Entities;

namespace Application.Reading.ConvertFileToAdd.Extensions
{
    internal static class InclinometerExtensions
    {
        internal static void CalculateInclinometerAccumulatedValues(
            this ConvertedReading reading)
        {
            reading.Values = reading.Values.OrderByDescending(value => value.Depth).ToList();

            var index = 0;
            foreach (var value in reading.Values)
            {
                var isFirstItem = index == 0;

                var previousValue = isFirstItem
                    ? null
                    : reading.Values[index - 1];

                var previousAccumulatedDisplacementA = isFirstItem
                    ? 0
                    : previousValue.AccumulatedDisplacementA.Value;

                var previousAccumulatedDisplacementB = isFirstItem
                    ? 0
                    : previousValue.AccumulatedDisplacementB.Value;

                value.AccumulatedDisplacementA = ReadingValue.CalculateAccumulatedDisplacement(value.AverageDisplacementA.Value, previousAccumulatedDisplacementA);
                value.AccumulatedDisplacementB = ReadingValue.CalculateAccumulatedDisplacement(value.AverageDisplacementB.Value, previousAccumulatedDisplacementB);

                index++;
            }
        }

        internal static void CalculateDeviation(
            this ConvertedReading reading,
            Domain.Entities.Reading referentialReading)
        {
            reading.Values = reading.Values.OrderByDescending(value => value.Depth).ToList();

            var index = 0;
            foreach (var value in reading.Values)
            {
                var referentialValue = referentialReading?.Values?.Find(x => x.Measurement.Id == value.Measurement.Id);

                if (referentialValue == null)
                {
                    value.DeviationA = 0;
                    value.DeviationB = 0;
                }
                else
                {
                    value.DeviationA = ReadingValue.CalculateDeviation(value.AccumulatedDisplacementA, referentialValue.AccumulatedDisplacementA);
                    value.DeviationB = ReadingValue.CalculateDeviation(value.AccumulatedDisplacementB, referentialValue.AccumulatedDisplacementB);
                }

                index++;
            }
        }
    }
}
