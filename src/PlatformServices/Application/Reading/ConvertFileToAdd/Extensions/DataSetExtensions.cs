using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Application.Extensions;
using Application.Reading.ConvertFileToAdd.Strategy;
using Application.Reading.Extensions;
using ClosedXML.Excel;
using Database.Repositories.Nature;
using Domain.Extensions;
using Model.Reading._Shared.ConvertReadings;
using static Application.Reading.GetTemplateFile.TemplateHeaders;

namespace Application.Reading.ConvertFileToAdd.Extensions;

public static class DataSetExtensions
{
    /// <summary>
    /// Extracts unique instrument identifiers from the first table in the provided <see cref="DataSet"/>.
    /// </summary>
    /// <param name="dataSet">The <see cref="DataSet"/> containing the table to extract identifiers from.</param>
    /// <returns>An <see cref="HashSet{T}"/> of unique instrument identifiers.</returns>
    /// <remarks>
    /// This method processes the rows in the first table of the <see cref="DataSet"/> and retrieves the values
    /// from the first column, skipping the header row. Only non-empty and distinct values are returned.
    /// </remarks>
    public static HashSet<string> GetUniqueInstrumentIdentifiers(
        this DataSet dataSet)
    {
        return dataSet.Tables[0].Rows
            .Cast<DataRow>()
            .Select(row => row[0].ToString())
            .Where(identifier => !string.IsNullOrEmpty(identifier))
            .Distinct()
            .ToHashSet();
    }

    /// <summary>
    /// Validates the rows in the first table of the provided <see cref="DataSet"/> against a list of instruments.
    /// </summary>
    /// <param name="dataSet">The <see cref="DataSet"/> containing the rows to validate.</param>
    /// <param name="instruments">A list of <see cref="Domain.Entities.Instrument"/> objects to validate the rows against.</param>
    /// <returns>
    /// <c>true</c> if all rows are valid; otherwise, <c>false</c>.
    /// </returns>
    /// <remarks>
    /// If a row is invalid, an error message is added to the "Errors" column of the row. The method uses the <see cref="AddErrors"/> helper method to add error messages.
    /// Date validation accepts only pt-BR format (dd/MM/yyyy) with optional time component.
    /// </remarks>
    public static bool ValidateRows(
        this DataSet dataSet,
        List<Domain.Entities.Instrument> instruments)
    {
        var isValid = true;

        dataSet.Tables[0].Rows
            .Cast<DataRow>()
            .ToList()
            .ForEach(row =>
            {
                var createdDate = row[Names.DateAndTime].ToString();

                if (string.IsNullOrEmpty(createdDate) ||
                    createdDate.HasOnlyNumbers() ||
                    !createdDate.IsValidInputDate())
                {
                    isValid = false;
                    row.AddErrors("Invalid date format.");
                }

                var identifier = row[0].ToString();

                if (string.IsNullOrEmpty(identifier))
                {
                    isValid = false;
                    row.AddErrors(
                        "The readings must contain the instrument identifier.");
                    return;
                }

                var instrument = instruments.FirstOrDefault(i =>
                    i.Identifier.Equals(identifier,
                        StringComparison.OrdinalIgnoreCase));

                if (instrument == null)
                {
                    isValid = false;
                    row.AddErrors(
                        $"The instrument with identifier {identifier} was not found in the informed structure.");
                }
            });

        dataSet.AcceptChanges();

        return isValid;
    }

    /// <summary>
    /// Validates the rows in the first table of the provided <see cref="DataSet"/> against the given list of instruments.
    /// </summary>
    /// <param name="dataSet">The <see cref="DataSet"/> containing the rows to be validated.</param>
    /// <param name="instruments">The list of instruments to validate the rows against.</param>
    /// <returns><c>true</c> if all rows are valid; otherwise, <c>false</c>.</returns>
    /// <remarks>
    /// This method checks each row in the first table of the <see cref="DataSet"/> to ensure that the instrument identifier
    /// exists and matches an instrument in the provided list. If a row is invalid, an error message is added to the "Errors" column.
    /// </remarks>
    public static async
        Task<(bool IsSuccessful, List<ConvertedReading> Readings)>
        TryParseAsync(
            this DataSet dataSet,
            List<Domain.Entities.Instrument> instruments,
            List<Domain.Entities.Reading> referentialReadings,
            INatureRepository natureRepository
        )
    {
        var readings = new List<ConvertedReading>();
        var isSuccessful = true;

        foreach (DataRow row in dataSet.Tables[0].Rows)
        {
            var identifier = row[0].ToString();

            var instrument = instruments.FirstOrDefault(i =>
                i.Identifier.Equals(identifier,
                    StringComparison.OrdinalIgnoreCase));

            try
            {
                var refReadingFromRequest = readings
                    .FirstOrDefault(item =>
                        item.Instrument.Id == instrument!.Id &&
                        item.IsReferential == true)?
                    .ConvertToEntity(instrument);
                
                var refReadingFromDatabase = referentialReadings
                    .FirstOrDefault(r => r.Instrument.Id == instrument!.Id);

                var referentialReading = refReadingFromRequest ??
                                         refReadingFromDatabase;

                var readingStrategy =
                    ReadingConverterFactory.GetStrategy(
                        instrument!.Type, natureRepository);
                
                await readingStrategy.ConvertRowAsync(row, readings,
                    instrument, referentialReading);
            }
            catch (Exception e)
            {
                isSuccessful = false;
                row.AddErrors(e.Message);
            }
        }

        return (isSuccessful, readings);
    }

    /// <summary>
    /// Adds an error message to the specified <see cref="DataRow"/> in the "Errors" column.
    /// </summary>
    /// <param name="row">The <see cref="DataRow"/> to which the error message will be added.</param>
    /// <param name="errorMessage">The error message to add to the row.</param>
    /// <remarks>
    /// This method sets the value of the last column in the <see cref="DataRow"/> (assumed to be the "Errors" column)
    /// to the provided error message. If the column already contains a value, it will be overwritten.
    /// </remarks>
    private static void AddErrors(
        this DataRow row,
        string errorMessage)
    {
        row.SetField<string>(row.Table.Columns.Count - 1, errorMessage);
    }

    /// <summary>
    /// Converts the first table in the provided <see cref="DataSet"/> to an Excel format.
    /// </summary>
    /// <param name="dataSet">The <see cref="DataSet"/> containing the table to be converted.</param>
    /// <returns>A byte array representing the Excel file content.</returns>
    /// <remarks>
    /// The method processes the first table in the <see cref="DataSet"/> and writes its content
    /// to an Excel file format. Each column in the table is treated as a header, and each row is
    /// written as a row in the Excel file. The headers are styled with bold text, a green background,
    /// and white font color.
    /// </remarks>
    public static byte[] ToExcel(this DataSet dataSet)
    {
        using var workbook = new XLWorkbook();

        var table = dataSet.Tables[0];
        var worksheet = workbook.Worksheets.Add(table.TableName);

        worksheet.Row(1).Style
            .Font.SetBold(true)
            .Font.SetFontSize(12)
            .Fill.SetBackgroundColor(XLColor.DarkGreen)
            .Font.SetFontColor(XLColor.White);

        for (var col = 0; col < table.Columns.Count; col++)
        {
            worksheet.Cell(1, col + 1).Value =
                table.Columns[col].ColumnName;
        }

        for (var row = 0; row < table.Rows.Count; row++)
        {
            for (var col = 0; col < table.Columns.Count; col++)
            {
                worksheet.Cell(row + 2, col + 1).Value =
                    table.Rows[row][col].ToString();
            }
        }

        return workbook.SaveAsBytes();
    }

    /// <summary>
    /// Converts the first table in the provided <see cref="DataSet"/> to a CSV format.
    /// </summary>
    /// <param name="dataSet">The <see cref="DataSet"/> containing the table to be converted.</param>
    /// <returns>A byte array representing the CSV file content.</returns>
    /// <remarks>
    /// The method processes the first table in the <see cref="DataSet"/> and writes its content
    /// to a CSV file format. Each column in the table is treated as a header, and each row is
    /// written as a line in the CSV file. Values are separated by semicolons (';').
    /// </remarks>
    public static byte[] ToCsv(this DataSet dataSet)
    {
        var table = dataSet.Tables[0];
        var headers = new string[table.Columns.Count];

        using var memoryStream = new MemoryStream();
        using var writer = new StreamWriter(memoryStream);

        for (var i = 0; i < table.Columns.Count; i++)
        {
            headers[i] = table.Columns[i].ColumnName;
        }

        writer.WriteLine(string.Join(";", headers));

        foreach (DataRow row in table.Rows)
        {
            var values = new string[table.Columns.Count];
            for (var i = 0; i < table.Columns.Count; i++)
            {
                values[i] = row[i]?.ToString() ?? "";
            }

            writer.WriteLine(string.Join(";", values));
        }

        writer.Close();

        return memoryStream.ToArray();
    }
}
