using System;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Application.Core;
using Application.Extensions;
using Application.Reading.ConvertFileToAdd.Extensions;
using Database.Repositories.Instrument;
using Database.Repositories.Nature;
using Database.Repositories.Reading;
using Database.Repositories.User;
using Domain.Core;
using ExcelDataReader;
using Model.Reading.Convert.Request;
using Model.Reading.Convert.Response;
using Model.Reading.GetTemplateFile.Response;
using static Application.Core.UseCaseResponseFactory<
    Model.Reading.Convert.Response.ConvertReadingsResponse>;

namespace Application.Reading.ConvertFileToAdd
{
    public sealed class
        ConvertFileToAddReadingUseCase : IConvertFileToAddReadingUseCase
    {
        private readonly IUserRepository _userRepository;
        private readonly IInstrumentRepository _instrumentRepository;
        private readonly IReadingRepository _readingRepository;
        private readonly INatureRepository _natureRepository;

        private readonly ConvertReadingsRequestValidator _requestValidator =
            new();

        public ConvertFileToAddReadingUseCase(
            IUserRepository userRepository,
            IInstrumentRepository instrumentRepository,
            IReadingRepository readingRepository,
            INatureRepository natureRepository)
        {
            _userRepository = userRepository;
            _instrumentRepository = instrumentRepository;
            _readingRepository = readingRepository;
            _natureRepository = natureRepository;
        }

        public async Task<UseCaseResponse<ConvertReadingsResponse>> Execute(
            ConvertReadingsRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest(null);
                }

                await request.AddRequesterStructures(_userRepository);

                var validationResult = await _requestValidator
                    .ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    return BadRequest(null,
                        validationResult.Errors.ToErrorMessages());
                }

                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                Thread.CurrentThread.ChangeCulture(Locale.PtBr);

                await using var stream = request.File.OpenReadStream();

                using var reader = CreateDataReader(stream, request.File.GetExtension());
                
                var dataSet = reader.AsDataSet(new()
                {
                    UseColumnDataType = true,
                    ConfigureDataTable = _ => new()
                    {
                        UseHeaderRow = true,
                        FilterRow = rowReader => rowReader.Depth >= 0,
                        FilterColumn = (_, _) => true
                    }
                });

                dataSet.Tables[0].Columns.Add("Erros", typeof(string));
                dataSet.AcceptChanges();

                var identifiers = dataSet.GetUniqueInstrumentIdentifiers();

                var instruments = await _instrumentRepository
                    .GetByIdentifiersAsync(identifiers,
                        request.StructureId);

                var rowsAreValid = dataSet.ValidateRows(instruments);

                if (!rowsAreValid)
                {
                    return BadRequest(new()
                    {
                        Readings = null,
                        ErrorsFile = CreateErrorFile(dataSet,
                            request.File.GetExtension())
                    });
                }

                var refReadings = await _readingRepository
                    .GetReferentialsAsync(instruments.Select(i => i.Id));

                var (conversionIsSuccessful, result) =
                    await dataSet.TryParseAsync(instruments,
                        refReadings, _natureRepository);

                if (!conversionIsSuccessful)
                {
                    return BadRequest(new()
                    {
                        Readings = null,
                        ErrorsFile = CreateErrorFile(dataSet,
                            request.File.GetExtension())
                    });
                }

                if (result.Count == 0)
                {
                    return BadRequest(null, "000", "Falha ao ler o arquivo");
                }

                return Ok(new()
                {
                    Readings = result
                });
            }
            catch (Exception e)
            {
                return InternalServerError(null,
                    errors: e.ToErrorMessages("000"));
            }
        }

        private static IExcelDataReader CreateDataReader(
            Stream stream,
            string extension)
        {
            return extension.Equals(".csv")
                ? ExcelReaderFactory.CreateCsvReader(stream)
                : ExcelReaderFactory.CreateReader(stream);
        }

        private static GetReadingTemplateFileResponse CreateErrorFile(
            DataSet dataSet,
            string extension)
        {
            return extension.Equals(".csv")
                ? new GetReadingTemplateFileResponse
                {
                    FileName =
                        $"readings_with_errors_{DateTime.UtcNow:dd-MM-yyyy_HH-mm-ss}.csv",
                    ContentType = "text/csv",
                    Bytes = dataSet.ToCsv()
                }
                : new GetReadingTemplateFileResponse
                {
                    FileName =
                        $"readings_with_errors_{DateTime.UtcNow:dd-MM-yyyy_HH-mm-ss}.xlsx",
                    ContentType =
                        "application/x-vnd.oasis.opendocument.spreadsheet",
                    Bytes = dataSet.ToExcel()
                };
        }
    }
}