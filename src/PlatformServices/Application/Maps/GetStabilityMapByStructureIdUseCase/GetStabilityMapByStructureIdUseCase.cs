using Application.Core;
using Application.Extensions;
using Coordinate.Core.Classes;
using Database.Repositories.Instrument;
using Database.Repositories.Structure;
using Database.Repositories.User;
using Model.Maps._Shared.StabilityMapData;
using Model.Maps.GetStabilityMapByStructureId.Request;
using Model.Maps.GetStabilityMapByStructureId.Response;
using System;
using System.Linq;
using System.Threading.Tasks;
using static Application.Core.UseCaseResponseFactory<Model.Maps.GetStabilityMapByStructureId.Response.GetStabilityMapByStructureIdResponse>;

namespace Application.Maps.GetStabilityMapByStructureIdUseCase
{
    public sealed class GetStabilityMapByStructureIdUseCase : IGetStabilityMapByStructureIdUseCase
    {

        private readonly IStructureRepository _structureRepository;
        private readonly IInstrumentRepository _instrumentRepository;
        private readonly IUserRepository _userRepository;
        private readonly GetStabilityMapByStructureIdRequestValidator _requestValidator = new();

        public GetStabilityMapByStructureIdUseCase(
            IStructureRepository structureRepository,
            IInstrumentRepository instrumentRepository,
            IUserRepository userRepository
        )
        {
            _structureRepository = structureRepository;
            _instrumentRepository = instrumentRepository;
            _userRepository = userRepository;
        }

        public async Task<UseCaseResponse<GetStabilityMapByStructureIdResponse>> Execute(GetStabilityMapByStructureIdRequest request)
        {

            try
            {
                if (request == null)
                {
                    return BadRequest(null, "000", "Request cannot be null.");
                }

                await request.AddRequesterStructures(_userRepository);

                var validationResult = await _requestValidator.ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    return BadRequest(null,
                        validationResult.Errors.ToErrorMessages());
                }

                if (!request.RequestedBySuperSupport && !request.RequestedUserStructures.Exists(x => x == request.StructureId))
                {
                    return Forbidden(null, "000", "You don't have permission to access this instrument.");
                }

                var instruments = await _instrumentRepository.GetStabilityMapInstrumentsData(request);

                if (!instruments.Any())
                {
                    return NoContent();
                }

                var structure = await _structureRepository.GetStructureStabilityMapData(request.StructureId);

                var response = new GetStabilityMapByStructureIdResponse
                {
                    Instruments = instruments,
                    StructureMapData = Map(structure)
                };

                return Ok(response);
            }
            catch (Exception e)
            {
                return InternalServerError(null, errors: e.ToErrorMessages("000"));
            }


        }

        private static StabilityMapData Map(Domain.Entities.Structure structure)
        {
            return new()
            {
                StructureId = structure.Id,
                Name = structure.Name,
                DecimalGeodetic = structure.CoordinateSetting.Systems.DecimalGeodetic,
                MapConfiguration = new()
                {
                    GeneralZoom = structure.MapConfiguration.GeneralZoom,
                    InstrumentsZoom = structure.MapConfiguration.InstrumentsZoom
                },
                Sections = structure.Sections.Select(x => new Model._Shared.Section.SectionMap()
                {
                    Id = x.Id,
                    Name = x.Name,
                    Coordinates = new Model.Section._Shared.SectionCoordinates.SectionCoordinates()
                    {
                        Datum = x.Coordinates.Datum,
                        UpstreamCoordinateSetting = new()
                        {
                            CoordinateFormat = x.Coordinates.UpstreamCoordinateSetting.Format,
                            CoordinateSystems = GetCoordinateSystems(x.Coordinates.UpstreamCoordinateSetting)
                        },
                        DownstreamCoordinateSetting = new()
                        {
                            CoordinateFormat = x.Coordinates.DownstreamCoordinateSetting.Format,
                            CoordinateSystems = GetCoordinateSystems(x.Coordinates.DownstreamCoordinateSetting)
                        },
                        MidpointCoordinateSetting = x.Coordinates.MidpointCoordinateSetting == null
                        ? null
                        : new()
                        {
                            CoordinateFormat = x.Coordinates.MidpointCoordinateSetting.Format,
                            CoordinateSystems = GetCoordinateSystems(x.Coordinates.MidpointCoordinateSetting)
                        }
                    },
                    MapLineSetting = new()
                    {
                        Color = x.MapLineSetting.Color,
                        Type = x.MapLineSetting.Type,
                        Width = x.MapLineSetting.Width
                    }
                })
                 .OrderBy(x => x.Name)
                 .ToList(),
                StabilityMapConfiguration = structure.StabilityMapConfiguration == null
                ? null
                : new ()
                {
                    Id = structure.StabilityMapConfiguration.Id,
                    PiezomenterColor = structure.StabilityMapConfiguration.PiezometerColor,
                    WaterLevelIndicatorColor = structure.StabilityMapConfiguration.WaterLevelIndicatorColor
                }
            };

        }

        private static Systems GetCoordinateSystems(Domain.Entities.SectionCoordinateSetting coordinateSetting)
        {
            return new Systems()
            {
                DecimalGeodetic = new DecimalGeodetic()
                {
                    Latitude = coordinateSetting.CoordinateSystems.DecimalGeodetic.Latitude,
                    Longitude = coordinateSetting.CoordinateSystems.DecimalGeodetic.Longitude
                },
                Utm = new Utm()
                {
                    Easting = coordinateSetting.CoordinateSystems.Utm.Easting,
                    Northing = coordinateSetting.CoordinateSystems.Utm.Northing,
                    ZoneNumber = coordinateSetting.CoordinateSystems.Utm.ZoneNumber,
                    ZoneLetter = coordinateSetting.CoordinateSystems.Utm.ZoneLetter
                }
            };
        }
    }
}
