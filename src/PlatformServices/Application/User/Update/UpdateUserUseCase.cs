using Application.Apis.Keycloak;
using Application.Core;
using Application.Extensions;
using Application.Extensions.Comparer;
using Database.Repositories.Client;
using Database.Repositories.ClientUnit;
using Database.Repositories.User;
using Domain.Enums;
using Domain.Extensions;
using Domain.ValueObjects;
using KellermanSoftware.CompareNetObjects;
using Model.User.Update.Request;
using System;
using System.Linq;
using System.Threading.Tasks;
using static Application.Core.UseCaseResponseFactory<System.Guid>;
using UpdateUserRequest = Model.User.Update.Request.UpdateUserRequest;

namespace Application.User.Update
{
    public sealed class UpdateUserUseCase : IUpdateUserUseCase
    {
        private readonly IUserRepository _userRepository;
        private readonly IClientRepository _clientRepository;
        private readonly IClientUnitRepository _clientUnitRepository;
        private readonly IKeycloakApiService _keycloakApiService;
        private readonly UpdateUserRequestValidator _requestValidator = new();

        public UpdateUserUseCase(
            IUserRepository userRepository,
            IKeycloakApiService keycloakApiService,
            IClientRepository clientRepository,
            IClientUnitRepository clientUnitRepository)
        {
            _userRepository = userRepository;
            _keycloakApiService = keycloakApiService;
            _clientRepository = clientRepository;
            _clientUnitRepository = clientUnitRepository;
        }

        public async Task<UseCaseResponse<Guid>> Execute(UpdateUserRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest(
                        Guid.Empty, 
                        "000",
                        "Request cannot be empty.");
                }

                await request.AddRequesterClients(_userRepository);

                var validationResult = await _requestValidator
                   .ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    return BadRequest(
                        Guid.Empty, 
                        validationResult.Errors.ToErrorMessages());
                }

                var user = await _userRepository
                    .GetAsync(request.Id);

                if (user == null)
                {
                    return BadRequest(
                        Guid.Empty, 
                        "000",
                        "User not found on database.");
                }

                if (!AllowedToRequest(request, user))
                {
                    return BadRequest(
                        Guid.Empty, 
                        "000",
                        "You cannot edit this user.");
                }

                if (!user.CanUserBeModifiedBasedOnType())
                {
                    return BadRequest(
                        Guid.Empty, 
                        "000",
                        "It is not possible to modify the user. " +
                        "It is used internally by the Workflows API.");
                }

                var usernameExists = await _userRepository.UsernameExists(request.Username, request.Id);

                if (usernameExists)
                {
                    return BadRequest(
                        Guid.Empty, 
                        "000",
                        "Username already exists on database.");
                }

                var emailExists = await _userRepository.EmailExists(request.EmailAddress, request.Id);

                if (emailExists)
                {
                    return BadRequest(
                        Guid.Empty, 
                        "000",
                        "Email already exists on database.");
                }

                if (request.Role != Domain.Enums.Role.SuperSupport)
                {
                    var clientUnitIds = request.ClientUnits.Select(x => x.Id);
                    var dbClientUnits = await _clientUnitRepository
                        .GetByStructureIdsAsync(request.Structures.Select(x => x.Id));

                    var differenceDbClientUnitListAndRequest = dbClientUnits.Except(clientUnitIds);

                    if (differenceDbClientUnitListAndRequest.Any())
                    {
                        return BadRequest(
                            Guid.Empty, 
                            "000",
                            "There are structures that do not have selected client units.");
                    }

                    var differenceRequestClientUnitAndDb = clientUnitIds.Except(dbClientUnits);

                    if (differenceRequestClientUnitAndDb.Any())
                    {
                        var clientUnitsName = await _clientUnitRepository.GetNameWithIds(differenceRequestClientUnitAndDb);

                        return BadRequest(
                            Guid.Empty, 
                            "StructuresNotSelectedError", 
                            string.Join(", ", clientUnitsName));
                    }

                    var clientIds = request.Clients.Select(x => x.Id);
                    var dbClients = await _clientRepository.GetIdsWithClientUnitIdsAsync(clientUnitIds);

                    var differenceDbClientListAndRequest = dbClients.Except(clientIds);

                    if (differenceDbClientListAndRequest.Any())
                    {
                        return BadRequest(
                            Guid.Empty, 
                            "000",
                            "There are units that do not have selected customers.");
                    }

                    var differenceRequestClientAndDb = clientIds.Except(dbClients);

                    if (differenceRequestClientAndDb.Any())
                    {
                        var clientsName = await _clientRepository.GetNamesWithIds(differenceRequestClientAndDb);

                        return BadRequest(
                            Guid.Empty, 
                            "ClientUnitsNotSelectedError", 
                            string.Join(", ", clientsName));
                    }
                }

                var currentRole = user.Role;
                var futureRole = request.Role;

                if (currentRole >= request.RequestedUserRole && !request.RequestedBySuperSupport)
                {
                    return BadRequest(
                        Guid.Empty, 
                        "000",
                        "The user's access level is greater than " +
                        "or equal to their profile's access level.");
                }

                var userRoleHasChanged = currentRole != futureRole;
                var userWasInactive = request.Active && !user.Active;

                if (HasNewEntities(user, request))
                {
                    var requestingUser = await _userRepository.GetAsync(request.RequestedBy);

                    if (!AllowedToChangeEntities(request, requestingUser))
                    {
                        return BadRequest(
                            Guid.Empty, 
                            "000",
                            "It is not possible to link clients, units " +
                            "or structures that are not associated with your profile.");
                    }
                }

                UpdateProperties(user, request);

                if (userRoleHasChanged)
                {
                    var availableRoles = await _keycloakApiService
                        .GetAvailableRoles(user.Id);

                    var futureKeycloakRole = availableRoles
                        .FirstOrDefault(r => r.Name == futureRole.GetDescription());

                    var bindedRoles = await _keycloakApiService
                        .GetBindedUserRoles(user.Id);
                    var currentKeycloakRole = bindedRoles
                        .FirstOrDefault(r => r.Name == currentRole.GetDescription());

                    if (availableRoles == null || futureKeycloakRole == null
                        || bindedRoles == null || currentKeycloakRole == null)
                    {
                        return BadRequest(
                            Guid.Empty, 
                            "000",
                            "Error fetching roles in keycloak.");
                    }

                    var unbindUserRoleResponse = await _keycloakApiService
                       .UnbindUserRole(user.Id, new()
                       {
                           new()
                           {
                               Id = currentKeycloakRole.Id,
                               Name = currentKeycloakRole.Name
                           }
                       });

                    if (!unbindUserRoleResponse.IsSuccessStatusCode)
                    {
                        return BadRequest(
                            Guid.Empty, 
                            "000",
                            "Error unbinding roles in keycloak.");
                    }

                    var bindUserRoleResponse = await _keycloakApiService
                        .BindUserRole(user.Id, new()
                        {
                            new()
                            {
                                Id = futureKeycloakRole.Id,
                                Name = futureKeycloakRole.Name
                            }
                        });

                    if (!bindUserRoleResponse.IsSuccessStatusCode)
                    {
                        return BadRequest(
                            Guid.Empty, 
                            "000",
                            "Error binding roles in keycloak.");
                    }
                }

                var keycloakResponse = await _keycloakApiService
                    .UpdateUser(Map(user, userWasInactive));

                if (!keycloakResponse.IsSuccessStatusCode)
                {
                    return BadRequest(
                        Guid.Empty, 
                        "000",
                        "An error occurred while updating the user in keycloak.");
                }

                await _userRepository.UpdateAsync(user);

                return Ok(user.Id);
            }
            catch (Exception e)
            {
                return InternalServerError(Guid.Empty, errors: e.ToErrorMessages("000"));
            }
        }

        private static bool AllowedToRequest(UpdateUserRequest request, Domain.Entities.User user)
        {
            if (request.RequestedBySuperSupport)
            {
                return true;
            }

            var haveSameClient = request.RequestedUserClients.First() == user.Clients.First().Id;
            var roleIsGreater = request.RequestedUserRole > user.Role;

            return roleIsGreater && haveSameClient;
        }

        private static bool AllowedToChangeEntities(
            UpdateUserRequest request,
            Domain.Entities.User requestingUser)
        {
            if (request.RequestedBySuperSupport)
            {
                return true;
            }

            var requestingUserHaveAllClientUnits = request.ClientUnits
                .All(x => requestingUser.ClientUnits.Select(c => c.Id).Contains(x.Id));

            var requestingUserHaveAllStructures = request.Structures
                .All(x => requestingUser.Structures.Select(c => c.Id).Contains(x.Id));

            return requestingUserHaveAllClientUnits && requestingUserHaveAllStructures;
        }

        private static bool HasNewEntities(Domain.Entities.User user, UpdateUserRequest request)
        {
            var clientUnits = request.ClientUnits.Select(x => new Domain.Entities.ClientUnit() { Id = x.Id }).ToList();
            var structures = request.Structures.Select(x => new Domain.Entities.Structure() { Id = x.Id }).ToList();

            return !user.Structures.All(c => structures.Contains(c)) || !user.ClientUnits.All(c => clientUnits.Contains(c));
        }

        private static void UpdateProperties(Domain.Entities.User user, UpdateUserRequest request)
        {
            var userClone = (Domain.Entities.User)user.Clone();

            var email = new Email { Value = request.EmailAddress };

            var clients = request.Clients.Select(x => new Domain.Entities.Client() { Id = x.Id }).ToList();
            var clientUnits = request.ClientUnits.Select(x => new Domain.Entities.ClientUnit() { Id = x.Id }).ToList();
            var structures = request.Structures.Select(x => new Domain.Entities.Structure() { Id = x.Id }).ToList();

            user.UpdateClients(clients);
            user.UpdateClientUnits(clientUnits);
            user.UpdateStructures(structures);
            user.EmailAddress = email;
            user.FirstName = request.FirstName;
            user.Surname = request.Surname;
            user.Username = request.Username;
            user.Role = request.Role;
            user.Locale = request.Locale;
            user.Active = request.Active;

            var compareLogic = new CompareLogic()
            {
                Config = new ComparisonConfig()
                {
                    IgnoreCollectionOrder = true,
                    MaxDifferences = typeof(Domain.Entities.User).GetProperties().Length,
                    CustomComparers = new()
                    {
                       new ClientListComparer(RootComparerFactory.GetRootComparer()),
                       new ClientUnitListComparer(RootComparerFactory.GetRootComparer()),
                       new StructureListComparer(RootComparerFactory.GetRootComparer())
                    }
                }
            };

            var propertiesChanged = compareLogic.GetChangedProperties(user, userClone);

            if (propertiesChanged.Any())
            {
                var history = new Domain.Entities.UserHistory()
                {
                    User = user,
                    ModifiedBy = new() { Id = request.RequestedBy },
                    Action = HistoryAction.Update
                };

                history.AddProperties(propertiesChanged);

                user.AddHistory(history);
            }
        }

        private static Apis.Keycloak.Request.UpdateUserRequest Map(Domain.Entities.User user, bool userWasInactive)
        {
            return new()
            {
                Id = user.Id,
                Email = user.EmailAddress.Value,
                Enabled = user.Active,
                FirstName = user.FirstName,
                LastName = user.Surname,
                Username = user.Username,
                RequiredActions = userWasInactive ? new() { RequiredAction.TermsAndConditions.GetDescription() } : default,
                Attributes = new()
                {
                    Locale = new()
                    {
                        user.Locale.GetDescription()
                    },
                    TermsAndConditions = new()
                    {
                        user.AcceptanceDateOfTerms != null
                        ? DateTimeOffset.Parse(user.AcceptanceDateOfTerms.Value.ToString("R"))
                            .ToUnixTimeSeconds().ToString()
                        : null
                    }
                }
            };
        }
    }
}
