using Application.Area.Add;
using Application.Area.GetById;
using Application.Area.List;
using Application.Area.Update;
using Application.Aspect.Add;
using Application.Aspect.GetById;
using Application.Aspect.List;
using Application.Aspect.Update;
using Application.Chart.AbsoluteVariation;
using Application.Chart.ClimateInstrument;
using Application.Chart.GetStabilityAnalysisData;
using Application.Chart.Inclinometer;
using Application.Chart.LinimetricRuler;
using Application.Chart.PercolationInstruments;
using Application.Chart.SettlementGauge;
using Application.Chart.SurfaceLandmarkOrPrism;
using Application.City.List;
using Application.Client.Add;
using Application.Client.Delete;
using Application.Client.GetById;
using Application.Client.GetClientAutomatedReadingsConfigurations;
using Application.Client.GetExpiring;
using Application.Client.GetLogo;
using Application.Client.List;
using Application.Client.Patch;
using Application.Client.Search;
using Application.Client.Update;
using Application.Client.UpsertClientAutomatedReadingsConfigurations;
using Application.ClientUnit.Add;
using Application.ClientUnit.Delete;
using Application.ClientUnit.GetById;
using Application.ClientUnit.List;
using Application.ClientUnit.Patch;
using Application.ClientUnit.Search;
using Application.ClientUnit.Update;
using Application.Country.List;
using Application.Dashboard.GetPercolationChart;
using Application.Dashboard.GetSectionDxf;
using Application.Dashboard.GetSectionsMap;
using Application.Dashboard.GetStabilityChart;
using Application.Image.Add;
using Application.Image.Delete;
using Application.Image.GetImageByIdAsBase64;
using Application.Image.Patch;
using Application.Image.Search;
using Application.InspectionSheet.Add;
using Application.InspectionSheet.AddOccurrenceAttachment;
using Application.InspectionSheet.Complete;
using Application.InspectionSheet.Delete;
using Application.InspectionSheet.DownloadVoiceNotesFile;
using Application.InspectionSheet.GetById;
using Application.InspectionSheet.GetLinkableOccurrences;
using Application.InspectionSheet.GetOccurrenceById;
using Application.InspectionSheet.Search;
using Application.InspectionSheet.SearchHistory;
using Application.InspectionSheet.SearchOccurrence;
using Application.InspectionSheet.Update;
using Application.Instrument.Add;
using Application.Instrument.AddGroup;
using Application.Instrument.AddNote;
using Application.Instrument.AddSecurityLevelAlert;
using Application.Instrument.ConvertFileToAdd;
using Application.Instrument.ConvertFileToUpdate;
using Application.Instrument.DeleteGroup;
using Application.Instrument.GetAdjacentReadingValues;
using Application.Instrument.GetAutomatedReadingsData;
using Application.Instrument.GetByFiltersMaps;
using Application.Instrument.GetById;
using Application.Instrument.GetFile;
using Application.Instrument.GetSection;
using Application.Instrument.GetTemplateFile;
using Application.Instrument.GetTotalRainfall;
using Application.Instrument.List;
using Application.Instrument.PatchHistory;
using Application.Instrument.Search;
using Application.Instrument.SearchGroup;
using Application.Instrument.SearchHistory;
using Application.Instrument.SearchNote;
using Application.Instrument.SearchNoteHistory;
using Application.Instrument.SearchSecurityLevelAlert;
using Application.Instrument.Update;
using Application.Instrument.UpdateGroup;
using Application.Instrument.UpdateNote;
using Application.Maps.GetDisplacementMap;
using Application.Maps.GetPercolationMap;
using Application.Maps.GetStabilityMapByStructureIdUseCase;
using Application.Maps.UpdateAbsoluteVariationColorsUseCase;
using Application.Maps.UpdateDisplacementMapConfigurationUseCase;
using Application.Maps.UpdateStabilityMapConfigurationUseCase;
using Application.Nature.Add;
using Application.Nature.List;
using Application.Nature.Update;
using Application.Notification.Add;
using Application.Notification.DeleteOld;
using Application.Notification.GetNotificationsByUserId;
using Application.Notification.GetUserNotificationConfiguration;
using Application.Notification.MarkAllNotifications;
using Application.Notification.MarkNotificationAsRead;
using Application.Notification.UpdateUserNotificationConfiguration;
using Application.Package.Add;
using Application.Package.Calculate;
using Application.Package.Delete;
using Application.Package.GetById;
using Application.Package.Patch;
using Application.Package.Search;
using Application.Package.UpdateStatus;
using Application.Position.Add;
using Application.Position.GetById;
using Application.Position.List;
using Application.Position.Search;
using Application.Position.Update;
using Application.Reading.Add;
using Application.Reading.AddBeachLength;
using Application.Reading.ConvertFileToAdd;
using Application.Reading.Delete;
using Application.Reading.GetBeachLengthById;
using Application.Reading.GetBeachLengthStats;
using Application.Reading.GetById;
using Application.Reading.GetReferential;
using Application.Reading.GetRelatedReadingValues;
using Application.Reading.GetStats;
using Application.Reading.GetTemplateFile;
using Application.Reading.Patch;
using Application.Reading.Search;
using Application.Reading.SearchBeachLength;
using Application.Reading.SearchBeachLengthHistory;
using Application.Reading.SearchHistory;
using Application.Reading.Update;
using Application.Reading.UpdateBeachLength;
using Application.Report.Add;
using Application.Report.Delete;
using Application.Report.GetById;
using Application.Report.GetData;
using Application.Report.GetPending;
using Application.Report.Search;
using Application.Report.Update;
using Application.Report.UpdateEmissionDates;
using Application.Responsible.Add;
using Application.Responsible.GetById;
using Application.Responsible.List;
using Application.Responsible.Update;
using Application.Role.Add;
using Application.Role.GetById;
using Application.Role.List;
using Application.Role.Search;
using Application.Role.Update;
using Application.Section.Add;
using Application.Section.AddSli;
using Application.Section.GetByFiltersMaps;
using Application.Section.GetById;
using Application.Section.GetInstruments;
using Application.Section.GetReview;
using Application.Section.GetReviewFile;
using Application.Section.GetWithSliById;
using Application.Section.List;
using Application.Section.Patch;
using Application.Section.PatchSectionsChartLineColor;
using Application.Section.Search;
using Application.Section.SearchHistory;
using Application.Section.TransformDxf;
using Application.Section.TransformMaterialDxf;
using Application.Section.Update;
using Application.Section.UpdateDxf;
using Application.Simulation.Add;
using Application.Simulation.Delete;
using Application.Simulation.DownloadAllFiles;
using Application.Simulation.DownloadFile;
using Application.Simulation.GetById;
using Application.Simulation.List;
using Application.Simulation.Patch;
using Application.Simulation.Search;
using Application.Simulation.Share;
using Application.Simulation.Update;
using Application.Simulation.UpdateStatus;
using Application.StabilityAnalysis.Add;
using Application.StabilityAnalysis.DownloadZipFile;
using Application.StabilityAnalysis.GetById;
using Application.StabilityAnalysis.GetByIdWithSafetyFactorsMetrics;
using Application.StabilityAnalysis.GetBySectionId;
using Application.StabilityAnalysis.GetFileBySafetyFactorId;
using Application.StabilityAnalysis.Search;
using Application.State.List;
using Application.StaticMaterial.Add;
using Application.StaticMaterial.GetById;
using Application.StaticMaterial.GetBySearchId;
using Application.StaticMaterial.GetReviewById;
using Application.StaticMaterial.List;
using Application.StaticMaterial.Patch;
using Application.StaticMaterial.SaveReview;
using Application.StaticMaterial.Search;
using Application.StaticMaterial.SearchHistory;
using Application.StaticMaterial.Update;
using Application.Structure.Add;
using Application.Structure.GetBasicInfo;
using Application.Structure.GetByFiltersMaps;
using Application.Structure.GetById;
using Application.Structure.GetCalculationMethods;
using Application.Structure.GetLayerFileById;
using Application.Structure.GetMapConfiguration;
using Application.Structure.GetSimulationData;
using Application.Structure.GetStabilityAnalysisDate;
using Application.Structure.GetStabilityAnalysisInfo;
using Application.Structure.GetSurfaceTypes;
using Application.Structure.List;
using Application.Structure.Patch;
using Application.Structure.Search;
using Application.Structure.Update;
using Application.StructureType.Add;
using Application.StructureType.Delete;
using Application.StructureType.GetById;
using Application.StructureType.List;
using Application.StructureType.Search;
using Application.StructureType.Update;
using Application.User.Add;
using Application.User.ExecuteActionsEmail;
using Application.User.GetById;
using Application.User.GetClients;
using Application.User.GetClientUnits;
using Application.User.GetEmail;
using Application.User.GetOwn;
using Application.User.GetStructures;
using Application.User.GetUser;
using Application.User.List;
using Application.User.Patch;
using Application.User.ReacceptTerms;
using Application.User.Search;
using Application.User.Update;
using Application.User.UpdateAttributes;
using Application.User.UpdatePassword;
using Application.User.UpdateSignedin;
using Microsoft.Extensions.DependencyInjection;
using Application.ActionPlan.Add;
using Application.ActionPlan.AddAttachment;
using Application.ActionPlan.Delete;
using Application.ActionPlan.DeleteAttachment;
using Application.ActionPlan.GetAttachments;
using Application.ActionPlan.GetById;
using Application.ActionPlan.Search;
using Application.ActionPlan.Update;
using Application.ActionPlan.SearchHistory;
using Application.Dashboard.GetInstrumentMetrics;
using Application.Dashboard.GetLatestUpdates;
using Application.Report.AddOccurrenceList;
using Application.Report.GetOccurrenceListById;
using Application.Report.SearchOccurrenceList;
using Application.Report.PatchOccurrenceList;
using Application.InspectionSheet.SearchOccurrenceUnpaginated;
using Application.Dashboard.GetStructureMap;
using Application.Instrument.GetByIds;
using Application.Notification.Exists;
using Application.Section.V2.AddConstructionStage;
using Application.Section.V2.AddReview;
using Application.Section.V2.GetDrawingFile;
using Application.Section.V2.UpdateConstructionStage;
using Application.Section.V2.UpdateGeneralInformation;
using Application.Section.V2.UpdateReview;
using Application.Section.V2.UpdateStabilityConfiguration;
using Application.SectionType.Add;
using Application.SectionType.Delete;
using Application.SectionType.GetById;
using Application.SectionType.List;
using Application.SectionType.Update;
using Application.Structure.SearchHistory;

namespace Application
{
    public static class UseCaseCfg
    {
        public static IServiceCollection AddUseCases(this IServiceCollection services)
        {
            return services
                .AddClientUseCases()
                .AddClientUnitUseCases()
                .AddCountryUseCases()
                .AddStateUseCases()
                .AddCityUseCases()
                .AddStructureTypeUseCases()
                .AddStructureUseCases()
                .AddImageUseCases()
                .AddPositionUseCases()
                .AddRoleUseCases()
                .AddAreaUseCases()
                .AddAspectUseCases()
                .AddSectionUseCases()
                .AddInstrumentUseCases()
                .AddNotificationUseCase()
                .AddUserUseCases()
                .AddStaticMaterialUseCases()
                .AddNatureUseCases()
                .AddResponsibleUseCases()
                .AddPackageUseCases()
                .AddChartUseCases()
                .AddReadingUseCases()
                .AddMapsUseCases()
                .AddReportUseCases()
                .AddSimulationUseCases()
                .AddInspectionSheetUseCases()
                .AddActionPlanUseCases()
                .AddDashboardUseCases()
                .AddStabilityAnalysisUseCases()
                .AddSectionTypeUseCases();
        }

        private static IServiceCollection AddDashboardUseCases(this IServiceCollection services)
        {
            return services
                .AddSingleton<IGetDashboardPercolationChartUseCase, GetDashboardPercolationChartUseCase>()
                .AddSingleton<IGetDashboardSectionDxfUseCase, GetDashboardSectionDxfUseCase>()
                .AddSingleton<IGetDashboardStabilityChartUseCase, GetDashboardStabilityChartUseCase>()
                .AddSingleton<IGetDashboardSectionsMapUseCase, GetDashboardSectionsMapUseCase>()
                .AddSingleton<IGetDashboardInstrumentMetricsUseCase, GetDashboardInstrumentMetricsUseCase>()
                .AddSingleton<IGetDashboardStructureMapUseCase, GetDashboardStructureMapUseCase>()
                .AddSingleton<IGetDashboardLatestUpdatesUseCase, GetDashboardLatestUpdatesUseCase>();
        }

        private static IServiceCollection AddActionPlanUseCases(this IServiceCollection services)
        {
            return services
                .AddSingleton<IAddActionPlanUseCase, AddActionPlanUseCase>()
                .AddSingleton<IGetActionPlanAttachmentsUseCase, GetActionPlanAttachmentsUseCase>()
                .AddSingleton<IAddActionPlanAttachmentUseCase, AddActionPlanAttachmentUseCase>()
                .AddSingleton<IGetActionPlanByIdUseCase, GetActionPlanByIdUseCase>()
                .AddSingleton<IUpdateActionPlanUseCase, UpdateActionPlanUseCase>()
                .AddSingleton<IDeleteActionPlanUseCase, DeleteActionPlanUseCase>()
                .AddSingleton<IDeleteActionPlanAttachmentUseCase, DeleteActionPlanAttachmentUseCase>()
                .AddSingleton<ISearchActionPlanHistoryUseCase, SearchActionPlanHistoryUseCase>()
                .AddSingleton<ISearchActionPlanUseCase, SearchActionPlanUseCase>();
        }

        private static IServiceCollection AddInspectionSheetUseCases(this IServiceCollection services)
        {
            return services
                .AddSingleton<IUpdateInspectionSheetUseCase, UpdateInspectionSheetUseCase>()
                .AddSingleton<IGetInspectionSheetByIdUseCase, GetInspectionSheetByIdUseCase>()
                .AddSingleton<ISearchInspectionSheetUseCase, SearchInspectionSheetUseCase>()
                .AddSingleton<IDeleteInspectionSheetUseCase, DeleteInspectionSheetUseCase>()
                .AddSingleton<IGetLinkableOccurrencesUseCase, GetLinkableOccurrencesUseCase>()
                .AddSingleton<IAddInspectionSheetUseCase, AddInspectionSheetUseCase>()
                .AddSingleton<IAddOccurrenceAttachmentUseCase, AddOccurrenceAttachmentUseCase>()
                .AddSingleton<IDownloadVoiceNotesFileUseCase, DownloadVoiceNotesFileUseCase>()
                .AddSingleton<ISearchInspectionSheetHistoryUseCase, SearchInspectionSheetHistoryUseCase>()
                .AddSingleton<ISearchOccurrenceUseCase, SearchOccurrenceUseCase>()
                .AddSingleton<ISearchOccurrenceUnpaginatedUseCase, SearchOccurrenceUnpaginatedUseCase>()
                .AddSingleton<ICompleteInspectionSheetUseCase, CompleteInspectionSheetUseCase>()
                .AddSingleton<IGetOccurrenceByIdUseCase, GetOccurrenceByIdUseCase>();
        }

        private static IServiceCollection AddSimulationUseCases(this IServiceCollection services)
        {
            return services
                .AddSingleton<IAddSimulationUseCase, AddSimulationUseCase>()    
                .AddSingleton<IGetSimulationByIdUseCase, GetSimulationByIdUseCase>()
                .AddSingleton<IShareSimulationUseCase, ShareSimulationUseCase>()
                .AddSingleton<IDeleteSimulationUseCase, DeleteSimulationUseCase>()
                .AddSingleton<IDownloadSimulationFileUseCase, DownloadSimulationFileUseCase>()
                .AddSingleton<IDownloadAllFilesUseCase, DownloadAllFilesUseCase>()
                .AddSingleton<IPatchSimulationUseCase, PatchSimulationUseCase>()
                .AddSingleton<IUpdateSimulationUseCase, UpdateSimulationUseCase>()
                .AddSingleton<IListSimulationUseCase, ListSimulationUseCase>()
                .AddSingleton<IUpdateSimulationStatusUseCase, UpdateSimulationStatusUseCase>()
                .AddSingleton<ISearchSimulationUseCase, SearchSimulationUseCase>();
        }

        private static IServiceCollection AddChartUseCases(this IServiceCollection services)
        {
            services.AddSingleton<IGetInclinometerDataUseCase, GetInclinometerDataUseCase>();
            services.AddSingleton<IGetClimateInstrumentDataUseCase, GetClimateInstrumentDataUseCase>();
            services.AddSingleton<IGetLinimetricRulerDataUseCase, GetLinimetricRulerDataUseCase>();
            services.AddSingleton<IGetSettlementGaugeDataUseCase, GetSettlementGaugeDataUseCase>();
            services.AddSingleton<IGetPercolationInstrumentDataUseCase, GetPercolationInstrumentDataUseCase>();
            services.AddSingleton<IGetAbsoluteVariationDataUseCase, GetAbsoluteVariationDataUseCase>();
            services.AddSingleton<IGetSurfaceLandmarkOrPrismDataUseCase, GetSurfaceLandmarkOrPrismDataUseCase>();
            services.AddSingleton<IGetStabilityAnalysisDataUseCase, GetStabilityAnalysisDataUseCase>();

            return services;
        }

        private static IServiceCollection AddReadingUseCases(this IServiceCollection services)
        {
            services.AddSingleton<IAddReadingUseCase, AddReadingUseCase>();
            services.AddSingleton<IGetReadingByIdUseCase, GetReadingByIdUseCase>();
            services.AddSingleton<ISearchReadingValueUseCase, SearchReadingValueUseCase>();
            services.AddSingleton<IDeleteReadingUseCase, DeleteReadingUseCase>();
            services.AddSingleton<IUpdateReadingUseCase, UpdateReadingUseCase>();
            services.AddSingleton<IPatchReadingValueUseCase, PatchReadingValueUseCase>();
            services.AddSingleton<IGetReferentialReadingValueUseCase, GetReferentialReadingValueUseCase>();
            services.AddSingleton<IGetReadingTemplateFileUseCase, GetReadingTemplateFileUseCase>();
            services.AddSingleton<IConvertFileToAddReadingUseCase, ConvertFileToAddReadingUseCase>();
            services.AddSingleton<ISearchReadingHistoryUseCase, SearchReadingHistoryUseCase>();
            services.AddSingleton<IAddBeachLengthUseCase, AddBeachLengthUseCase>();
            services.AddSingleton<IGetBeachLengthByIdUseCase, GetBeachLengthByIdUseCase>();
            services.AddSingleton<ISearchBeachLengthUseCase, SearchBeachLengthUseCase>();
            services.AddSingleton<IUpdateBeachLengthUseCase, UpdateBeachLengthUseCase>();
            services.AddSingleton<ISearchBeachLengthHistoryUseCase, SearchBeachLengthHistoryUseCase>();
            services.AddSingleton<ICalculateReadingStatsUseCase, CalculateReadingStatsUseCase>();
            services.AddSingleton<ICalculateBeachLengthStatsUseCase, CalculateBeachLengthStatsUseCase>();
            services.AddSingleton<IGetRelatedReadingValuesUseCase, GetRelatedReadingValuesUseCase>();

            return services;
        }

        private static IServiceCollection AddPackageUseCases(this IServiceCollection services)
        {
            services.AddSingleton<IAddPackageUseCase, AddPackageUseCase>();
            services.AddSingleton<ISearchPackageUseCase, SearchPackageUseCase>();
            services.AddSingleton<IGetPackageByIdUseCase, GetPackageByIdUseCase>();
            services.AddSingleton<IDeletePackageUseCase, DeletePackageUseCase>();
            services.AddSingleton<ICalculatePackageStabilityUseCase, CalculatePackageStabilityUseCase>();
            services.AddSingleton<IPatchPackageUseCase, PatchPackageUseCase>();
            services.AddSingleton<IUpdatePackageStatusUseCase, UpdatePackageStatusUseCase>();

            return services;
        }

        private static IServiceCollection AddNatureUseCases(this IServiceCollection services)
        {
            services.AddSingleton<IAddNatureUseCase, AddNatureUseCase>();
            services.AddSingleton<IUpdateNatureUseCase, UpdateNatureUseCase>();
            services.AddSingleton<IListNatureUseCase, ListNatureUseCase>();

            return services;
        }

        private static IServiceCollection AddStaticMaterialUseCases(this IServiceCollection services)
        {
            services.AddSingleton<IAddStaticMaterialUseCase, AddStaticMaterialUseCase>();
            services.AddSingleton<IGetStaticMaterialByIdUseCase, GetStaticMaterialByIdUseCase>();
            services.AddSingleton<IListStaticMaterialUseCase, ListStaticMaterialUseCase>();
            services.AddSingleton<ISearchStaticMaterialUseCase, SearchStaticMaterialUseCase>();
            services.AddSingleton<IUpdateStaticMaterialUseCase, UpdateStaticMaterialUseCase>();
            services.AddSingleton<IPatchStaticMaterialUseCase, PatchStaticMaterialUseCase>();
            services.AddSingleton<IGetStaticMaterialBySearchIdUseCase, GetStaticMaterialBySearchIdUseCase>();
            services.AddSingleton<ISaveStaticMaterialReviewUseCase, SaveStaticMaterialReviewUseCase>();
            services.AddSingleton<ISearchStaticMaterialHistoryUseCase, SearchStaticMaterialHistoryUseCase>();
            services.AddSingleton<IGetStaticMaterialReviewByIdUseCase, GetStaticMaterialReviewByIdUseCase>();

            return services;
        }

        private static IServiceCollection AddNotificationUseCase(this IServiceCollection services)
        {
            services.AddSingleton<IAddNotificationUseCase, AddNotificationUseCase>();
            services.AddSingleton<IDeleteOldNotificationsUseCase, DeleteOldNotificationsUseCase>();
            services.AddSingleton<IUpdateUserNotificationConfigurationUseCase, UpdateUserNotificationConfigurationUseCase>();
            services.AddSingleton<IGetUserNotificationConfigurationUseCase, GetUserNotificationConfigurationUseCase>();
            services.AddSingleton<IMarkNotificationAsReadUseCase, MarkNotificationAsReadUseCase>();
            services.AddSingleton<IGetNotificationsByUserIdUseCase, GetNotificationsByUserIdUseCase>();
            services.AddSingleton<IMarkAllUserNotificationsAsReadUseCase, MarkAllUserNotificationsAsReadUseCase>();
            services.AddSingleton<IDeleteOldNotificationsUseCase, DeleteOldNotificationsUseCase>();
            services.AddSingleton<IExistsNotificationUseCase, ExistsNotificationUseCase>();

            return services;
        }

        private static IServiceCollection AddUserUseCases(this IServiceCollection services)
        {
            services.AddSingleton<IAddUserUseCase, AddUserUseCase>();
            services.AddSingleton<IListUserUseCase, ListUserUseCase>();
            services.AddSingleton<IGetUserByIdUseCase, GetUserByIdUseCase>();
            services.AddSingleton<IPatchUserUseCase, PatchUserUseCase>();
            services.AddSingleton<IUpdateUserUseCase, UpdateUserUseCase>();
            services.AddSingleton<ISearchUserUseCase, SearchUserUseCase>();
            services.AddSingleton<IExecuteUserEmailActionsUseCase, ExecuteUserEmailActionsUseCase>();
            services.AddSingleton<IUpdateUserAttributesUseCase, UpdateUserAttributesUseCase>();
            services.AddSingleton<IUpdateSignedinUserUseCase, UpdateSignedinUserUseCase>();
            services.AddSingleton<IUsersReacceptTermsUseCase, UsersReacceptTermsUseCase>();
            services.AddSingleton<IUpdateUserPasswordUseCase, UpdateUserPasswordUseCase>();
            services.AddSingleton<IGetOwnUserUseCase, GetOwnUserUseCase>();
            services.AddSingleton<IGetUserClientsUseCase, GetUserClientsUseCase>();
            services.AddSingleton<IGetUserClientUnitsUseCase, GetUserClientUnitsUseCase>();
            services.AddSingleton<IGetUserStructuresUseCase, GetUserStructuresUseCase>();
            services.AddSingleton<IGetUserEmailUseCase, GetUserEmailUseCase>();
            services.AddSingleton<IGetUserHistoryUseCase, GetUserHistoryUseCase>();

            return services;
        }

        private static IServiceCollection AddInstrumentUseCases(this IServiceCollection services)
        {
            return services
                .AddScoped<IAddInstrumentUseCase, AddInstrumentUseCase>()
                .AddScoped<IGetInstrumentByIdUseCase, GetInstrumentByIdUseCase>()
                .AddScoped<IGetInstrumentsByIdsUseCase, GetInstrumentsByIdsUseCase>()
                .AddScoped<IListInstrumentUseCase, ListInstrumentUseCase>()
                .AddScoped<ISearchInstrumentUseCase, SearchInstrumentUseCase>()
                .AddScoped<IAddInstrumentNoteUseCase, AddInstrumentNoteUseCase>()
                .AddScoped<IUpdateInstrumentNoteUseCase, UpdateInstrumentNoteUseCase>()
                .AddScoped<ISearchInstrumentNoteUseCase, SearchInstrumentNoteUseCase>()
                .AddScoped<ISearchInstrumentNoteHistoryUseCase, SearchInstrumentNoteHistoryUseCase>()
                .AddScoped<ISearchInstrumentHistoryUseCase, SearchInstrumentHistoryUseCase>()
                .AddScoped<IPatchInstrumentHistoryUseCase, PatchInstrumentHistoryUseCase>()
                .AddScoped<IAddInstrumentGroupUseCase, AddInstrumentGroupUseCase>()
                .AddScoped<ISearchInstrumentGroupUseCase, SearchInstrumentGroupUseCase>()
                .AddScoped<IUpdateInstrumentGroupUseCase, UpdateInstrumentGroupUseCase>()
                .AddScoped<IDeleteInstrumentGroupUseCase, DeleteInstrumentGroupUseCase>()
                .AddScoped<IGetInstrumentByFiltersMapsUseCase, GetInstrumentByFiltersMapsUseCase>()
                .AddScoped<IConvertFileToAddInstrumentUseCase, ConvertFileToAddInstrumentUseCase>()
                .AddScoped<IConvertFileToUpdateInstrumentUseCase, ConvertFileToUpdateInstrumentUseCase>()
                .AddScoped<IGetInstrumentFileUseCase, GetInstrumentFileUseCase>()
                .AddScoped<IGetInstrumentTemplateFileUseCase, GetInstrumentTemplateFileUseCase>()
                .AddScoped<IGetInstrumentSectionUseCase, GetInstrumentSectionUseCase>()
                .AddScoped<IAddInstrumentSecurityLevelAlertUseCase, AddInstrumentSecurityLevelAlertUseCase>()
                .AddScoped<ISearchInstrumentSecurityLevelAlertUseCase, SearchInstrumentSecurityLevelAlertUseCase>()
                .AddScoped<IGetAdjacentReadingValuesUseCase, GetAdjacentReadingValuesUseCase>()
                .AddScoped<IGetInstrumentTotalRainfallUseCase, GetInstrumentTotalRainfallUseCase>()
                .AddScoped<IUpdateInstrumentUseCase, UpdateInstrumentUseCase>()
                .AddScoped<IGetAutomatedReadingsDataUseCase, GetAutomatedReadingsDataUseCase>();
        }

        private static IServiceCollection AddSectionUseCases(this IServiceCollection services)
        {
            return services.AddSingleton<IAddSectionUseCase, AddSectionUseCase>()
                .AddSingleton<IGetSectionByIdUseCase, GetSectionByIdUseCase>()
                .AddSingleton<IPatchSectionUseCase, PatchSectionUseCase>()
                .AddSingleton<IListSectionUseCase, ListSectionUseCase>()
                .AddSingleton<ISearchSectionUseCase, SearchSectionUseCase>()
                .AddSingleton<IGetSectionByFiltersMapsUseCase, GetSectionByFiltersMapsUseCase>()
                .AddSingleton<IGetSectionInstrumentsUseCase, GetSectionInstrumentsUseCase>()
                .AddSingleton<IGetSectionReviewFileUseCase, GetSectionReviewFileUseCase>()
                .AddSingleton<IAddSliFileUseCase, AddSliFileUseCase>()
                .AddSingleton<IGetSectionWithSliByIdUseCase, GetSectionWithSliByIdUseCase>()
                .AddSingleton<IUpdateSectionUseCase, UpdateSectionUseCase>()
                .AddSingleton<ITransformDxfUseCase, TransformDxfUseCase>()
                .AddSingleton<ITransformMaterialDxfUseCase, TransformMaterialDxfUseCase>()
                .AddSingleton<ISearchSectionHistoryUseCase, SearchSectionHistoryUseCase>()
                .AddSingleton<IGetSectionReviewUseCase, GetSectionReviewUseCase>()
                .AddSingleton<IUpdateSectionDxfUseCase, UpdateSectionDxfUseCase>()
                .AddSingleton<IPatchSectionsChartLineColorUseCase, PatchSectionsChartLineColorUseCase>()
                .AddSingleton<Application.Section.V2.Add.IAddSectionUseCase, 
                    Application.Section.V2.Add.AddSectionUseCase>()
                .AddSingleton<IUpdateGeneralInformationUseCase, UpdateGeneralInformationUseCase>()
                .AddSingleton<IUpdateStabilityConfigurationUseCase, UpdateStabilityConfigurationUseCase>()
                .AddSingleton<IAddSectionReviewUseCase, AddSectionReviewUseCase>()
                .AddSingleton<IUpdateSectionReviewUseCase, UpdateSectionReviewUseCase>()
                .AddSingleton<IAddConstructionStageUseCase, AddConstructionStageUseCase>()
                .AddSingleton<IGetSectionDrawingFileUseCase, GetSectionDrawingFileUseCase>()
                .AddSingleton<IUpdateConstructionStageUseCase, UpdateConstructionStageUseCase>()
                .AddSingleton<Application.Section.V2.GetById.IGetSectionByIdUseCase,
                    Application.Section.V2.GetById.GetSectionByIdUseCase>()
                .AddSingleton<Application.Section.V2.GetLengthData.IGetLengthDataUseCase,
                    Application.Section.V2.GetLengthData.GetLengthDataUseCase>();
        }

        private static IServiceCollection AddAspectUseCases(this IServiceCollection services)
        {
            return services.AddSingleton<IAddAspectUseCase, AddAspectUseCase>()
                .AddSingleton<IGetAspectByIdUseCase, GetAspectByIdUseCase>()
                .AddSingleton<IListAspectUseCase, ListAspectUseCase>()
                .AddSingleton<IUpdateAspectUseCase, UpdateAspectUseCase>();
        }

        private static IServiceCollection AddAreaUseCases(this IServiceCollection services)
        {
            return services.AddSingleton<IAddAreaUseCase, AddAreaUseCase>()
                .AddSingleton<IGetAreaByIdUseCase, GetAreaByIdUseCase>()
                .AddSingleton<IListAreaUseCase, ListAreaUseCase>()
                .AddSingleton<IUpdateAreaUseCase, UpdateAreaUseCase>();
        }

        private static IServiceCollection AddResponsibleUseCases(this IServiceCollection services)
        {
            return services.AddSingleton<IAddResponsibleUseCase, AddResponsibleUseCase>()
                .AddSingleton<IGetResponsibleByIdUseCase, GetResponsibleByIdUseCase>()
                .AddSingleton<IListResponsibleUseCase, ListResponsibleUseCase>()
                .AddSingleton<IUpdateResponsibleUseCase, UpdateResponsibleUseCase>();
        }

        private static IServiceCollection AddPositionUseCases(this IServiceCollection services)
        {
            return services.AddSingleton<IAddPositionUseCase, AddPositionUseCase>()
                .AddSingleton<IGetPositionByIdUseCase, GetPositionByIdUseCase>()
                .AddSingleton<IListPositionUseCase, ListPositionUseCase>()
                .AddSingleton<ISearchPositionUseCase, SearchPositionUseCase>()
                .AddSingleton<IUpdatePositionUseCase, UpdatePositionUseCase>();
        }

        private static IServiceCollection AddRoleUseCases(this IServiceCollection services)
        {
            return services.AddSingleton<IAddRoleUseCase, AddRoleUseCase>()
                .AddSingleton<IGetRoleByIdUseCase, GetRoleByIdUseCase>()
                .AddSingleton<IListRoleUseCase, ListRoleUseCase>()
                .AddSingleton<ISearchRoleUseCase, SearchRoleUseCase>()
                .AddSingleton<IUpdateRoleUseCase, UpdateRoleUseCase>();
        }

        private static IServiceCollection AddStructureUseCases(this IServiceCollection services)
        {
            return services
                .AddSingleton<IAddStructureUseCase, AddStructureUseCase>()
                .AddSingleton<IListStructureUseCase, ListStructureUseCase>()
                .AddSingleton<ISearchStructureUseCase, SearchStructureUseCase>()
                .AddSingleton<IGetStructureByIdUseCase, GetStructureByIdUseCase>()
                .AddSingleton<IUpdateStructureUseCase, UpdateStructureUseCase>()    
                .AddSingleton<IPatchStructureUseCase, PatchStructureUseCase>()
                .AddSingleton<IGetStructureMapConfigurationUseCase, GetStructureMapConfigurationUseCase>()
                .AddSingleton<IGetStructureByFiltersMapsUseCase, GetStructureByFiltersMapsUseCase>()
                .AddSingleton<IGetStructureStabilityAnalysisInfoUseCase, GetStructureStabilityAnalysisInfoUseCase>()
                .AddSingleton<IGetCalculationMethodsUseCase, GetCalculationMethodsUseCase>()
                .AddSingleton<IGetSurfaceTypesUseCase, GetSurfaceTypesUseCase>()
                .AddSingleton<IGetStructureSimulationDataUseCase, GetStructureSimulationDataUseCase>()
                .AddSingleton<IGetStructureBasicInfoUseCase, GetStructureBasicInfoUseCase>()
                .AddSingleton<IGetStabilityAnalysisDateUseCase, GetStabilityAnalysisDateUseCase>()
                .AddSingleton<IGetLayerFileByIdUseCase, GetLayerFileByIdUseCase>()
                .AddSingleton<ISearchStructureHistoryUseCase, SearchStructureHistoryUseCase>();
        }

        private static IServiceCollection AddImageUseCases(this IServiceCollection services)
        {
            return services
                .AddSingleton<IAddImagesFormdataUseCase, AddImagesFormdataUseCase>()
                .AddSingleton<IPatchImageUseCase, PatchImageUseCase>()
                .AddSingleton<IGetImageByIdAsBase64UseCase, GetImageByIdAsBase64UseCase>()
                .AddSingleton<ISearchImagesUseCase, SearchImagesUseCase>()
                .AddSingleton<IDeleteImageUseCase, DeleteImageUseCase>();
        }

        private static IServiceCollection AddStructureTypeUseCases(this IServiceCollection services)
        {
            return services.AddSingleton<IListStructureTypeUseCase, ListStructureTypeUseCase>()
                .AddSingleton<IAddStructureTypeUseCase, AddStructureTypeUseCase>()
                .AddSingleton<IGetStructureTypeByIdUseCase, GetStructureTypeByIdUseCase>()
                .AddSingleton<ISearchStructureTypeUseCase, SearchStructureTypeUseCase>()
                .AddSingleton<IUpdateStructureTypeUseCase, UpdateStructureTypeUseCase>()
                .AddSingleton<IDeleteStructureTypeUseCase, DeleteStructureTypeUseCase>();
        }

        private static IServiceCollection AddSectionTypeUseCases(this IServiceCollection services)
        {
            return services
                .AddSingleton<IListSectionTypeUseCase, ListSectionTypeUseCase>()
                .AddSingleton<IAddSectionTypeUseCase, AddSectionTypeUseCase>()
                .AddSingleton<IGetSectionTypeByIdUseCase, GetSectionTypeByIdUseCase>()
                .AddSingleton<IUpdateSectionTypeUseCase, UpdateSectionTypeUseCase>()
                .AddSingleton<IDeleteSectionTypeUseCase, DeleteSectionTypeUseCase>();
        }

        private static IServiceCollection AddClientUseCases(this IServiceCollection services)
        {
            return services.AddSingleton<IAddClientUseCase, AddClientUseCase>()
                .AddSingleton<IGetClientByIdUseCase, GetClientByIdUseCase>()
                .AddSingleton<IUpdateClientUseCase, UpdateClientUseCase>()
                .AddSingleton<IDeleteClientUseCase, DeleteClientUseCase>()
                .AddSingleton<ISearchClientUseCase, SearchClientUseCase>()
                .AddSingleton<IListClientUseCase, ListClientUseCase>()
                .AddSingleton<IPatchClientUseCase, PatchClientUseCase>()
                .AddSingleton<IGetExpiringClientsUseCase, GetExpiringClientsUseCase>()
                .AddSingleton<IGetClientLogoUseCase, GetClientLogoUseCase>()
                .AddSingleton<IGetClientAutomatedReadingsConfigurationsUseCase, GetClientAutomatedReadingsConfigurationsUseCase>()
                .AddSingleton<IUpsertClientAutomatedReadingsConfigurationsUseCase, UpsertClientAutomatedReadingsConfigurationsUseCase>();
        }

        private static IServiceCollection AddClientUnitUseCases(this IServiceCollection services)
        {
            return services.AddSingleton<IAddClientUnitUseCase, AddClientUnitUseCase>()
                .AddSingleton<ISearchClientUnitUseCase, SearchClientUnitUseCase>()
                .AddSingleton<IListClientUnitUseCase, ListClientUnitUseCase>()
                .AddSingleton<IGetClientUnitByIdUseCase, GetClientUnitByIdUseCase>()
                .AddSingleton<IUpdateClientUnitUseCase, UpdateClientUnitUseCase>()
                .AddSingleton<IDeleteClientUnitUseCase, DeleteClientUnitUseCase>()
                .AddSingleton<IPatchClientUnitUseCase, PatchClientUnitUseCase>();
        }

        public static IServiceCollection AddCountryUseCases(this IServiceCollection services)
        {
            return services.AddSingleton<IListCountryUseCase, ListCountryUseCase>();
        }

        public static IServiceCollection AddStateUseCases(this IServiceCollection services)
        {
            return services.AddSingleton<IListStateUseCase, ListStateUseCase>();
        }

        public static IServiceCollection AddCityUseCases(this IServiceCollection services)
        {
            return services.AddSingleton<IListCityUseCase, ListCityUseCase>();
        }

        public static IServiceCollection AddMapsUseCases(this IServiceCollection services)
        {
            return services.AddSingleton<IGetPercolationMapUseCase, GetPercolationMapUseCase>()
                .AddSingleton<IUpdateAbsoluteVariationColorsUseCase, UpdateAbsoluteVariationColorsUseCase>()
                .AddSingleton<IGetDisplacementMapUseCase, GetDisplacementMapUseCase>()
                .AddSingleton<IUpdateDisplacementMapConfigurationUseCase, UpdateDisplacementMapConfigurationUseCase>()
                .AddSingleton<IGetStabilityMapByStructureIdUseCase, GetStabilityMapByStructureIdUseCase>()
                .AddSingleton<IUpdateStabilityMapConfigurationUseCase, UpdateStabilityMapConfigurationUseCase>();
        }

        private static IServiceCollection AddReportUseCases(this IServiceCollection services)
        {
            return services
                .AddSingleton<IAddReportUseCase, AddReportUseCase>()
                .AddSingleton<IDeleteReportUseCase, DeleteReportUseCase>()
                .AddSingleton<IGetReportByIdUseCase, GetReportByIdUseCase>()
                .AddSingleton<IUpdateReportUseCase, UpdateReportUseCase>()
                .AddSingleton<IUpdateReportEmisisonDatesUseCase, UpdateReportEmisisonDatesUseCase>()
                .AddSingleton<IGetPendingReportsUseCase, GetPendingReportsUseCase>()
                .AddSingleton<ISearchReportUseCase, SearchReportUseCase>()
                .AddSingleton<IAddOccurrenceListReportUseCase, AddOccurrenceListReportUseCase>()
                .AddSingleton<IGetOccurrenceListReportByIdUseCase, GetOccurrenceListReportByIdUseCase>()
                .AddSingleton<ISearchOccurrenceListReportUseCase, SearchOccurrenceListReportUseCase>()
                .AddSingleton<IPatchOccurrenceListReportByIdUseCase, PatchOccurrenceListReportByIdUseCase>()
                .AddSingleton<IGetReportDataUseCase, GetReportDataUseCase>();
        }

        private static IServiceCollection AddStabilityAnalysisUseCases(this IServiceCollection services)
        {
            return services
                .AddSingleton<IAddStabilityAnalysisUseCase, AddStabilityAnalysisUseCase>()
                .AddSingleton<ISearchStabilityAnalysisUseCase, SearchStabilityAnalysisUseCase>()
                .AddSingleton<IGetStabilityAnalysisByIdUseCase, GetStabilityAnalysisByIdUseCase>()
                .AddSingleton<IGetStabilityAnalysisBySectionIdUseCase, GetStabilityAnalysisBySectionIdUseCase>()
                .AddSingleton<IGetFileBySafetyFactorIdUseCase, GetFileBySafetyFactorIdUseCase>()
                .AddSingleton<IGetStabilityAnalysisByIdWithSafetyFactorsMetricsUseCase, GetStabilityAnalysisByIdWithSafetyFactorsMetricsUseCase>()
                .AddSingleton<IDownloadStabilityAnalysisZipFileUseCase, DownloadStabilityAnalysisZipFileUseCase>();
        }
    }
}