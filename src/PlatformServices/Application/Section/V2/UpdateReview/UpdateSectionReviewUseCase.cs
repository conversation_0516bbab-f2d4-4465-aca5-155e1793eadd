using System;
using System.Linq;
using System.Threading.Tasks;
using Application.Core;
using Application.Extensions;
using Application.Section.Extensions;
using Application.Services.BlobStorage;
using Database.Repositories.Section;
using Database.Repositories.StaticMaterial;
using Database.Repositories.User;
using Model.Section.V2.UpdateReview.Request;
using static Application.Section.Constants;
using static Application.Core.UseCaseResponseFactory<System.Guid>;

namespace Application.Section.V2.UpdateReview;

public sealed class UpdateSectionReviewUseCase : IUpdateSectionReviewUseCase
{
    private readonly ISectionRepository _sectionRepository;
    private readonly IUserRepository _userRepository;
    private readonly IStaticMaterialRepository _staticMaterialRepository;
    private readonly IBlobStorageService _blobService;

    private readonly UpdateSectionReviewRequestValidator _requestValidator =
        new();

    public UpdateSectionReviewUseCase(
        ISectionRepository sectionRepository,
        IUserRepository userRepository,
        IStaticMaterialRepository staticMaterialRepository,
        IBlobStorageService blobService)
    {
        _sectionRepository = sectionRepository;
        _userRepository = userRepository;
        _staticMaterialRepository = staticMaterialRepository;
        _blobService = blobService;
    }

    public async Task<UseCaseResponse<Guid>> Execute(
        UpdateSectionReviewRequest request)
    {
        if (request is null)
        {
            return BadRequest(Guid.Empty);
        }

        try
        {
            await request.AddRequesterStructures(_userRepository);

            var validationResult = await _requestValidator
                .ValidateAsync(request);

            if (!validationResult.IsValid)
            {
                return BadRequest(
                    Guid.Empty,
                    validationResult.Errors.ToErrorMessages());
            }

            var section = await _sectionRepository.GetAsync(request.Section.Id);

            if (section is null)
            {
                return BadRequest(
                    Guid.Empty,
                    "000",
                    "Section not found in database.");
            }

            if (!request.RequestedBySuperSupport
                && request.RequestedUserStructures.All(id =>
                    id != section.Structure.Id))
            {
                return Forbidden(
                    Guid.Empty,
                    "000",
                    "You do not have permission to update this section.");
            }

            var sectionReview =
                section.Reviews.FirstOrDefault(review =>
                    review.Id == request.Id);

            if (sectionReview is null)
            {
                return BadRequest(
                    Guid.Empty,
                    "000",
                    "Section review not found in database.");
            }

            var materials = await _staticMaterialRepository
                .GetByStructure(section.Structure.Id);

            var dxfValidationResult = request
                .SectionReviewV2
                .Drawing?
                .IsValidSection(materials) ?? string.Empty;

            if (!string.IsNullOrEmpty(dxfValidationResult))
            {
                return BadRequest(
                    Guid.Empty,
                    "000",
                    dxfValidationResult);
            }

            var unmodified = section.Clone();

            sectionReview.StartDate = request.SectionReviewV2.StartDate;
            
            sectionReview.SetSectionType(request.SectionReviewV2.SectionType
                .ConvertToEntity<Domain.Entities.SectionType>());

            sectionReview.Description = request.SectionReviewV2.Description;
            sectionReview.Index = request.SectionReviewV2.Index;

            sectionReview.SetDrawing(
                request.SectionReviewV2.Drawing.ConvertToEntity());

            section.UpsertSectionReview(sectionReview);

            var changeText = CompareLogic.GetChangeHistory(unmodified, section);

            var history = section.AddHistory(request.RequestedBy, changeText);

            if (sectionReview.Drawing is not null)
            {
                var byteArray = Convert.FromBase64String(
                    sectionReview.Drawing.Base64);

                await _blobService.UploadAsync(
                    byteArray,
                    sectionReview.Drawing.UniqueName);
            }

            await _sectionRepository.UpsertSectionReviewAsync(
                sectionReview,
                history);

            return Ok(sectionReview.Id);
        }
        catch (Exception e)
        {
            return InternalServerError(
                Guid.Empty,
                errors: e.ToErrorMessages("000"));
        }
    }
}
