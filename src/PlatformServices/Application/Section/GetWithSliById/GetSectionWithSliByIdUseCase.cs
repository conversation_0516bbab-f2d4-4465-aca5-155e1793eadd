using Application.Core;
using Application.Services.BlobStorage;
using Coordinate.Core;
using Coordinate.Core.Classes;
using Database.Repositories.Section;
using Domain.ValueObjects;
using Microsoft.Extensions.Options;
using Model.Section.GetWithSliById.Response;
using Model.StructureType._Shared.StructureTypeActivity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Application.Core.UseCaseResponseFactory<Model.Section.GetWithSliById.Response.GetSectionWithSliByIdResponse>;

namespace Application.Section.GetWithSliById
{
    public sealed class GetSectionWithSliByIdUseCase : IGetSectionWithSliByIdUseCase
    {
        private readonly ISectionRepository _sectionRepository;
        private readonly IBlobStorageService _blobService;
        private readonly BlobStorageOptions _blobOptions;

        public GetSectionWithSliByIdUseCase(
            ISectionRepository sectionRepository,
            IBlobStorageService blobService,
            IOptions<BlobStorageOptions> blobOptions)
        {
            _sectionRepository = sectionRepository;
            _blobService = blobService;
            _blobOptions = blobOptions.Value;
        }

        public async Task<UseCaseResponse<GetSectionWithSliByIdResponse>> Execute(Guid request)
        {
            try
            {
                if (request == Guid.Empty)
                {
                    return BadRequest(null, "000", "Request cannot be empty.");
                }

                var section = await _sectionRepository.GetAsync(request);

                if (section == null)
                {
                    return NoContent();
                }

                return Ok(await Map(section));
            }
            catch (Exception e)
            {
                return InternalServerError(null, errors: e.ToErrorMessages("000"));
            }
        }

        private async Task<GetSectionWithSliByIdResponse> Map(Domain.Entities.Section section)
        {
            var coordinates = new Model.Section._Shared.SectionCoordinates.SectionCoordinates()
            {
                Datum = section.Coordinates.Datum,
                UpstreamCoordinateSetting = new()
                {
                    CoordinateFormat = section.Coordinates.UpstreamCoordinateSetting.Format,
                    CoordinateSystems = GetCoordinateSystems(section.Coordinates.UpstreamCoordinateSetting)
                },
                DownstreamCoordinateSetting = new()
                {
                    CoordinateFormat = section.Coordinates.DownstreamCoordinateSetting.Format,
                    CoordinateSystems = GetCoordinateSystems(section.Coordinates.DownstreamCoordinateSetting)
                },
                MidpointCoordinateSetting = section.Coordinates.MidpointCoordinateSetting == null
                ? null
                : new()
                {
                    CoordinateFormat = section.Coordinates.MidpointCoordinateSetting.Format,
                    CoordinateSystems = GetCoordinateSystems(section.Coordinates.MidpointCoordinateSetting)
                }
            };

            var sectionReviewList = new List<SectionReviewWithSliResponse>();

            foreach (var review in section.Reviews)
            {
                var reviewModel = new SectionReviewWithSliResponse()
                {
                    Id = review.Id,
                    StartDate = review.StartDate,
                    DxfHasWaterline = review.DxfHasWaterline,
                    StructureType = new()
                    {
                        Id = review.StructureType.Id,
                        Name = review.StructureType.Name,
                        Description = review.StructureType.Description,
                        Active = review.StructureType.Active,
                        Activities = review.StructureType.Activities.Select(x => new StructureTypeActivity()
                        {
                            Id = x.Id,
                            Activity = x.Activity,
                            Index = x.Index,
                        }).ToList()
                    },
                    Index = review.Index
                };

                if (review.Drawing != null)
                {
                    reviewModel.Drawing = await GetFileAsync(review.Drawing);
                }

                if (review.Sli != null)
                {
                    reviewModel.Sli = await GetFileAsync(review.Sli);
                }

                var constructionStages = new List<ConstructionStageWithSliResponse>();

                foreach (var constructionStage in review.ConstructionStages)
                {
                    var constructionStageModel = new ConstructionStageWithSliResponse()
                    {
                        Id = constructionStage.Id,
                        Stage = constructionStage.Stage,
                        Description = constructionStage.Description,
                        IsCurrentStage = constructionStage.IsCurrentStage,
                        DxfHasWaterline = constructionStage.DxfHasWaterline
                    };

                    if (constructionStage.Drawing != null)
                    {
                        constructionStageModel.Drawing = await GetFileAsync(constructionStage.Drawing);
                    }

                    if (constructionStage.Sli != null)
                    {
                        constructionStageModel.Sli = await GetFileAsync(constructionStage.Sli);
                    }

                    constructionStages.Add(constructionStageModel);
                }

                reviewModel.ConstructionStages = constructionStages;

                sectionReviewList.Add(reviewModel);
            }

            var instruments = new List<Model.Section._Shared.InstrumentInfo>();

            foreach (var instrument in section.Instruments)
            {
                var instrumentInfo = new Model.Section._Shared.InstrumentInfo()
                {
                    Id = instrument.Id,
                    Identifier = instrument.Identifier,
                    Online = instrument.Online,
                    Type = instrument.Type,
                    DecimalGeodetic = instrument.CoordinateSetting.Systems.DecimalGeodetic
                };

                if (instrument.CoordinateSetting.Datum != Coordinate.Core.Enums.Datum.SIRGAS2000)
                {
                    instrumentInfo.DecimalGeodetic = Helper.ToDatum(instrument.CoordinateSetting.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, instrumentInfo.DecimalGeodetic);
                }

                instruments.Add(instrumentInfo);
            }

            return new()
            {
                Id = section.Id,
                Name = section.Name,
                MinimumDrainedDepth = section.MinimumDrainedDepth,
                MinimumUndrainedDepth = section.MinimumUndrainedDepth,
                MinimumPseudoStaticDepth = section.MinimumPseudoStaticDepth,
                Client = new()
                {
                    Id = section.Client.Id,
                    Name = section.Client.Name,
                    Active = section.Client.Active,
                },
                ClientUnit = new()
                {
                    Id = section.ClientUnit.Id,
                    Name = section.ClientUnit.Name,
                    Active = section.ClientUnit.Active,
                    ClientId = section.ClientUnit.ClientId
                },
                Structure = new()
                {
                    Id = section.Structure.Id,
                    Name = section.Structure.Name,
                    ClientUnitId = section.Structure.ClientUnitId,
                },
                IsSkew = section.IsSkew,
                SkewLineAzimuth = section.SkewLineAzimuth,
                NormalLineAzimuth = section.NormalLineAzimuth,
                MapLineSetting = new()
                {
                    Color = section.MapLineSetting.Color,
                    Type = section.MapLineSetting.Type,
                    Width = section.MapLineSetting.Width
                },
                Instruments = instruments,
                Coordinates = coordinates,
                Reviews = sectionReviewList
            };
        }

        private async Task<Model._Shared.File.File> GetFileAsync(File file)
        {
            var byteArray = await _blobService
                                .GetAsync(file.UniqueName, _blobOptions.ClientsContainer);

            if (byteArray != null && byteArray.Any())
            {
                return new()
                {
                    Base64 = Convert.ToBase64String(byteArray),
                    Name = file.Name
                };
            }

            return null;
        }

        private static Systems GetCoordinateSystems(Domain.Entities.SectionCoordinateSetting coordinateSetting)
        {
            return new Systems()
            {
                DecimalGeodetic = new DecimalGeodetic()
                {
                    Latitude = coordinateSetting.CoordinateSystems.DecimalGeodetic.Latitude,
                    Longitude = coordinateSetting.CoordinateSystems.DecimalGeodetic.Longitude
                },
                Utm = new Utm()
                {
                    Easting = coordinateSetting.CoordinateSystems.Utm.Easting,
                    Northing = coordinateSetting.CoordinateSystems.Utm.Northing,
                    ZoneNumber = coordinateSetting.CoordinateSystems.Utm.ZoneNumber,
                    ZoneLetter = coordinateSetting.CoordinateSystems.Utm.ZoneLetter
                }
            };
        }
    }
}
