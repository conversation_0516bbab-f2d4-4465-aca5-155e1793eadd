using System;
using System.Linq;
using Application.Extensions;
using Domain.Entities;
using Model.Section.V2._Shared;

namespace Application.Section.Extensions;

public static class SectionReviewRequestExtensions
{
    public static SectionReview ConvertToEntity(
        this SectionReviewRequest request,
        Domain.Entities.Section section,
        Guid requestedBy)
    {
        var result = new SectionReview()
        {
            StartDate = request.StartDate,
            Section = section,
            Index = request.Index,
            Description = request.Description,
            IsUnderConstruction = request.IsUnderConstruction,
            CreatedBy = new() { Id = requestedBy },
        };
        
        result.SetSectionType(new() { Id = request.SectionType.Id });

        var drawing = request.Drawing?.ConvertToEntity();
        result.SetDrawing(drawing);

        result.ConstructionStages = request.ConstructionStages
            .Select(stage => stage.ConvertToEntity(result))
            .ToList();

        return result;
    }

    public static SectionReview ConvertToEntity(
        this SectionReviewV2 request,
        Domain.Entities.Section section,
        Guid requestedBy)
    {
        var result = new SectionReview()
        {
            StartDate = request.StartDate,
            Section = section,
            Index = request.Index,
            Description = request.Description,
            IsUnderConstruction = false,
            CreatedBy = new() { Id = requestedBy },
        };
        
        result.SetSectionType(new() { Id = request.SectionType.Id });
        
        var drawing = request.Drawing?.ConvertToEntity();
        result.SetDrawing(drawing);

        return result;
    }
}
