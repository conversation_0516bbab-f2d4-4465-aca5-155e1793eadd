using Application.Extensions;
using Domain.Entities;
using Model.Section.V2._Shared;

namespace Application.Section.Extensions;

public static class ConstructionStageRequestV2Extensions
{
    public static ConstructionStage ConvertToEntity(
        this ConstructionStageV2 stage,
        SectionReview sectionReview)
    {
        var result = new ConstructionStage()
        {
            Stage = stage.Stage,
            Description = stage.Description,
            IsCurrentStage = stage.IsCurrentStage,
            SectionReview = sectionReview,
        };

        var drawing = stage.Drawing?.ConvertToEntity();
        result.SetDrawing(drawing);

        return result;
    }
}
