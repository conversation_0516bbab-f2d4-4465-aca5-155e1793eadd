using Application.Core;
using Application.Extensions;
using Database.Repositories.Instrument;
using Database.Repositories.Package;
using Database.Repositories.Reading;
using Database.Repositories.Section;
using Database.Repositories.Structure;
using Database.Repositories.User;
using Model.Package.Add.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Application.Package.Extensions;
using static Application.Core.UseCaseResponseFactory<
    System.Collections.Generic.IEnumerable<System.Guid>>;

namespace Application.Package.Add;

public sealed class AddPackageUseCase : IAddPackageUseCase
{
    private readonly IPackageRepository _packageRepository;
    private readonly ISectionRepository _sectionRepository;
    private readonly IReadingRepository _readingRepository;
    private readonly IInstrumentRepository _instrumentRepository;
    private readonly IUserRepository _userRepository;
    private readonly IStructureRepository _structureRepository;
    private readonly AddPackageRequestValidator _requestValidator = new();

    public AddPackageUseCase(
        IPackageRepository packageRepository,
        ISectionRepository sectionRepository,
        IInstrumentRepository instrumentRepository,
        IUserRepository userRepository,
        IReadingRepository readingRepository,
        IStructureRepository structureRepository)
    {
        _packageRepository = packageRepository;
        _sectionRepository = sectionRepository;
        _instrumentRepository = instrumentRepository;
        _userRepository = userRepository;
        _readingRepository = readingRepository;
        _structureRepository = structureRepository;
    }

    public async Task<UseCaseResponse<IEnumerable<Guid>>> Execute(
        AddPackageRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(null);
            }

            await request.AddRequesterStructures(_userRepository);

            var validationResult = await _requestValidator
                .ValidateAsync(request);

            if (!validationResult.IsValid)
            {
                return BadRequest(null,
                    validationResult.Errors.ToErrorMessages());
            }
            
            var instrumentStructure = Guid.Empty;

            var readingValues = new List<Domain.Entities.ReadingValue>();

            foreach (var readingValueId in request.ReadingValues)
            {
                var reading = await _readingRepository.GetByReadingValueIdAsync(readingValueId);

                if (reading == null)
                {
                    return BadRequest(null, "000", $"The reading value {readingValueId} does not exist.");
                }
                
                if (!request.RequestedBySuperSupport
                   && !request.RequestedUserStructures.Exists(x =>
                       x == reading.Instrument.Structure.Id))
                {
                    return Forbidden(null, "000",
                        "You cannot create packages of readings linked to instruments from structures that you do not have permissions.");
                }

                if (!reading.Instrument.CanBeUsedInPackages())
                {
                    return BadRequest(null, "000",
                        "The reading for this type of instrument cannot be used to generate packages.");
                }

                if (instrumentStructure == Guid.Empty)
                {
                    instrumentStructure = reading.Instrument.Structure.Id;
                }

                if (instrumentStructure != reading.Instrument.Structure.Id)
                {
                    return BadRequest(null, "000", "Readings must be from instruments of the same structure.");
                }

                var instrumentIsInSomeSection = await _instrumentRepository
                    .CheckIfIsInAnySectionAsync(reading.Instrument.Id);

                if (!instrumentIsInSomeSection)
                {
                    return BadRequest(null, "000",
                        $"The instrument {reading.Instrument.Identifier} is not associated with any section.");
                }

                var readingValue =
                    reading.Values.First(x => x.Id == readingValueId);

                readingValue.Reading = reading;

                readingValues.Add(readingValue);
            }

            var structure =
                await _structureRepository.GetAsync(instrumentStructure);

            if (structure is not { Active: true })
            {
                return BadRequest(null, "000", "Structure not found.");
            }

            if (!structure.ShouldEvaluateAnyCondition())
            {
                return BadRequest(null, "000",
                    "Structure does not have any condition (drained, undrained or pseudo static) to evaluate.");
            }

            var sections =
                await _sectionRepository.GetByStructureAsync(structure.Id);

            if (sections == null || !sections.Any() ||
                structure.Sections.Count != sections.Count())
            {
                return BadRequest(null, "000",
                    "Could not find all sections from this structure.");
            }

            var packages = readingValues
                .GroupBy(readingValue => readingValue.Date)
                .Select(group => group.ConvertToPackage(sections))
                .ToList();

            await _packageRepository.AddAsync(packages);

            return Ok(packages.Select(x => x.Id));
        }
        catch (Exception e)
        {
            return InternalServerError(null, errors: e.ToErrorMessages("000"));
        }
    }
}