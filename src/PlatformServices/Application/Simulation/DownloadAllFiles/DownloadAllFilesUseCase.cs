using Application.Core;
using Application.Extensions;
using Application.Services.BlobStorage;
using Database.Repositories.Simulation;
using Microsoft.Extensions.Options;
using Model._Shared.File;
using Model.Simulation.DownloadAllFiles.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Domain.Extensions;
using static Application.Core.UseCaseResponseFactory<Model._Shared.File.DownloadableFile>;

namespace Application.Simulation.DownloadAllFiles
{
    public sealed class DownloadAllFilesUseCase : IDownloadAllFilesUseCase
    {
        private readonly ISimulationRepository _simulationRepository;
        private readonly IBlobStorageService _blobStorageService;
        private readonly BlobStorageOptions _blobStorageOptions;

        public DownloadAllFilesUseCase(
            ISimulationRepository simulationRepository,
            IBlobStorageService blobStorageService,
            IOptions<BlobStorageOptions> blobStorageOptions)
        {
            _simulationRepository = simulationRepository;
            _blobStorageService = blobStorageService;
            _blobStorageOptions = blobStorageOptions.Value;
        }

        public async Task<UseCaseResponse<DownloadableFile>> Execute(
            DownloadAllFilesRequest request)
        {
            if (request == null)
            {
                return BadRequest(null, "000", "Request cannot be null.");
            }

            try
            {
                var simulation = await _simulationRepository.GetAsync(request.Id);

                if (simulation == null)
                {
                    return NotFound(null, null);
                }

                if (!simulation.AuthorizedUsers.Exists(x => x.Id == request.RequestedBy))
                {
                    return Forbidden(null, "000", "User does not have access to this simulation.");
                }

                var files = new List<Domain.ValueObjects.File>();
                foreach (var section in simulation.Sections)
                {
                    files.AddRange(
                        section.Results.SelectMany(result => new Domain.ValueObjects.File[]
                        {
                            result.SliFile.WithNamePrefix(section.Section.Name),
                            result.SltmFile.WithNamePrefix(section.Section.Name),
                            result.DxfFile.WithNamePrefix(section.Section.Name),
                            result.PngFile.WithNamePrefix(section.Section.Name)
                        })
                    );
                }

                if (!files.Any())
                {
                    return NotFound(null, null);
                }

                var zipFilesBytes = ZipFileCreator.CreateZipFile(files.ToAsyncEnumerable()
                    .SelectAwait(async f =>
                    {
                        var byteArray = await _blobStorageService.GetAsync(f.UniqueName, _blobStorageOptions.ClientsContainer);
                        return (f.Name, byteArray);
                    })
                    .ToEnumerable());

                return Ok(new DownloadableFile(zipFilesBytes, "application/zip"));
            }
            catch (Exception ex)
            {
                return InternalServerError(null, errors: ex.ToErrorMessages("000"));
            }
        }
    }
}
