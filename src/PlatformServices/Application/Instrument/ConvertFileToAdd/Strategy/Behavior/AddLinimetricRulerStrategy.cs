using Domain.Extensions;
using Coordinate.Core;
using Coordinate.Core.Enums;
using Domain.Enums;
using Model.Instrument._Shared;
using System;
using System.Data;

namespace Application.Instrument.ConvertFileToAdd.Strategy.Behavior
{
    public class AddLinimetricRulerStrategy : IJsonStrategy
    {
        public void SetProperties(InstrumentRequest instrument, DataRow row)
        {
            var latHasBeenConverted = double.TryParse(row[6].ToString(), out double lat);
            var lngHasBeenConverted = double.TryParse(row[7].ToString(), out double lng);

            instrument.Identifier = row[1].ToString();
            instrument.AlternativeName = row[2].ToString();
            instrument.LinimetricRulerPosition = Enum.TryParse(row[3].ToString(), out LinimetricRulerPosition position) ? position : LinimetricRulerPosition.Upstream;
            instrument.TopQuota = decimal.TryParse(row[4].ToString(), out decimal topQuota) ? topQuota : 0;
            instrument.CoordinateSetting = new()
            {
                Datum = Enum.TryParse(row[5].ToString(), out Datum datum) ? datum : Datum.SIRGAS2000,
                Format = latHasBeenConverted && lngHasBeenConverted ? Format.DecimalGeodetic : Format.UTM,
                Systems = new()
                {
                    DecimalGeodetic = new()
                    {
                        Latitude = latHasBeenConverted ? lat : 0,
                        Longitude = lngHasBeenConverted ? lng : 0
                    },
                    Utm = new()
                    {
                        Easting = double.TryParse(row[8].ToString(), out double easting) ? easting : 0,
                        Northing = double.TryParse(row[9].ToString(), out double northing) ? northing : 0,
                        ZoneNumber = int.TryParse(row[10].ToString(), out int zoneNumber) ? zoneNumber : 1,
                        ZoneLetter = char.TryParse(row[11].ToString(), out char zoneLetter) ? zoneLetter : 'C',
                    }
                }
            };
            instrument.Automated = row[12].ToString().ToBool();
            instrument.Online = row[13].ToString().ToBool();
            instrument.Model = row[14].ToString();
            instrument.ResponsibleForInstallation = row[15].ToString();
            instrument.InstallationDate = DateTime.TryParse(row[16].ToString(), out DateTime installationDate) ? installationDate : DateTime.UtcNow;
            instrument.SecurityLevels.Add(new()
            {
                Attention = decimal.TryParse(row[17].ToString(), out decimal attention) ? attention : null,
                Alert = decimal.TryParse(row[18].ToString(), out decimal alert) ? alert : null,
                Emergency = decimal.TryParse(row[19].ToString(), out decimal emergency) ? emergency : null,
                AbruptVariation = decimal.TryParse(row[20].ToString(), out decimal abruptVariationBetweenReadings) ? abruptVariationBetweenReadings : null
            });

            if (instrument.CoordinateSetting.Format == Format.DecimalGeodetic)
            {
                instrument.CoordinateSetting.Systems.Utm =
                    Helper.ConvertDecimalGeodeticToUTM(instrument.CoordinateSetting.Systems.DecimalGeodetic, instrument.CoordinateSetting.Datum);
            }
            else
            {
                instrument.CoordinateSetting.Systems.DecimalGeodetic =
                    Helper.ConvertUTMToDecimalGeodetic(instrument.CoordinateSetting.Systems.Utm, instrument.CoordinateSetting.Datum);
            }
        }
    }
}
