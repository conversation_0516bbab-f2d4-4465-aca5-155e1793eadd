using Domain.Extensions;
using Coordinate.Core;
using Coordinate.Core.Enums;
using Model.Instrument._Shared;
using System;
using System.Data;

namespace Application.Instrument.ConvertFileToAdd.Strategy.Behavior
{
    public class AddPluviometerStrategy : IJsonStrategy
    {
        public void SetProperties(InstrumentRequest instrument, DataRow row)
        {
            var latHasBeenConverted = double.TryParse(row[4].ToString(), out double lat);
            var lngHasBeenConverted = double.TryParse(row[5].ToString(), out double lng);

            instrument.Identifier = row[1].ToString();
            instrument.AlternativeName = row[2].ToString();

            instrument.CoordinateSetting = new()
            {
                Datum = Enum.TryParse(row[3].ToString(), out Datum datum) ? datum : Datum.SIRGAS2000,
                Format = latHasBeenConverted && lngHasBeenConverted ? Format.DecimalGeodetic : Format.UTM,
                Systems = new()
                {
                    DecimalGeodetic = new()
                    {
                        Latitude = latHasBeenConverted ? lat : 0,
                        Longitude = lngHasBeenConverted ? lng : 0
                    },
                    Utm = new()
                    {
                        Easting = double.TryParse(row[6].ToString(), out double easting) ? easting : 0,
                        Northing = double.TryParse(row[7].ToString(), out double northing) ? northing : 0,
                        ZoneNumber = int.TryParse(row[8].ToString(), out int zoneNumber) ? zoneNumber : 1,
                        ZoneLetter = char.TryParse(row[9].ToString(), out char zoneLetter) ? zoneLetter : 'C',
                    }
                }
            };
            instrument.Automated = row[10].ToString().ToBool();
            instrument.Online = row[11].ToString().ToBool();
            instrument.Model = row[12].ToString();
            instrument.ResponsibleForInstallation = row[13].ToString();
            instrument.InstallationDate = DateTime.TryParse(row[14].ToString(), out DateTime installationDate) ? installationDate : DateTime.UtcNow;
            instrument.SecurityLevels.Add(new()
            {
                MaximumDailyRainfall = decimal.TryParse(row[15].ToString(), out decimal dailyRainfall) ? dailyRainfall : null
            });

            if (instrument.CoordinateSetting.Format == Format.DecimalGeodetic)
            {
                instrument.CoordinateSetting.Systems.Utm =
                    Helper.ConvertDecimalGeodeticToUTM(instrument.CoordinateSetting.Systems.DecimalGeodetic, instrument.CoordinateSetting.Datum);
            }
            else
            {
                instrument.CoordinateSetting.Systems.DecimalGeodetic =
                    Helper.ConvertUTMToDecimalGeodetic(instrument.CoordinateSetting.Systems.Utm, instrument.CoordinateSetting.Datum);
            }
        }
    }
}
