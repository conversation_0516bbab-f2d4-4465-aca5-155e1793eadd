using Domain.Extensions;
using Coordinate.Core;
using Coordinate.Core.Enums;
using Model.Instrument._Shared;
using System;
using System.Data;
using System.Globalization;
using System.Text.RegularExpressions;

namespace Application.Instrument.ConvertFileToAdd.Strategy.Behavior
{
    public class AddPluviographStrategy : IJsonStrategy
    {
        public void SetProperties(InstrumentRequest instrument, DataRow row)
        {
            var latHasBeenConverted = double.TryParse(row[5].ToString(), out double lat);
            var lngHasBeenConverted = double.TryParse(row[6].ToString(), out double lng);

            var measurementFrequency = row[3].ToString();

            if (string.IsNullOrEmpty(measurementFrequency))
            {
                measurementFrequency = "00:00";
            }
            else if (Regex.IsMatch(measurementFrequency, @"^\d+:\d+:?\d+?$"))
            {
                measurementFrequency = measurementFrequency[..5].EndsWith(":") ? measurementFrequency[..4] : measurementFrequency[..5];
            }
            else if (Regex.IsMatch(measurementFrequency, @"^\d+\/\d+\/\d+ .*$"))
            {
                DateTime.TryParseExact(measurementFrequency, "MM/dd/yyyy h:mm:ss tt", null, DateTimeStyles.None, out var dateTime);

                measurementFrequency = dateTime.ToString("HH:mm");
            }

            instrument.Identifier = row[1].ToString();
            instrument.AlternativeName = row[2].ToString();
            instrument.MeasurementFrequency = measurementFrequency;

            instrument.CoordinateSetting = new()
            {
                Datum = Enum.TryParse(row[4].ToString(), out Datum datum) ? datum : Datum.SIRGAS2000,
                Format = latHasBeenConverted && lngHasBeenConverted ? Format.DecimalGeodetic : Format.UTM,
                Systems = new()
                {
                    DecimalGeodetic = new()
                    {
                        Latitude = latHasBeenConverted ? lat : 0,
                        Longitude = lngHasBeenConverted ? lng : 0
                    },
                    Utm = new()
                    {
                        Easting = double.TryParse(row[7].ToString(), out double easting) ? easting : 0,
                        Northing = double.TryParse(row[8].ToString(), out double northing) ? northing : 0,
                        ZoneNumber = int.TryParse(row[9].ToString(), out int zoneNumber) ? zoneNumber : 1,
                        ZoneLetter = char.TryParse(row[10].ToString(), out char zoneLetter) ? zoneLetter : 'C',
                    }
                }
            };

            instrument.Automated = row[11].ToString().ToBool();
            instrument.Online = row[12].ToString().ToBool();
            instrument.Model = row[13].ToString();
            instrument.ResponsibleForInstallation = row[14].ToString();
            instrument.InstallationDate = DateTime.TryParse(row[15].ToString(), out DateTime installationDate) ? installationDate : DateTime.UtcNow;
            instrument.SecurityLevels.Add(new()
            {
                MaximumDailyRainfall = decimal.TryParse(row[16].ToString(), out decimal dailyRainfall) ? dailyRainfall : null,
                RainIntensity = decimal.TryParse(row[17].ToString(), out decimal intensity) ? intensity : null
            });

            if (instrument.CoordinateSetting.Format == Format.DecimalGeodetic)
            {
                instrument.CoordinateSetting.Systems.Utm =
                    Helper.ConvertDecimalGeodeticToUTM(instrument.CoordinateSetting.Systems.DecimalGeodetic, instrument.CoordinateSetting.Datum);
            }
            else
            {
                instrument.CoordinateSetting.Systems.DecimalGeodetic =
                    Helper.ConvertUTMToDecimalGeodetic(instrument.CoordinateSetting.Systems.Utm, instrument.CoordinateSetting.Datum);
            }
        }
    }
}
