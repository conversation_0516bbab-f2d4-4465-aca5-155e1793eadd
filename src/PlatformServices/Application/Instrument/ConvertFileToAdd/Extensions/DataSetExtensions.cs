using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace Application.Instrument.ConvertFileToAdd.Extensions;

public static class DataSetExtensions
{
    /// <summary>
    /// Extracts unique structure identifiers from the first table in the provided <see cref="DataSet"/>.
    /// </summary>
    /// <param name="dataSet">The <see cref="DataSet"/> containing the table to extract identifiers from.</param>
    /// <returns>An <see cref="HashSet{T}"/> of unique structure identifiers.</returns>
    /// <remarks>
    /// This method processes the rows in the first table of the <see cref="DataSet"/> and retrieves the values
    /// from the first column, skipping the header row. Only non-empty and distinct values are returned.
    /// </remarks>
    public static HashSet<int> GetUniqueStructureIdentifiers(
        this DataSet dataSet)
    {
        return dataSet.Tables[0].Rows
            .Cast<DataRow>()
            .Select(row => row[0].ToString())
            .Where(identifier => !string.IsNullOrEmpty(identifier))
            .Select(identifier => Convert.ToInt32(identifier))
            .Distinct()
            .ToHashSet();
    }
}
