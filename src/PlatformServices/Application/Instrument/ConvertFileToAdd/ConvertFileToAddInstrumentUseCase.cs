using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Core;
using Application.Extensions;
using Application.Instrument.ConvertFileToAdd.Extensions;
using Application.Instrument.ConvertFileToAdd.Strategy;
using Application.Instrument.ConvertFileToAdd.Strategy.Factory;
using Database.Repositories.Structure;
using Database.Repositories.User;
using ExcelDataReader;
using Model.Instrument._Shared;
using Model.Instrument.Convert.Request;
using static Application.Core.UseCaseResponseFactory<System.Collections.Generic.IEnumerable<Model.Instrument._Shared.InstrumentRequest>>;

namespace Application.Instrument.ConvertFileToAdd
{
    public sealed class ConvertFileToAddInstrumentUseCase : IConvertFileToAddInstrumentUseCase
    {
        private readonly IUserRepository _userRepository;
        private readonly IStructureRepository _structureRepository;
        private readonly ConvertInstrumentsRequestValidator _requestValidator = new();

        public ConvertFileToAddInstrumentUseCase(
            IUserRepository userRepository,
            IStructureRepository structureRepository)
        {
            _userRepository = userRepository;
            _structureRepository = structureRepository;
        }

        public async Task<UseCaseResponse<IEnumerable<InstrumentRequest>>> Execute(ConvertInstrumentsRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest(null);
                }

                await request.AddRequesterStructures(_userRepository);

                var validationResult = await _requestValidator
                   .ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    return BadRequest(null,
                        validationResult.Errors.ToErrorMessages());
                }

                var instruments = new List<InstrumentRequest>();

                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

                await using var stream = request.File.OpenReadStream();
                
                using var reader = request.File.ContentType.EndsWith("csv")
                    ? ExcelReaderFactory.CreateCsvReader(stream)
                    : ExcelReaderFactory.CreateReader(stream);

                var result = reader.AsDataSet(new()
                {
                    ConfigureDataTable = _ => new()
                    {
                        UseHeaderRow = true,
                        // Reads only rows when the Depth (row index) is greater than 0.
                        FilterRow = rowReader => rowReader.Depth > 0,
                        FilterColumn = (rowReader, columnIndex) => true
                    }
                });

                var identifiers = result.GetUniqueStructureIdentifiers();
                
                var structures = await _structureRepository
                    .GetBySearchIdentifiersAsync(identifiers);

                foreach (DataRow row in result.Tables[0].Rows)
                {
                    var identifier = row[1].ToString();

                    if (string.IsNullOrEmpty(identifier))
                    {
                        return BadRequest(null, "000", "All instruments must have an identifier.");
                    }

                    if (string.IsNullOrEmpty(row[0].ToString()))
                    {
                        return BadRequest(null, "000",
                            $"Instrument {identifier} does not have a structure with a valid ID.");
                    }

                    var structureIdentifier = int.Parse(row[0].ToString());
                    
                    var structure = structures.FirstOrDefault(structure =>
                        structure.SearchIdentifier == structureIdentifier);

                    if (structure == null)
                    {
                        return BadRequest(null, "000",
                            $"Structure of instrument {identifier} not found.");
                    }

                    if (!request.RequestedBySuperSupport
                        && !request.RequestedUserStructures.Contains(structure.Id))
                    {
                        return Forbidden(null, "000",
                            $"You are not allowed to create instruments linked to the selected structure for instrument {identifier}.");
                    }

                    var instrumentInList = instruments.FirstOrDefault(i => i.Identifier == identifier);

                    var instrument = instrumentInList ?? new InstrumentRequest()
                    {
                        Type = request.Type,
                        Structure = new()
                        {
                            Id = structure.Id,
                            Name = structure.Name,
                            ClientUnitId = structure.ClientUnitId
                        }
                    };

                    var instrumentStrategy =
                        new JsonStrategy(instrument.Type.GetStrategy());

                    instrumentStrategy.SetProperties(instrument, row);

                    if (instrumentInList == null)
                    {
                        instruments.Add(instrument);
                    }
                }

                return Ok(instruments);
            }
            catch (Exception e)
            {
                return InternalServerError(null, errors: e.ToErrorMessages("000"));
            }
        }
    }
}
