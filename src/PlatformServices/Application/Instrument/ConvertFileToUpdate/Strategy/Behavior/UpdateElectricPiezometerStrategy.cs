using Coordinate.Core;
using Coordinate.Core.Enums;
using Domain.Enums;
using Model.Instrument._Shared;
using Model.Instrument._Shared.Measurement;
using System;
using System.Data;
using System.Linq;
using Domain.Extensions;

namespace Application.Instrument.ConvertFileToUpdate.Strategy.Behavior
{
    public class UpdateElectricPiezometerStrategy : IJsonStrategy
    {
        public void SetProperties(InstrumentRequest instrument, DataRow row)
        {
            var latHasBeenConverted = double.TryParse(row[7].ToString(), out double lat);
            var lngHasBeenConverted = double.TryParse(row[8].ToString(), out double lng);

            instrument.Identifier = row[2].ToString();
            instrument.AlternativeName = row[3].ToString();
            instrument.TopQuota = decimal.TryParse(row[4].ToString(), out decimal topQuota) ? topQuota : 0;
            instrument.DryType = Enum.TryParse(row[5].ToString(), out DryType dryType) ? dryType : DryType.Interpolated;
            instrument.CoordinateSetting = new()
            {
                Datum = Enum.TryParse(row[6].ToString(), out Datum datum) ? datum : Datum.SIRGAS2000,
                Format = latHasBeenConverted && lngHasBeenConverted ? Format.DecimalGeodetic : Format.UTM,
                Systems = new()
                {
                    DecimalGeodetic = new()
                    {
                        Latitude = latHasBeenConverted ? lat : 0,
                        Longitude = lngHasBeenConverted ? lng : 0
                    },
                    Utm = new()
                    {
                        Easting = double.TryParse(row[9].ToString(), out double easting) ? easting : 0,
                        Northing = double.TryParse(row[10].ToString(), out double northing) ? northing : 0,
                        ZoneNumber = int.TryParse(row[11].ToString(), out int zoneNumber) ? zoneNumber : 1,
                        ZoneLetter = char.TryParse(row[12].ToString(), out char zoneLetter) ? zoneLetter : 'C',
                    }
                }
            };
            instrument.Automated = row[13].ToString().ToBool();
            instrument.Online = row[14].ToString().ToBool();
            instrument.Model = row[15].ToString();
            instrument.ResponsibleForInstallation = row[16].ToString();
            instrument.InstallationDate = DateTime.TryParse(row[17].ToString(), out DateTime installationDate) ? installationDate : DateTime.UtcNow;
            var measurement = new MeasurementRequest()
            {
                Identifier = row[18].ToString(),
                AlternativeName = row[19].ToString(),
                Active = row[20].ToString().ToBool(),
                Quota = decimal.TryParse(row[21].ToString(), out decimal quota) ? quota : 0,
                SecurityLevels = new()
                {
                    Attention = decimal.TryParse(row[22].ToString(), out decimal attention) ? attention : null,
                    Alert = decimal.TryParse(row[23].ToString(), out decimal alert) ? alert : null,
                    Emergency = decimal.TryParse(row[24].ToString(), out decimal emergency) ? emergency : null,
                    AbruptVariation = decimal.TryParse(row[25].ToString(), out decimal abruptVariationBetweenReadings) ? abruptVariationBetweenReadings : null
                }
            };

            var measurementInList = instrument.Measurements.FirstOrDefault(x => x.Identifier == measurement.Identifier);

            if (measurementInList != null)
            {
                measurement.Id = measurementInList.Id;
                instrument.Measurements.Remove(measurementInList);
            }

            instrument.Measurements.Add(measurement);

            if (instrument.CoordinateSetting.Format == Format.DecimalGeodetic)
            {
                instrument.CoordinateSetting.Systems.Utm =
                    Helper.ConvertDecimalGeodeticToUTM(instrument.CoordinateSetting.Systems.DecimalGeodetic, instrument.CoordinateSetting.Datum);
            }
            else
            {
                instrument.CoordinateSetting.Systems.DecimalGeodetic =
                    Helper.ConvertUTMToDecimalGeodetic(instrument.CoordinateSetting.Systems.Utm, instrument.CoordinateSetting.Datum);
            }
        }
    }
}
