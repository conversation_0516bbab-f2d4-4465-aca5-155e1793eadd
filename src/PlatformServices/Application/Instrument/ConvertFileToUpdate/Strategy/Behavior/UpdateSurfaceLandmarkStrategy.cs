using Domain.Extensions;
using Coordinate.Core;
using Coordinate.Core.Enums;
using Model.Instrument._Shared;
using System;
using System.Data;

namespace Application.Instrument.ConvertFileToUpdate.Strategy.Behavior
{
    public class UpdateSurfaceLandmarkStrategy : IJsonStrategy
    {
        public void SetProperties(InstrumentRequest instrument, DataRow row)
        {
            var latHasBeenConverted = double.TryParse(row[9].ToString(), out double lat);
            var lngHasBeenConverted = double.TryParse(row[10].ToString(), out double lng);

            instrument.Identifier = row[2].ToString();
            instrument.AlternativeName = row[3].ToString();
            instrument.TopQuota = decimal.TryParse(row[4].ToString(), out decimal topQuota) ? topQuota : 0;
            instrument.Azimuth = double.TryParse(row[5].ToString(), out double azimuth) ? azimuth : 0;
            instrument.UpperLimit = double.TryParse(row[6].ToString(), out double upperLimit) ? upperLimit : 0;
            instrument.LowerLimit = double.TryParse(row[7].ToString(), out double lowerLimit) ? lowerLimit : 0;
            instrument.CoordinateSetting = new()
            {
                Datum = Enum.TryParse(row[8].ToString(), out Datum datum) ? datum : Datum.SIRGAS2000,
                Format = latHasBeenConverted && lngHasBeenConverted ? Format.DecimalGeodetic : Format.UTM,
                Systems = new()
                {
                    DecimalGeodetic = new()
                    {
                        Latitude = latHasBeenConverted ? lat : 0,
                        Longitude = lngHasBeenConverted ? lng : 0
                    },
                    Utm = new()
                    {
                        Easting = double.TryParse(row[11].ToString(), out double easting) ? easting : 0,
                        Northing = double.TryParse(row[12].ToString(), out double northing) ? northing : 0,
                        ZoneNumber = int.TryParse(row[13].ToString(), out int zoneNumber) ? zoneNumber : 1,
                        ZoneLetter = char.TryParse(row[14].ToString(), out char zoneLetter) ? zoneLetter : 'C',
                    }
                }
            };
            instrument.Automated = row[15].ToString().ToBool();
            instrument.Online = row[16].ToString().ToBool();
            instrument.Model = row[17].ToString();
            instrument.ResponsibleForInstallation = row[18].ToString();
            instrument.InstallationDate = DateTime.TryParse(row[19].ToString(), out DateTime installationDate) ? installationDate : DateTime.UtcNow;

            instrument.SecurityLevels = new();

            instrument.SecurityLevels.Add(new()
            {
                Attention = decimal.TryParse(row[20].ToString(), out decimal aAxisAttention) ? aAxisAttention : null,
                Alert = decimal.TryParse(row[21].ToString(), out decimal aAxisAlert) ? aAxisAlert : null,
                Emergency = decimal.TryParse(row[22].ToString(), out decimal aAxisEmergency) ? aAxisEmergency : null,
                AbruptVariation = decimal.TryParse(row[23].ToString(), out decimal aAxisAbruptVariationBetweenReadings) ? aAxisAbruptVariationBetweenReadings : null,
                Axis = Domain.Enums.Axis.A
            });
            instrument.SecurityLevels.Add(new()
            {
                Attention = decimal.TryParse(row[24].ToString(), out decimal bAxisAttention) ? bAxisAttention : null,
                Alert = decimal.TryParse(row[25].ToString(), out decimal bAxisAlert) ? bAxisAlert : null,
                Emergency = decimal.TryParse(row[26].ToString(), out decimal bAxisEmergency) ? bAxisEmergency : null,
                AbruptVariation = decimal.TryParse(row[27].ToString(), out decimal bAxisAbruptVariationBetweenReadings) ? bAxisAbruptVariationBetweenReadings : null,
                Axis = Domain.Enums.Axis.B
            });
            instrument.SecurityLevels.Add(new()
            {
                Attention = decimal.TryParse(row[28].ToString(), out decimal eAxisAttention) ? eAxisAttention : null,
                Alert = decimal.TryParse(row[29].ToString(), out decimal eAxisAlert) ? eAxisAlert : null,
                Emergency = decimal.TryParse(row[30].ToString(), out decimal eAxisEmergency) ? eAxisEmergency : null,
                AbruptVariation = decimal.TryParse(row[31].ToString(), out decimal eAxisAbruptVariationBetweenReadings) ? eAxisAbruptVariationBetweenReadings : null,
                Axis = Domain.Enums.Axis.E
            });
            instrument.SecurityLevels.Add(new()
            {
                Attention = decimal.TryParse(row[32].ToString(), out decimal nAxisAttention) ? nAxisAttention : null,
                Alert = decimal.TryParse(row[33].ToString(), out decimal nAxisAlert) ? nAxisAlert : null,
                Emergency = decimal.TryParse(row[34].ToString(), out decimal nAxisEmergency) ? nAxisEmergency : null,
                AbruptVariation = decimal.TryParse(row[35].ToString(), out decimal nAxisAbruptVariationBetweenReadings) ? nAxisAbruptVariationBetweenReadings : null,
                Axis = Domain.Enums.Axis.N
            });
            instrument.SecurityLevels.Add(new()
            {
                Attention = decimal.TryParse(row[36].ToString(), out decimal planimetricAxisAttention) ? planimetricAxisAttention : null,
                Alert = decimal.TryParse(row[37].ToString(), out decimal planimetricAxisAlert) ? planimetricAxisAlert : null,
                Emergency = decimal.TryParse(row[38].ToString(), out decimal planimetricAxisEmergency) ? planimetricAxisEmergency : null,
                AbruptVariation = decimal.TryParse(row[39].ToString(), out decimal planimetricAxisAbruptVariationBetweenReadings) ? planimetricAxisAbruptVariationBetweenReadings : null,
                Axis = Domain.Enums.Axis.Planimetric
            });
            instrument.SecurityLevels.Add(new()
            {
                Attention = decimal.TryParse(row[40].ToString(), out decimal zAxisAttention) ? zAxisAttention : null,
                Alert = decimal.TryParse(row[41].ToString(), out decimal zAxisAlert) ? zAxisAlert : null,
                Emergency = decimal.TryParse(row[42].ToString(), out decimal zAxisEmergency) ? zAxisEmergency : null,
                AbruptVariation = decimal.TryParse(row[43].ToString(), out decimal zAxisAbruptVariationBetweenReadings) ? zAxisAbruptVariationBetweenReadings : null,
                Axis = Domain.Enums.Axis.Z
            });

            if (instrument.CoordinateSetting.Format == Format.DecimalGeodetic)
            {
                instrument.CoordinateSetting.Systems.Utm =
                    Helper.ConvertDecimalGeodeticToUTM(instrument.CoordinateSetting.Systems.DecimalGeodetic, instrument.CoordinateSetting.Datum);
            }
            else
            {
                instrument.CoordinateSetting.Systems.DecimalGeodetic =
                    Helper.ConvertUTMToDecimalGeodetic(instrument.CoordinateSetting.Systems.Utm, instrument.CoordinateSetting.Datum);
            }
        }
    }
}
