using Application.Core;
using Application.Extensions;
using Application.Instrument.ConvertFileToUpdate.Strategy;
using Application.Instrument.ConvertFileToUpdate.Strategy.Factory;
using Database.Repositories.Instrument;
using Database.Repositories.Structure;
using Database.Repositories.User;
using ExcelDataReader;
using Model.Instrument._Shared.Measurement;
using Model.Instrument.Convert.Request;
using Model.Instrument.Convert.Response;
using Model.Instrument.Update.Request;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Application.Core.UseCaseResponseFactory<System.Collections.Generic.IEnumerable<Model.Instrument.Convert.Response.ConvertInstrumentsResponse>>;

namespace Application.Instrument.ConvertFileToUpdate
{
    public sealed class ConvertFileToUpdateInstrumentUseCase : IConvertFileToUpdateInstrumentUseCase
    {
        private readonly IUserRepository _userRepository;
        private readonly IStructureRepository _structureRepository;
        private readonly IInstrumentRepository _instrumentRepository;
        private readonly ConvertInstrumentsRequestValidator _requestValidator = new();

        public ConvertFileToUpdateInstrumentUseCase(
            IUserRepository userRepository,
            IStructureRepository structureRepository,
            IInstrumentRepository instrumentRepository)
        {
            _userRepository = userRepository;
            _structureRepository = structureRepository;
            _instrumentRepository = instrumentRepository;
        }

        public async Task<UseCaseResponse<IEnumerable<ConvertInstrumentsResponse>>> Execute(ConvertInstrumentsRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest(null);
                }

                await request.AddRequesterStructures(_userRepository);

                var validationResult = await _requestValidator
                   .ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    return BadRequest(null,
                        validationResult.Errors.ToErrorMessages());
                }

                var instruments = new List<ConvertInstrumentsResponse>();

                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

                using (var stream = request.File.OpenReadStream())
                {
                    using var reader = request.File.ContentType.EndsWith("csv")
                        ? ExcelReaderFactory.CreateCsvReader(stream)
                        : ExcelReaderFactory.CreateReader(stream);

                    var result = reader.AsDataSet(new()
                    {
                        ConfigureDataTable = _ => new()
                        {
                            UseHeaderRow = false,
                            // Reads only rows when the Depth (row index) is greater than 0.
                            FilterRow = rowReader => rowReader.Depth > 0,
                            FilterColumn = (rowReader, columnIndex) => true
                        }
                    });

                    foreach (var row in result.Tables[0].Rows.Cast<DataRow>())
                    {
                        var instrumentSearchIdentifierWasConverted = int.TryParse(row[0].ToString(), out var instrumentSearchIdentifier);

                        if (!instrumentSearchIdentifierWasConverted)
                        {
                            return BadRequest(null, "000", "Instrument ID must be a number.");
                        }

                        var dbInstrument = await _instrumentRepository
                            .GetBySearchIdentifierAsync(instrumentSearchIdentifier);

                        if (dbInstrument == null)
                        {
                            return BadRequest(null, "000",
                                $"Instrument with ID {instrumentSearchIdentifier} not found.");
                        }

                        if (!request.RequestedBySuperSupport
                            && !request.RequestedUserStructures.Contains(dbInstrument.Structure.Id))
                        {
                            return Forbidden(null, "000",
                                $"You are not allowed to update instrument with ID {instrumentSearchIdentifier}.");
                        }

                        if (string.IsNullOrEmpty(row[1].ToString()))
                        {
                            return BadRequest(null, "000",
                                $"Instrument with ID {instrumentSearchIdentifier} does not have a structure with a valid ID.");
                        }

                        var structure = await _structureRepository.GetBySearchIdentifierAsync(int.Parse(row[1].ToString()));

                        if (structure == null)
                        {
                            return BadRequest(null, "000",
                                $"Structure of instrument with ID {instrumentSearchIdentifier} not found.");
                        }

                        if (!request.RequestedBySuperSupport
                            && !request.RequestedUserStructures.Contains(structure.Id))
                        {
                            return Forbidden(null, "000",
                                $"You are not allowed to link instruments with structure {structure.Name}.");
                        }

                        var instrumentInList = instruments.Select(x => x.Updated).FirstOrDefault(x => x.Id == dbInstrument.Id);

                        var instrument = instrumentInList ?? Convert(dbInstrument, structure);

                        var instrumentStrategy =
                            new JsonStrategy(instrument.Type.GetStrategy());

                        instrumentStrategy.SetProperties(instrument, row);

                        if (instrumentInList == null)
                        {
                            instruments.Add(new()
                            {
                                Current = Convert(dbInstrument, dbInstrument.Structure),
                                Updated = instrument
                            });
                        }
                    }
                }

                return Ok(instruments);
            }
            catch (Exception e)
            {
                return InternalServerError(null, errors: e.ToErrorMessages("000"));
            }
        }

        private static UpdateInstrument Convert(Domain.Entities.Instrument instrument, Domain.Entities.Structure structure)
        {
            return new()
            {
                Id = instrument.Id,
                Identifier = instrument.Identifier,
                AlternativeName = instrument.AlternativeName,
                Type = instrument.Type,
                TopQuota = instrument.TopQuota,
                Automated = instrument.Automated,
                Azimuth = instrument.Azimuth,
                Depth = instrument.Depth,
                LowerLimit = instrument.LowerLimit,
                MeasurementFrequency = instrument.MeasurementFrequency?.ToString(@"hh\:mm"),
                UpperLimit = instrument.UpperLimit,
                BaseQuota = instrument.BaseQuota,
                CoordinateSetting = instrument.CoordinateSetting,
                Elevation = instrument.Elevation,
                GeophoneType = instrument.GeophoneType,
                InstallationDate = instrument.InstallationDate,
                Structure = new()
                {
                    Id = structure.Id,
                    Name = structure.Name,
                    ClientUnitId = structure.ClientUnitId
                },
                Model = instrument.Model,
                Online = instrument.Online,
                ResponsibleForInstallation = instrument.ResponsibleForInstallation,
                SecurityLevels = instrument.SecurityLevels?.Select(x => new Model._Shared.SecurityLevels.SecurityLevels()
                            {
                                Id = x.Id,
                                Axis = x.Axis,
                                MaximumDailyRainfall = x.MaximumDailyRainfall,
                                RainIntensity = x.RainIntensity,
                                Emergency = x.Emergency,
                                Alert = x.Alert,
                                Attention = x.Attention,
                                AbruptVariation = x.AbruptVariation,
                            }).ToList(),
                Measurements = instrument.Measurements != null
                            ? instrument.Measurements.Select(x => new MeasurementRequest()
                            {
                                Id = x.Id,
                                Identifier = x.Identifier,
                                AlternativeName = x.AlternativeName,
                                Active = x.Active,
                                DeltaRef = x.DeltaRef,
                                Depth = x.Depth,
                                IsReferential = x.IsReferential,
                                Length = x.Length,
                                Limit = x.Limit,
                                Lithotype = x.Lithotype,
                                Quota = x.Quota,
                                SecurityLevels = x.SecurityLevels != null
                                ? new()
                                {
                                    Id = x.SecurityLevels.Id,
                                    Axis = x.SecurityLevels.Axis,
                                    MaximumDailyRainfall = x.SecurityLevels.MaximumDailyRainfall,
                                    RainIntensity = x.SecurityLevels.RainIntensity,
                                    Emergency = x.SecurityLevels.Emergency,
                                    Alert = x.SecurityLevels.Alert,
                                    Attention = x.SecurityLevels.Attention,
                                    AbruptVariation = x.SecurityLevels.AbruptVariation,
                                }
                                : null
                            }).ToList()
                            : new(),
                LinimetricRulerPosition = instrument.LinimetricRulerPosition
            };
        }
    }
}
