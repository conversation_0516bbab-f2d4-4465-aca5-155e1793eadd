using Application.Core;
using Application.Extensions;
using Database.Repositories.Instrument;
using Database.Repositories.User;
using Model.Instrument.List.Request;
using Model.Instrument.List.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Application.Core.Caching;
using Microsoft.Extensions.Caching.Distributed;
using static Application.Core.UseCaseResponseFactory<System.Collections.Generic.
    IEnumerable<Model.Instrument.List.Response.ListInstrumentResponse>>;

namespace Application.Instrument.List;

public sealed class ListInstrumentUseCase : IListInstrumentUseCase
{
    private readonly IInstrumentRepository _instrumentRepository;
    private readonly IUserRepository _userRepository;
    private readonly IDistributedCache _cache;
    private readonly ListInstrumentRequestValidator _requestValidator = new();

    public ListInstrumentUseCase(
        IInstrumentRepository instrumentRepository,
        IUserRepository userRepository,
        IDistributedCache cache)
    {
        _instrumentRepository = instrumentRepository;
        _userRepository = userRepository;
        _cache = cache;
    }

    public async Task<UseCaseResponse<IEnumerable<ListInstrumentResponse>>>
        Execute(ListInstrumentRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(null, "000", "Request cannot be null.");
            }

            await request.AddRequesterStructures(_userRepository);

            var validationResult = await _requestValidator
                .ValidateAsync(request);

            if (!validationResult.IsValid)
            {
                return BadRequest(
                    null,
                    validationResult.Errors.ToErrorMessages());
            }

            var instruments = await _cache.GetOrCreateAsync(
                CacheKeyGenerator.Generate(request),
                () => _instrumentRepository.ListAsync(request));

            if (instruments == null || !instruments.Any())
            {
                return NoContent();
            }

            return Ok(instruments);
        }
        catch (Exception e)
        {
            return InternalServerError(null, errors: e.ToErrorMessages("000"));
        }
    }
}
