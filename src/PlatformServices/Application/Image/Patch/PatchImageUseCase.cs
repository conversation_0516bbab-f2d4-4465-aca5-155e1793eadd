using Application.Core;
using Application.Extensions;
using Database.Repositories.Instrument;
using Database.Repositories.InstrumentImage;
using Database.Repositories.StructureImage;
using Database.Repositories.User;
using DocumentFormat.OpenXml.Spreadsheet;
using Model.Image.Patch.Request;
using System;
using System.Threading.Tasks;
using static Application.Core.UseCaseResponseFactory<System.Guid>;

namespace Application.Image.Patch
{
    public sealed class PatchImageUseCase : IPatchImageUseCase
    {
        private readonly IStructureImageRepository _structureImageRepository;
        private readonly IInstrumentImageRepository _instrumentImageRepository;
        private readonly IInstrumentRepository _instrumentRepository;
        private readonly IUserRepository _userRepository;
        private readonly PatchImageRequestValidator _requestValidator = new();

        public PatchImageUseCase(
            IStructureImageRepository structureImageRepository,
            IInstrumentImageRepository instrumentImageRepository,
            IInstrumentRepository instrumentRepository,
            IUserRepository userRepository)
        {
            _structureImageRepository = structureImageRepository;
            _instrumentImageRepository = instrumentImageRepository;
            _instrumentRepository = instrumentRepository;
            _userRepository = userRepository;
        }

        public async Task<UseCaseResponse<Guid>> Execute(PatchImageRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest(Guid.Empty, "000", "Request cannot be empty.");
                }

                await request.AddRequesterStructures(_userRepository);

                var validationResult = await _requestValidator
                   .ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    return BadRequest(Guid.Empty,
                        validationResult.Errors.ToErrorMessages());
                }

                var result = await Convert(request);

                return Ok(result);
            }
            catch (Exception e)
            {
                return InternalServerError(Guid.Empty, errors: e.ToErrorMessages("000"));
            }
        }

        private async Task<Guid> Convert(PatchImageRequest request)
        {
            // Trying to find a structure image with the given id
            var structureImage = await _structureImageRepository.GetAsync(request.Id);
            if (structureImage != null)
            {
                // Validating permission
                if (!request.RequestedBySuperSupport &&
                    !request.RequestedUserStructures.Contains(structureImage.StructureId))
                    throw new InvalidOperationException("The requester does not have permission to update this image");

                await _structureImageRepository.UpdateAsync(new()
                {
                    Id = request.Id,
                    Description = request.Description
                });

                return request.Id;
            }
            else {
                var instrumentImage = await _instrumentImageRepository.GetAsync(request.Id);
                if (instrumentImage != null)
                {
                    // Locating the structure of this instrument
                    var instrument = await _instrumentRepository.GetAsync(instrumentImage.InstrumentId);

                    // Checking for permissions
                    if (!request.RequestedBySuperSupport &&
                        !request.RequestedUserStructures.Contains(instrument.Structure.Id))
                    {
                        throw new InvalidOperationException("The requester does not have permission to update this image");
                    }

                    await _instrumentImageRepository.UpdateAsync(new()
                    {
                        Id = request.Id,
                        Description = request.Description
                    });

                    return request.Id;
                }
                else
                    throw new InvalidOperationException("The image with the given id was not found");
            }
        }
    }
}
