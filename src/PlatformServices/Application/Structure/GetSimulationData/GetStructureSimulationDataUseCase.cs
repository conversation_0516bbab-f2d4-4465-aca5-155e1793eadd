using Application.Core;
using Application.Extensions;
using Database.Repositories.Structure;
using Database.Repositories.User;
using Model.Structure.GetSimulationData.Request;
using Model.Structure.GetSimulationData.Response;
using System;
using System.Linq;
using System.Threading.Tasks;
using static Application.Core.UseCaseResponseFactory<Model.Structure.
    GetSimulationData.Response.GetStructureSimulationDataResponse>;

namespace Application.Structure.GetSimulationData;

public sealed class GetStructureSimulationDataUseCase
    : IGetStructureSimulationDataUseCase
{
    private readonly IStructureRepository _structureRepository;
    private readonly IUserRepository _userRepository;

    private readonly GetStructureSimulationDataRequestValidator
        _requestValidator = new();

    public GetStructureSimulationDataUseCase(
        IUserRepository userRepository,
        IStructureRepository structureRepository)
    {
        _userRepository = userRepository;
        _structureRepository = structureRepository;
    }

    public async Task<UseCaseResponse<GetStructureSimulationDataResponse>> Execute(
        GetStructureSimulationDataRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(null, "000", "Request cannot be null.");
            }

            await request.AddRequesterStructures(_userRepository);

            var validationResult = await _requestValidator
                .ValidateAsync(request);

            if (!validationResult.IsValid)
            {
                return BadRequest(
                    null,
                    validationResult.Errors.ToErrorMessages());
            }

            var simulationData = await _structureRepository
                .GetSimulationDataAsync(request);

            if (simulationData == null)
            {
                return NoContent();
            }

            if (!request.RequestedBySuperSupport &&
                request.RequestedUserStructures
                    .All(x => x != simulationData.Structure.Id))
            {
                return Forbidden(
                    null,
                    "000",
                    "You are not allowed to request information from the structure of the requested section.");
            }

            return Ok(simulationData);
        }
        catch (Exception e)
        {
            return InternalServerError(null, errors: e.ToErrorMessages("000"));
        }
    }
}
