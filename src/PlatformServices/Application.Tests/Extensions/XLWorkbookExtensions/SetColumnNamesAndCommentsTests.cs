using System;
using System.Globalization;
using System.Threading;
using Application.Extensions;
using Bogus;
using ClosedXML.Excel;

namespace Application.Tests.Extensions.XLWorkbookExtensions;

[Trait("XLWorkbookExtensions", "SetColumnNamesAndComments")]
public class SetColumnNamesAndCommentsTests
{
    private static readonly Faker Faker = new();

    [Fact(DisplayName = "When workbook is null, then throws ArgumentNullException")]
    public void WhenWorkbookIsNull_ThrowsArgumentNullException()
    {
        XLWorkbook workbook = null;
        var columns = new[] { ("Column1", "Comment1"), ("Column2", "Comment2") };
        const string worksheetName = "TestSheet";

        Action act = () => workbook.SetColumnNamesAndComments(columns, worksheetName);

        act.Should().Throw<ArgumentNullException>();
    }

    [Fact(DisplayName = "When columns array is null, then throws ArgumentNullException")]
    public void WhenColumnsArrayIsNull_ThrowsArgumentNullException()
    {
        using var workbook = new XLWorkbook();
        (string Name, string Comment)[] columns = null;
        const string worksheetName = "TestSheet";

        Action act = () => workbook.SetColumnNamesAndComments(columns, worksheetName);

        act.Should().Throw<ArgumentNullException>();
    }

    [Fact(DisplayName = "When worksheet name is null, then throws ArgumentNullException")]
    public void WhenWorksheetNameIsNull_ThrowsArgumentNullException()
    {
        using var workbook = new XLWorkbook();
        var columns = new[] { ("Column1", "Comment1"), ("Column2", "Comment2") };
        string worksheetName = null;

        Action act = () => workbook.SetColumnNamesAndComments(columns, worksheetName);

        act.Should().Throw<ArgumentNullException>();
    }

    [Fact(DisplayName = "When worksheet name is empty string, then throws ArgumentException")]
    public void WhenWorksheetNameIsEmptyString_ThrowsArgumentException()
    {
        using var workbook = new XLWorkbook();
        var columns = new[] { ("Column1", "Comment1") };
        var worksheetName = "";

        Action act = () => workbook.SetColumnNamesAndComments(columns, worksheetName);

        act.Should().Throw<ArgumentException>().WithMessage("sheetName must not be null or whitespace");
    }

    [Fact(DisplayName = "When columns array is empty, then creates worksheet with no columns")]
    public void WhenColumnsArrayIsEmpty_CreatesWorksheetWithNoColumns()
    {
        using var workbook = new XLWorkbook();
        var columns = Array.Empty<(string Name, string Comment)>();
        var worksheetName = "TestSheet";

        var result = workbook.SetColumnNamesAndComments(columns, worksheetName);

        result.Should().NotBeNull();
        result.Name.Should().Be(worksheetName);
        result.Cell(1, 1).Value.ToString().Should().Be("");
    }

    [Fact(DisplayName = "When valid inputs provided, then creates worksheet with correct name")]
    public void WhenValidInputsProvided_CreatesWorksheetWithCorrectName()
    {
        using var workbook = new XLWorkbook();
        var columns = new[] { ("Column1", "Comment1"), ("Column2", "Comment2") };
        var worksheetName = Faker.Lorem.Word();

        var result = workbook.SetColumnNamesAndComments(columns, worksheetName);

        result.Should().NotBeNull();
        result.Name.Should().Be(worksheetName);
    }

    [Fact(DisplayName = "When valid columns provided, then sets column comments correctly")]
    public void WhenValidColumnsProvided_SetsColumnCommentsCorrectly()
    {
        using var workbook = new XLWorkbook();
        var columns = new[] { ("First Column", "First Comment"), ("Second Column", "Second Comment"), ("Third Column", "Third Comment") };
        var worksheetName = "TestSheet";

        var result = workbook.SetColumnNamesAndComments(columns, worksheetName);

        result.Cell(1, 1).HasComment.Should().BeTrue();
        result.Cell(1, 1).GetComment().Text.Should().Be("First Comment");
        result.Cell(1, 2).HasComment.Should().BeTrue();
        result.Cell(1, 2).GetComment().Text.Should().Be("Second Comment");
        result.Cell(1, 3).HasComment.Should().BeTrue();
        result.Cell(1, 3).GetComment().Text.Should().Be("Third Comment");
    }

    [Fact(DisplayName = "When method called, then sets thread culture to pt-BR")]
    public void WhenMethodCalled_SetsThreadCultureToPtBr()
    {
        // Store original culture
        var originalCulture = Thread.CurrentThread.CurrentCulture;
        var originalUICulture = Thread.CurrentThread.CurrentUICulture;

        try
        {
            // Set different culture initially
            Thread.CurrentThread.CurrentCulture = new CultureInfo("en-US");
            Thread.CurrentThread.CurrentUICulture = new CultureInfo("en-US");

            using var workbook = new XLWorkbook();
            var columns = new[] { ("Column1", "Comment1") };
            var worksheetName = "TestSheet";

            workbook.SetColumnNamesAndComments(columns, worksheetName);

            Thread.CurrentThread.CurrentCulture.Name.Should().Be("pt-BR");
            Thread.CurrentThread.CurrentUICulture.Name.Should().Be("pt-BR");
        }
        finally
        {
            // Restore original culture
            Thread.CurrentThread.CurrentCulture = originalCulture;
            Thread.CurrentThread.CurrentUICulture = originalUICulture;
        }
    }

    [Fact(DisplayName = "When date field column provided, then applies date formatting")]
    public void WhenDateFieldColumnProvided_AppliesDateFormatting()
    {
        using var workbook = new XLWorkbook();
        var columns = new[] { ("Data e hora", "Date and time comment"), ("Regular Column", "Regular comment") };
        var worksheetName = "TestSheet";

        var result = workbook.SetColumnNamesAndComments(columns, worksheetName);

        // Check that date formatting is applied to the date column
        result.Column(1).Style.DateFormat.NumberFormatId.Should().Be(Convert.ToInt32(XLPredefinedFormat.DateTime.Text));
        // Check that regular column doesn't have date formatting
        result.Column(2).Style.DateFormat.NumberFormatId.Should().NotBe(Convert.ToInt32(XLPredefinedFormat.DateTime.Text));
    }

    [Fact(DisplayName = "When installation date field column provided, then applies date formatting")]
    public void WhenInstallationDateFieldColumnProvided_AppliesDateFormatting()
    {
        using var workbook = new XLWorkbook();
        var columns = new[] { ("Data da instalação", "Installation date comment"), ("Regular Column", "Regular comment") };
        var worksheetName = "TestSheet";

        var result = workbook.SetColumnNamesAndComments(columns, worksheetName);

        // Check that date formatting is applied to the installation date column
        result.Column(1).Style.DateFormat.NumberFormatId.Should().Be(Convert.ToInt32(XLPredefinedFormat.DateTime.Text));
        // Check that regular column doesn't have date formatting
        result.Column(2).Style.DateFormat.NumberFormatId.Should().NotBe(Convert.ToInt32(XLPredefinedFormat.DateTime.Text));
    }

    [Fact(DisplayName = "When multiple date fields provided, then applies date formatting to all")]
    public void WhenMultipleDateFieldsProvided_AppliesDateFormattingToAll()
    {
        using var workbook = new XLWorkbook();
        var columns = new[] { ("Data e hora", "Date comment"), ("Data da instalação", "Installation comment"), ("Regular Column", "Regular comment") };
        var worksheetName = "TestSheet";

        var result = workbook.SetColumnNamesAndComments(columns, worksheetName);

        result.Column(1).Style.DateFormat.NumberFormatId.Should().Be(Convert.ToInt32(XLPredefinedFormat.DateTime.Text));
        result.Column(2).Style.DateFormat.NumberFormatId.Should().Be(Convert.ToInt32(XLPredefinedFormat.DateTime.Text));
        result.Column(3).Style.DateFormat.NumberFormatId.Should().NotBe(Convert.ToInt32(XLPredefinedFormat.DateTime.Text));
    }

    [Fact(DisplayName = "When non-date field columns provided, then does not apply date formatting")]
    public void WhenNonDateFieldColumnsProvided_DoesNotApplyDateFormatting()
    {
        using var workbook = new XLWorkbook();
        var columns = new[] { ("Regular Column 1", "Comment 1"), ("Regular Column 2", "Comment 2") };
        var worksheetName = "TestSheet";

        var result = workbook.SetColumnNamesAndComments(columns, worksheetName);

        result.Column(1).Style.DateFormat.NumberFormatId.Should().NotBe(Convert.ToInt32(XLPredefinedFormat.DateTime.Text));
        result.Column(2).Style.DateFormat.NumberFormatId.Should().NotBe(Convert.ToInt32(XLPredefinedFormat.DateTime.Text));
    }

    [Fact(DisplayName = "When method called, then applies correct header styling")]
    public void WhenMethodCalled_AppliesCorrectHeaderStyling()
    {
        using var workbook = new XLWorkbook();
        var columns = new[] { ("Column1", "Comment1"), ("Column2", "Comment2") };
        var worksheetName = "TestSheet";

        var result = workbook.SetColumnNamesAndComments(columns, worksheetName);

        var headerRow = result.Row(1);
        headerRow.Style.Font.Bold.Should().BeTrue();
        headerRow.Style.Font.FontSize.Should().Be(12);
        headerRow.Style.Fill.BackgroundColor.Should().Be(XLColor.DarkGreen);
        headerRow.Style.Font.FontColor.Should().Be(XLColor.White);
    }

    [Fact(DisplayName = "When method called, then adjusts columns to contents")]
    public void WhenMethodCalled_AdjustsColumnsToContents()
    {
        using var workbook = new XLWorkbook();
        var columns = new[] { ("Short", "Short comment"), ("Very Long Column Name", "Very long comment text") };
        var worksheetName = "TestSheet";

        var result = workbook.SetColumnNamesAndComments(columns, worksheetName);

        // Verify that columns have been adjusted (width should be greater than default)
        result.Column(1).Width.Should().BeGreaterThan(0);
        result.Column(2).Width.Should().BeGreaterThan(0);
        result.Column(2).Width.Should().BeGreaterThan(result.Column(1).Width);
    }
}
