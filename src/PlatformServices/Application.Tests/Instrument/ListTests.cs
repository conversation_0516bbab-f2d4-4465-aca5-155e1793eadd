using System;
using System.Threading.Tasks;
using Application.Core;
using Application.Instrument.List;
using Application.Tests.Instrument.Builders;
using Database.Repositories.Instrument;
using Database.Repositories.User;
using Microsoft.Extensions.Caching.Distributed;
using Model.Instrument.List.Request;

namespace Application.Tests.Instrument;

[Trait("ListInstrumentUseCase", "Execute")]
public class ListTests
{
    private readonly Mock<IInstrumentRepository> _instrumentRepository = new();
    private readonly Mock<IUserRepository> _userRepository = new();
    private readonly Mock<IDistributedCache> _cache = new();
    private readonly IListInstrumentUseCase _listInstrumentUseCase;

    public ListTests()
    {
        _listInstrumentUseCase = new ListInstrumentUseCase(
            _instrumentRepository.Object,
            _userRepository.Object,
            _cache.Object);
    }

    [Fact(DisplayName = "When an unexpected exception is thrown, then returns InternalServerError")]
    public async Task WhenAnUnexpectedExceptionIsThrown_ReturnsInternalServerError()
    {
        _instrumentRepository
            .Setup(x => x.ListAsync(It.IsAny<ListInstrumentRequest>()))
            .Throws(new Exception());

        var useCaseResponse = await _listInstrumentUseCase
            .Execute(ListInstrumentRequestBuilder.CreateDefault());

        useCaseResponse.Status.Should()
            .Be(UseCaseResponseKind.InternalServerError);
    }

    [Fact(DisplayName = "When request is right, then returns OK")]
    public async Task WhenRequestIsRight_ReturnsOK()
    {
        _instrumentRepository
            .Setup(x => x.ListAsync(It.IsAny<ListInstrumentRequest>()))
            .ReturnsAsync(ListInstrumentResponseBuilder.CreateDefault());

        var useCaseResponse = await _listInstrumentUseCase
            .Execute(ListInstrumentRequestBuilder.CreateDefault());

        useCaseResponse.Status.Should().Be(UseCaseResponseKind.OK);
    }
}