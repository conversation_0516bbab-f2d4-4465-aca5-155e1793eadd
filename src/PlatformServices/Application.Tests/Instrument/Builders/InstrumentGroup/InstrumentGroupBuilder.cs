using System;

namespace Application.Tests.Instrument.Builders.InstrumentGroup
{
    public static class InstrumentGroupBuilder
    {
        public static Domain.Entities.InstrumentGroup CreateDefault()
        {
            var group = new Domain.Entities.InstrumentGroup()
            {
                Name = "Group 1"
            };

            group.AddInstrument(new() { Id = Guid.NewGuid() });

            return group;
        }
    }
}
