using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Application.Core;
using Application.Instrument.ConvertFileToAdd;
using Application.Tests.Instrument.Builders.Convert;
using Application.Tests.Structure.Builders;
using Database.Repositories.Structure;
using Database.Repositories.User;
using Model.Instrument.Convert.Request;

namespace Application.Tests.Instrument;

[Trait("ConvertFileToAddInstrumentUseCase", "Execute")]
public class ConvertToAddTests
{
    private readonly Mock<IUserRepository> _userRepository;
    private readonly Mock<IStructureRepository> _structureRepository;
    private readonly ConvertFileToAddInstrumentUseCase _useCase;

    public ConvertToAddTests()
    {
        _structureRepository = new();
        _userRepository = new();

        _useCase =
            new ConvertFileToAddInstrumentUseCase(
                _userRepository.Object,
                _structureRepository.Object
            );

        // Required to convert decimal numbers from file to object
        Thread.CurrentThread.CurrentCulture = new CultureInfo(
            "en-US",
            false);

        GraphicsEngine.UpdateGraphicsEngineFonts();

        // Setup both singular and plural structure repository methods
        // Create structures for all SearchIdentifier values used by different instrument types
        var structures = new List<Domain.Entities.Structure>();
        var searchIdentifiers = new[] { 1, 2, 3, 4, 5, 6, 8, 9 }; // All identifiers used by test data

        foreach (var searchId in searchIdentifiers)
        {
            var structure = StructureBuilder.CreateDefault();
            structure.SearchIdentifier = searchId;
            structures.Add(structure);
        }

        _structureRepository
            .Setup(x => x.GetBySearchIdentifiersAsync(
                It.IsAny<IEnumerable<int>>()))
            .ReturnsAsync((IEnumerable<int> ids) =>
                structures.Where(s => ids.Contains(s.SearchIdentifier)).ToList());

        _structureRepository
            .Setup(x => x.GetBySearchIdentifierAsync(It.IsAny<int>()))
            .ReturnsAsync((int id) =>
                structures.FirstOrDefault(s => s.SearchIdentifier == id));
    }

    [Fact(DisplayName = "When user does not have permission in the structure, then returns Forbidden")]
    public async Task WhenUserDoesNotHavePermissionInTheStructure_ReturnsForbidden()
    {
        var response = await _useCase
            .Execute(GetFileInstrument
                .GetCSVAddInclinometers()
                .WithRequest(false, new() { Guid.NewGuid() }));

        response.Status.Should().Be(UseCaseResponseKind.Forbidden);
    }

    [Fact(DisplayName = "When conventional inclinometer CSV is valid, then returns OK")]
    public async Task WhenConventionalInclinometerCsvIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument
                .GetCSVAddInclinometers());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When electric piezometer CSV is valid, then returns OK")]
    public async Task WhenElectricPiezometerCsvIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetCSVAddElectricPiezometer());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When geophone CSV is valid, then returns OK")]
    public async Task WhenGeophoneCsvIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetCSVAddGeophone());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When linimetric ruler CSV is valid, then returns OK")]
    public async Task WhenLinimetricRulerCsvIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetCSVAddLinimetricRuler());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When open standpipe piezometer CSV is valid, then returns OK")]
    public async Task WhenOpenStandpipePiezometerCsvIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetCSVAddOpenStandpipePiezometer());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When prism CSV is valid, then returns OK")]
    public async Task WhenPrismCsvIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetCSVAddPrism());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When settlement gauge CSV is valid, then returns OK")]
    public async Task WhenSettlementGaugeCsvIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetCSVAddSettlementGauge());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When surface landmark CSV is valid, then returns OK")]
    public async Task WhenSurfaceLandmarkCsvIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetCSVAddSurfaceLandmark());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When water level indicator CSV is valid, then returns OK")]
    public async Task WhenWaterLevelIndicatorCsvIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetCSVAddWaterLevelIndicator());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When pluviometer CSV is valid, then returns OK")]
    public async Task WhenPluviometerCsvIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetCSVAddPluviometer());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When pluviograph CSV is valid, then returns OK")]
    public async Task WhenPluviographCsvIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetCSVAddPluviograph());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When conventional inclinometer XLSX is valid, then returns OK")]
    public async Task WhenConventionalInclinometerXlsxIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetXLSXAddInclinometers());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When electric piezometer XLSX is valid, then returns OK")]
    public async Task WhenElectricPiezometerXlsxIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetXLSXAddElectricPiezometer());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When geophone XLSX is valid, then returns OK")]
    public async Task WhenGeophoneXlsxIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetXLSXAddGeophone());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When open standpipe piezometer XLSX is valid, then returns OK")]
    public async Task WhenOpenStandpipePiezometerXlsxIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetXLSXAddOpenStandpipePiezometer());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When prism XLSX is valid, then returns OK")]
    public async Task WhenPrismXlsxIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetXLSXAddPrism());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When settlement gauge XLSX is valid, then returns OK")]
    public async Task WhenSettlementGaugeXlsxIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetXLSXAddSettlementGauge());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When surface landmark XLSX is valid, then returns OK")]
    public async Task WhenSurfaceLandmarkXlsxIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetXLSXAddSurfaceLandmark());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When water level indicator XLSX is valid, then returns OK")]
    public async Task WhenWaterLevelIndicatorXlsxIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetXLSXAddWaterLevelIndicator());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When linimetric ruler XLSX is valid, then returns OK")]
    public async Task WhenLinimetricRulerXlsxIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetXLSXAddLinimetricRuler());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When pluviometer XLSX is valid, then returns OK")]
    public async Task WhenPluviometerXlsxIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetXLSXAddPluviometer());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When pluviograph XLSX is valid, then returns OK")]
    public async Task WhenPluviographXlsxIsValid_ReturnsOK()
    {
        var response = await _useCase
            .Execute(GetFileInstrument.GetXLSXAddPluviograph());

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "When request is null, then returns BadRequest")]
    public async Task WhenRequestIsNull_ReturnsBadRequest()
    {
        ConvertInstrumentsRequest request = null;

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "When structure not found, then returns BadRequest")]
    public async Task WhenStructureNotFound_ReturnsBadRequest()
    {
        _structureRepository
            .Setup(x => x.GetBySearchIdentifiersAsync(It.IsAny<IEnumerable<int>>()))
            .ReturnsAsync(new List<Domain.Entities.Structure>());

        var response = await _useCase
            .Execute(GetFileInstrument.GetCSVAddInclinometers());

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
        response.Errors.Should().Contain(e => e.Message.Contains("not found"));
    }
}