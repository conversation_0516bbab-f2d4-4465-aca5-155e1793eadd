using Domain.Enums;
using Model.Chart.PercolationInstruments.Request;
using System;
using System.Collections.Generic;

namespace Application.Tests.Chart.Builder
{
    public static class GetPercolationInstrumentDataRequestBuilder
    {
        public static GetPercolationInstrumentDataRequest CreateDefault()
        {
            return new GetPercolationInstrumentDataRequest
            {
                Period = FilterPercolationInstrumentPeriod.OneYear,
                Instruments = new List<Guid> { Guid.NewGuid() },
                RequestedBy = Guid.NewGuid(),
                RequestedUserRole = Domain.Enums.Role.SuperSupport,
                RequestedBySuperSupport = true
            };
        }

        public static GetPercolationInstrumentDataRequest WithRequestedByNonSuperSupport(this GetPercolationInstrumentDataRequest request)
        {
            request.RequestedBySuperSupport = false;
            request.RequestedUserRole = Domain.Enums.Role.Administrator;
            request.RequestedUserStructures = new() { Guid.NewGuid() };

            return request;
        }

        public static GetPercolationInstrumentDataRequest WithInstruments(this GetPercolationInstrumentDataRequest request, List<Guid> instruments)
        {
            request.Instruments = instruments;
            return request;
        }
    }
}
