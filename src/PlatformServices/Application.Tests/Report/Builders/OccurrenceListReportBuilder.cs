using Domain.Entities;
using System;

namespace Application.Tests.Report.Builders
{
    public static class OccurrenceListReportBuilder
    {
        public static OccurrenceListReport CreateDefault()
        {
            return new()
            {
                Id = Guid.NewGuid(),
                DateFilter = Domain.Enums.OccurrenceDateFilter.DaysRemainingInDeadline,
                CreatedBy = new() { Id = Guid.NewGuid(), Username = "", FirstName = "", Surname = "" },
                CreatedDate = DateTime.Now,
                DaysRemaining = 1,
                EndDate = DateTime.Now,
                StartDate = DateTime.Now,
                InspectionSheetType = Domain.Enums.InspectionSheetType.Fie,
                ReportStatus = Domain.Enums.OccurrenceListReportStatus.Completed,
                Status = Domain.Enums.OccurrenceStatus.Completed,
                WithActionPlan = false,
                Structures = new()
                {
                    new()
                    {
                        Id = Guid.NewGuid(),
                        ClientUnit = new()
                        {
                            Id = Guid.NewGuid(),
                            ClientId = Guid.NewGuid(),
                        },
                        Client = new()
                        {
                            Id = Guid.NewGuid(),
                            Name = "Client",
                            Active = true
                        },
                    }
                }
            };
        }
    }
}
