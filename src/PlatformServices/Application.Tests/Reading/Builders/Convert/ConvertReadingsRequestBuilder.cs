using ClosedXML.Excel;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Internal;
using Model.Reading.Convert.Request;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Domain.Enums;

namespace Application.Tests.Reading.Builders.Convert;

public enum FileType
{
    CSV,
    XLSX
}
    
public static class ConvertReadingsRequestBuilder
{
    public static ConvertReadingsRequest CreateEmpty()
    {
        var stringBuilder = new StringBuilder();

        stringBuilder.AppendLine("Identificador do instrumento,Identificador do ponto de medição,Ref,Data e hora,A Positivo,A Negativo,B Positivo,B Negativo,Deslocamento médio do eixo A,Deslocamento médio do eixo B");

        return Generate(stringBuilder);
    }
    
    public static ConvertReadingsRequest CreateInvalid(string instrumentIdentifier)
    {
        var stringBuilder = new StringBuilder();

        stringBuilder.AppendLine("Identificador do instrumento,Identificador do ponto de medição,Ref,Data e hora,A Positivo,A Negativo,B Positivo,B Negativo,Deslocamento médio do eixo A,Deslocamento médio do eixo B");
        stringBuilder.AppendLine($"{instrumentIdentifier},null,AAA,01/01/2023 00:00:00,null,-0.1,AAA,null,0.05,0.05");

        return Generate(stringBuilder);
    }
    
    public static ConvertReadingsRequest Create(
        List<Domain.Entities.Reading> readings,
        InstrumentType instrumentType,
        FileType fileType)
    {
        return (instrumentType, fileType) switch
        {
            (InstrumentType.WaterLevelIndicator, FileType.CSV) => GetCSVAddWaterLevelIndicator(readings),
            (InstrumentType.WaterLevelIndicator, FileType.XLSX) => GetXLSXAddWaterLevelIndicator(readings),
            (InstrumentType.OpenStandpipePiezometer, FileType.CSV) => GetCSVAddOpenStandpipePiezometer(readings),
            (InstrumentType.OpenStandpipePiezometer, FileType.XLSX) => GetXLSXAddOpenStandpipePiezometer(readings),
            (InstrumentType.ElectricPiezometer, FileType.CSV) => GetCSVAddElectricPiezometer(readings),
            (InstrumentType.ElectricPiezometer, FileType.XLSX) => GetXLSXAddElectricPiezometer(readings),
            (InstrumentType.ConventionalInclinometer, FileType.CSV) => GetCSVAddConventionalInclinometer(readings),
            (InstrumentType.ConventionalInclinometer, FileType.XLSX) => GetXLSXAddConventionalInclinometer(readings),
            (InstrumentType.IPIInclinometer, FileType.CSV) => GetCSVAddIPIInclinometer(readings),
            (InstrumentType.IPIInclinometer, FileType.XLSX) => GetXLSXAddIPIInclinometer(readings),
            (InstrumentType.SurfaceLandmark, FileType.CSV) => GetCSVAddSurfaceLandmark(readings),
            (InstrumentType.SurfaceLandmark, FileType.XLSX) => GetXLSXAddSurfaceLandmark(readings),
            (InstrumentType.Prism, FileType.CSV) => GetCSVAddPrism(readings),
            (InstrumentType.Prism, FileType.XLSX) => GetXLSXAddPrism(readings),
            (InstrumentType.SettlementGauge, FileType.CSV) => GetCSVAddSettlementGauge(readings),
            (InstrumentType.SettlementGauge, FileType.XLSX) => GetXLSXAddSettlementGauge(readings),
            (InstrumentType.Geophone, FileType.CSV) => GetCSVAddGeophone(readings),
            (InstrumentType.Geophone, FileType.XLSX) => GetXLSXAddGeophone(readings),
            (InstrumentType.LinimetricRuler, FileType.CSV) => GetCSVAddLinimetricRuler(readings),
            (InstrumentType.LinimetricRuler, FileType.XLSX) => GetXLSXAddLinimetricRuler(readings),
            (InstrumentType.Pluviometer, FileType.CSV) => GetCSVAddPluviometer(readings),
            (InstrumentType.Pluviometer, FileType.XLSX) => GetXlsxAddPluviometer(readings),
            (InstrumentType.Pluviograph, FileType.CSV) => GetCSVAddPluviograph(readings),
            (InstrumentType.Pluviograph, FileType.XLSX) => GetXlsxAddPluviograph(readings),
            _ => throw new NotSupportedException($"File type {fileType} for instrument type {instrumentType} is not supported.")
        };
    }
    
    public static ConvertReadingsRequest GetCSVAddConventionalInclinometer(List<Domain.Entities.Reading> readings)
    {
        var stringBuilder = new StringBuilder();

        stringBuilder.AppendLine("Identificador do instrumento,Identificador do ponto de medição,Ref,Data e hora,A Positivo,A Negativo,B Positivo,B Negativo,Deslocamento médio do eixo A,Deslocamento médio do eixo B");

        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                stringBuilder.AppendLine($"{reading.Instrument.Identifier},{value.Measurement.Identifier},1,{value.Date:dd/MM/yyyy},{value.PositiveA},{value.NegativeA},{value.PositiveB},{value.PositiveB},{value.AverageDisplacementA},{value.PositiveB}");
            }
        }

        return Generate(stringBuilder);
    }

    public static ConvertReadingsRequest GetCSVAddPluviograph(List<Domain.Entities.Reading> readings)
    {
        var stringBuilder = new StringBuilder();

        stringBuilder.AppendLine("Identificador do instrumento,Data e hora,Pluviometria");

        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                stringBuilder.AppendLine($"{reading.Instrument.Identifier},{value.Date:dd/MM/yyyy},{value.Pluviometry}");
            }
        }

        return Generate(stringBuilder);
    }

    public static ConvertReadingsRequest GetCSVAddPluviometer(List<Domain.Entities.Reading> readings)
    {
        var stringBuilder = new StringBuilder();

        stringBuilder.AppendLine("Identificador do instrumento,Data e hora,Pluviometria");

        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                stringBuilder.AppendLine($"{reading.Instrument.Identifier},{value.Date:dd/MM/yyyy},{value.Pluviometry}");
            }
        }

        return Generate(stringBuilder);
    }

    public static ConvertReadingsRequest GetXlsxAddPluviometer(List<Domain.Entities.Reading> readings)
    {
        var workbook = InitializeWorkbook("Leituras de Pluviômetro");
        var ws = workbook.Worksheets.FirstOrDefault() ?? throw new InvalidOperationException("Worksheet could not be created.");

        var columns = new string[]
        {
            "Identificador do instrumento",
            "Data e Hora",
            "Pluviometria"
        };

        SetHeaderStyle(ws);
        AddColumnTitles(ws, columns);

        var row = 2;
        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                ws.Column(1).Cell(row).Value = reading.Instrument.Identifier;
                ws.Column(2).Cell(row).Value = value.Date.ToString("dd/MM/yyyy");
                ws.Column(3).Cell(row).Value = value.Pluviometry;

                row++;
            }
        }

        ws.Columns().AdjustToContents();

        return Generate(workbook);
    }

    public static ConvertReadingsRequest GetXlsxAddPluviograph(List<Domain.Entities.Reading> readings)
    {
        var workbook = InitializeWorkbook("Leituras de Pluviógrafo");
        var ws = workbook.Worksheets.FirstOrDefault() ?? throw new InvalidOperationException("Worksheet could not be created.");

        var columns = new string[]
        {
            "Identificador do instrumento",
            "Data e Hora",
            "Pluviometria"
        };

        SetHeaderStyle(ws);
        AddColumnTitles(ws, columns);

        var row = 2;
        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                ws.Column(1).Cell(row).Value = reading.Instrument.Identifier;
                ws.Column(2).Cell(row).Value = value.Date.ToString("dd/MM/yyyy");
                ws.Column(3).Cell(row).Value = value.Pluviometry;

                row++;
            }
        }

        ws.Columns().AdjustToContents();

        return Generate(workbook);
    }

    public static ConvertReadingsRequest GetXLSXAddConventionalInclinometer(List<Domain.Entities.Reading> readings)
    {
        var workbook = InitializeWorkbook("Leituras de Inc. Conv.");
        var ws = workbook.Worksheets.FirstOrDefault() ?? throw new InvalidOperationException("Worksheet could not be created.");

        var columns = new string[]
        {
            "Identificador do instrumento",
            "Identificador do ponto de medição",
            "Ref",
            "Data e hora",
            "A positivo",
            "A negativo",
            "B positivo",
            "B negativo",
            "Deslocamento médio do eixo A",
            "Deslocamento médio do eixo B",
        };

        SetHeaderStyle(ws);
        AddColumnTitles(ws, columns);

        var row = 2;
        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                ws.Column(1).Cell(row).Value = reading.Instrument.Identifier;
                ws.Column(2).Cell(row).Value = value.Measurement.Identifier;
                ws.Column(3).Cell(row).Value = true;
                ws.Column(4).Cell(row).Value = value.Date.ToString("dd/MM/yyyy");
                ws.Column(5).Cell(row).Value = value.PositiveA;
                ws.Column(6).Cell(row).Value = value.NegativeA;
                ws.Column(7).Cell(row).Value = value.PositiveB;
                ws.Column(8).Cell(row).Value = value.NegativeB;
                ws.Column(9).Cell(row).Value = value.AverageDisplacementA;
                ws.Column(10).Cell(row).Value = value.AverageDisplacementB;

                row++;
            }
        }

        ws.Columns().AdjustToContents();

        return Generate(workbook);
    }

    public static ConvertReadingsRequest GetCSVAddIPIInclinometer(List<Domain.Entities.Reading> readings)
    {
        var stringBuilder = new StringBuilder();

        stringBuilder.AppendLine("Identificador do instrumento,Identificador do ponto de medição,Ref,Data e hora,Leitura do eixo A,Leitura do eixo B");

        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                stringBuilder.AppendLine($"{reading.Instrument.Identifier},{value.Measurement.Identifier},1,{value.Date:dd/MM/yyyy},{value.AAxisReading},{value.BAxisReading}");
            }
        }

        return Generate(stringBuilder);
    }

    public static ConvertReadingsRequest GetXLSXAddIPIInclinometer(List<Domain.Entities.Reading> readings)
    {
        var workbook = InitializeWorkbook("Leituras de Inc. IPI");
        var ws = workbook.Worksheets.FirstOrDefault() ?? throw new InvalidOperationException("Worksheet could not be created.");

        var columns = new string[]
        {
            "Identificador do instrumento",
            "Identificador do ponto de medição",
            "Ref",
            "Data e hora",
            "Leitura do eixo A",
            "Leitura do eixo B"
        };

        SetHeaderStyle(ws);
        AddColumnTitles(ws, columns);

        var row = 2;
        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                ws.Column(1).Cell(row).Value = reading.Instrument.Identifier;
                ws.Column(2).Cell(row).Value = value.Measurement.Identifier;
                ws.Column(3).Cell(row).Value = true;
                ws.Column(4).Cell(row).Value = value.Date.ToString("dd/MM/yyyy");
                ws.Column(5).Cell(row).Value = value.AAxisReading;
                ws.Column(6).Cell(row).Value = value.BAxisReading;

                row++;
            }
        }

        ws.Columns().AdjustToContents();

        return Generate(workbook);
    }

    public static ConvertReadingsRequest GetCSVAddElectricPiezometer(List<Domain.Entities.Reading> readings)
    {
        var stringBuilder = new StringBuilder();

        stringBuilder.AppendLine("Identificador do instrumento,Identificador da célula de pressão,Data e hora,Cota,Seco");

        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                stringBuilder.AppendLine($"{reading.Instrument.Identifier},{value.Measurement.Identifier},{value.Date:dd/MM/yyyy},{value.Quota},{value.Dry}");
            }
        }

        return Generate(stringBuilder);
    }

    public static ConvertReadingsRequest GetXLSXAddElectricPiezometer(List<Domain.Entities.Reading> readings)
    {
        var workbook = InitializeWorkbook("Leituras de PZE");
        var ws = workbook.Worksheets.FirstOrDefault() ?? throw new InvalidOperationException("Worksheet could not be created.");

        var columns = new string[]
        {
            "Identificador do instrumento",
            "Identificador da célula de pressão",
            "Data e hora",
            "Cota",
            "Seco"
        };

        SetHeaderStyle(ws);
        AddColumnTitles(ws, columns);

        var row = 2;
        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                ws.Column(1).Cell(row).Value = reading.Instrument.Identifier;
                ws.Column(2).Cell(row).Value = value.Measurement.Identifier;
                ws.Column(3).Cell(row).Value = value.Date.ToString("dd/MM/yyyy");
                ws.Column(4).Cell(row).Value = value.Quota;
                ws.Column(5).Cell(row).Value = value.Dry.ToString();

                row++;
            }
        }

        ws.Columns().AdjustToContents();

        return Generate(workbook);
    }

    public static ConvertReadingsRequest GetCSVAddOpenStandpipePiezometer(List<Domain.Entities.Reading> readings)
    {
        var stringBuilder = new StringBuilder();

        stringBuilder.AppendLine("Identificador do instrumento,Data e hora,Cota,Profundidade,Pressão,Seco");

        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                stringBuilder.AppendLine($"{reading.Instrument.Identifier},{value.Date:dd/MM/yyyy},{value.Quota},{value.Depth},{value.Pressure},{value.Dry}");
            }
        }

        return Generate(stringBuilder);
    }

    public static ConvertReadingsRequest GetXLSXAddOpenStandpipePiezometer(List<Domain.Entities.Reading> readings)
    {
        var workbook = InitializeWorkbook("Leituras de PZ");
        var ws = workbook.Worksheets.FirstOrDefault() ?? throw new InvalidOperationException("Worksheet could not be created.");

        var columns = new string[]
        {
            "Identificador do instrumento",
            "Data e hora",
            "Cota",
            "Profundidade",
            "Pressão",
            "Seco"
        };

        SetHeaderStyle(ws);
        AddColumnTitles(ws, columns);

        var row = 2;
        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                ws.Column(1).Cell(row).Value = reading.Instrument.Identifier;
                ws.Column(2).Cell(row).Value = value.Date.ToString("dd/MM/yyyy");
                ws.Column(3).Cell(row).Value = value.Quota;
                ws.Column(4).Cell(row).Value = value.Depth;
                ws.Column(5).Cell(row).Value = value.Pressure;
                ws.Column(6).Cell(row).Value = value.Dry.ToString();

                row++;
            }
        }

        ws.Columns().AdjustToContents();

        return Generate(workbook);
    }

    public static ConvertReadingsRequest GetCSVAddGeophone(List<Domain.Entities.Reading> readings)
    {
        var stringBuilder = new StringBuilder();

        stringBuilder.AppendLine("Identificador do instrumento,Data e hora,Natureza,PGA eixo A,PGA eixo B,PGA eixo Z,Coordenada epicentro E,Coordenada epicentro N");

        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                stringBuilder.AppendLine($"{reading.Instrument.Identifier},{value.Date:dd/MM/yyyy},{value.Nature.Description},{value.AAxisPga},{value.BAxisPga},{value.ZAxisPga},{value.EastCoordinate},{value.NorthCoordinate}");
            }
        }

        return Generate(stringBuilder);
    }

    public static ConvertReadingsRequest GetXLSXAddGeophone(List<Domain.Entities.Reading> readings)
    {
        var workbook = InitializeWorkbook("Leituras de geofones");
        var ws = workbook.Worksheets.FirstOrDefault() ?? throw new InvalidOperationException("Worksheet could not be created.");

        var columns = new string[]
        {
            "Identificador do instrumento",
            "Data e hora",
            "Natureza",
            "PGA eixo A",
            "PGA eixo B",
            "PGA eixo Z",
            "Coordenada epicentro E",
            "Coordenada epicentro N"
        };

        SetHeaderStyle(ws);
        AddColumnTitles(ws, columns);

        var row = 2;
        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                ws.Column(1).Cell(row).Value = reading.Instrument.Identifier;
                ws.Column(2).Cell(row).Value = value.Date.ToString("dd/MM/yyyy");
                ws.Column(3).Cell(row).Value = value.Nature.Description;
                ws.Column(4).Cell(row).Value = value.AAxisPga;
                ws.Column(5).Cell(row).Value = value.BAxisPga;
                ws.Column(6).Cell(row).Value = value.ZAxisPga;
                ws.Column(7).Cell(row).Value = value.EastCoordinate;
                ws.Column(8).Cell(row).Value = value.NorthCoordinate;

                row++;
            }
        }

        ws.Columns().AdjustToContents();

        return Generate(workbook);
    }

    public static ConvertReadingsRequest GetCSVAddLinimetricRuler(List<Domain.Entities.Reading> readings)
    {
        var stringBuilder = new StringBuilder();

        stringBuilder.AppendLine("Identificador do instrumento,Data e hora,Cota NA");

        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                stringBuilder.AppendLine($"{reading.Instrument.Identifier},{value.Date:dd/MM/yyyy},{value.Quota}");
            }
        }

        return Generate(stringBuilder);
    }

    public static ConvertReadingsRequest GetXLSXAddLinimetricRuler(List<Domain.Entities.Reading> readings)
    {
        var workbook = InitializeWorkbook("Leituras de régua linimétrica");
        var ws = workbook.Worksheets.FirstOrDefault() ?? throw new InvalidOperationException("Worksheet could not be created.");

        var columns = new string[]
        {
            "Identificador do instrumento",
            "Data e hora",
            "Cota NA"
        };

        SetHeaderStyle(ws);
        AddColumnTitles(ws, columns);

        var row = 2;
        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                ws.Column(1).Cell(row).Value = reading.Instrument.Identifier;
                ws.Column(2).Cell(row).Value = value.Date.ToString("dd/MM/yyyy");
                ws.Column(3).Cell(row).Value = value.Quota;

                row++;
            }
        }

        ws.Columns().AdjustToContents();

        return Generate(workbook);
    }

    public static ConvertReadingsRequest GetCSVAddPrism(List<Domain.Entities.Reading> readings)
    {
        var stringBuilder = new StringBuilder();

        stringBuilder.AppendLine("Identificador do instrumento,Data e hora,Datum,Coord. E,Coord. N,Cota");

        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                stringBuilder.AppendLine($"{reading.Instrument.Identifier},{value.Date:dd/MM/yyyy},{value.Datum},{value.EastCoordinate},{value.NorthCoordinate},{value.Quota}");
            }
        }

        return Generate(stringBuilder);
    }

    public static ConvertReadingsRequest GetXLSXAddPrism(List<Domain.Entities.Reading> readings)
    {
        var workbook = InitializeWorkbook("Leituras de prisma");
        var ws = workbook.Worksheets.FirstOrDefault() ?? throw new InvalidOperationException("Worksheet could not be created.");

        var columns = new string[]
        {
            "Identificador do instrumento",
            "Data e hora",
            "Datum",
            "Coord. E",
            "Coord. N",
            "Cota"
        };

        SetHeaderStyle(ws);
        AddColumnTitles(ws, columns);

        var row = 2;
        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                ws.Column(1).Cell(row).Value = reading.Instrument.Identifier;
                ws.Column(2).Cell(row).Value = value.Date.ToString("dd/MM/yyyy");
                ws.Column(3).Cell(row).Value = (int)value.Datum;
                ws.Column(4).Cell(row).Value = value.EastCoordinate;
                ws.Column(5).Cell(row).Value = value.NorthCoordinate;
                ws.Column(6).Cell(row).Value = value.Quota;

                row++;
            }
        }

        ws.Columns().AdjustToContents();

        return Generate(workbook);
    }

    public static ConvertReadingsRequest GetCSVAddSettlementGauge(List<Domain.Entities.Reading> readings)
    {
        var stringBuilder = new StringBuilder();

        stringBuilder.AppendLine("Identificador do instrumento,Identificador do anel magnético,Data e hora,Profundidade absoluta,Recalque absoluto");

        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                stringBuilder.AppendLine($"{reading.Instrument.Identifier},{value.Measurement.Identifier},{value.Date:dd/MM/yyyy},{value.Depth},{value.AbsoluteSettlement}");
            }
        }

        return Generate(stringBuilder);
    }

    public static ConvertReadingsRequest GetXLSXAddSettlementGauge(List<Domain.Entities.Reading> readings)
    {
        var workbook = InitializeWorkbook("Leituras de med. de recalque");
        var ws = workbook.Worksheets.FirstOrDefault() ?? throw new InvalidOperationException("Worksheet could not be created.");

        var columns = new string[]
        {
            "Identificador do instrumento",
            "Identificador do anel magnético",
            "Data e hora",
            "Profundidade absoluta",
            "Recalque absoluto"
        };

        SetHeaderStyle(ws);
        AddColumnTitles(ws, columns);

        var row = 2;
        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                ws.Column(1).Cell(row).Value = reading.Instrument.Identifier;
                ws.Column(2).Cell(row).Value = value.Measurement.Identifier;
                ws.Column(3).Cell(row).Value = value.Date.ToString("dd/MM/yyyy");
                ws.Column(4).Cell(row).Value = value.Depth;
                ws.Column(5).Cell(row).Value = value.AbsoluteSettlement;

                row++;
            }
        }

        ws.Columns().AdjustToContents();

        return Generate(workbook);
    }

    public static ConvertReadingsRequest GetCSVAddSurfaceLandmark(List<Domain.Entities.Reading> readings)
    {
        var stringBuilder = new StringBuilder();

        stringBuilder.AppendLine("Identificador do instrumento,Data e hora,Datum,Coord. E,Coord. N,Cota");

        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                stringBuilder.AppendLine($"{reading.Instrument.Identifier},{value.Date:dd/MM/yyyy},{value.Datum},{value.EastCoordinate},{value.NorthCoordinate},{value.Quota}");
            }
        }

        return Generate(stringBuilder);
    }

    public static ConvertReadingsRequest GetXLSXAddSurfaceLandmark(List<Domain.Entities.Reading> readings)
    {
        var workbook = InitializeWorkbook("Leituras de marco superficial");
        var ws = workbook.Worksheets.FirstOrDefault() ?? throw new InvalidOperationException("Worksheet could not be created.");

        var columns = new string[]
        {
            "Identificador do instrumento",
            "Data e hora",
            "Datum",
            "Coord. E",
            "Coord. N",
            "Cota"
        };

        SetHeaderStyle(ws);
        AddColumnTitles(ws, columns);

        var row = 2;
        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                ws.Column(1).Cell(row).Value = reading.Instrument.Identifier;
                ws.Column(2).Cell(row).Value = value.Date.ToString("dd/MM/yyyy");
                ws.Column(3).Cell(row).Value = (int)value.Datum;
                ws.Column(4).Cell(row).Value = value.EastCoordinate;
                ws.Column(5).Cell(row).Value = value.NorthCoordinate;
                ws.Column(6).Cell(row).Value = value.Quota;

                row++;
            }
        }

        ws.Columns().AdjustToContents();

        return Generate(workbook);
    }

    public static ConvertReadingsRequest GetCSVAddWaterLevelIndicator(List<Domain.Entities.Reading> readings)
    {
        var stringBuilder = new StringBuilder();

        stringBuilder.AppendLine("Identificador do instrumento,Data e hora,Cota,Profundidade,Pressão,Seco");

        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                stringBuilder.AppendLine($"{reading.Instrument.Identifier},{value.Date:dd/MM/yyyy},{value.Quota},{value.Depth},{value.Pressure},{value.Dry}");
            }
        }

        return Generate(stringBuilder);
    }

    public static ConvertReadingsRequest GetXLSXAddWaterLevelIndicator(List<Domain.Entities.Reading> readings)
    {
        var workbook = InitializeWorkbook("Leituras de INA");
        var ws = workbook.Worksheets.FirstOrDefault() ?? throw new InvalidOperationException("Worksheet could not be created.");

        var columns = new string[]
        {
            "Identificador do instrumento",
            "Data e hora",
            "Cota",
            "Profundidade",
            "Pressão",
            "Seco"
        };

        SetHeaderStyle(ws);
        AddColumnTitles(ws, columns);

        var row = 2;
        foreach (var reading in readings)
        {
            foreach (var value in reading.Values)
            {
                ws.Column(1).Cell(row).Value = reading.Instrument.Identifier;
                ws.Column(2).Cell(row).Value = value.Date.ToString("dd/MM/yyyy");
                ws.Column(3).Cell(row).Value = value.Quota;
                ws.Column(4).Cell(row).Value = value.Depth;
                ws.Column(5).Cell(row).Value = value.Pressure;
                ws.Column(6).Cell(row).Value = value.Dry.ToString();

                row++;
            }
        }

        ws.Columns().AdjustToContents();

        return Generate(workbook);
    }

    private static void AddColumnTitles(IXLWorksheet worksheet, string[] columnTitles)
    {
        for (int i = 0; i < columnTitles.Length; i++)
        {
            worksheet.Cell(1, i + 1).Value = columnTitles[i];
        }
    }

    private static XLWorkbook InitializeWorkbook(string sheetName)
    {
        var workbook = new XLWorkbook();
        workbook.Worksheets.Add(sheetName);
        return workbook;
    }

    private static void SetHeaderStyle(IXLWorksheet worksheet)
    {
        worksheet.Row(1).Style
            .Font.SetBold(true)
            .Font.SetFontSize(12)
            .Fill.SetBackgroundColor(XLColor.DarkGreen)
            .Font.SetFontColor(XLColor.White);
    }

    private static ConvertReadingsRequest Generate(StringBuilder stringBuilder)
    {
        var file = Encoding.UTF8.GetBytes(stringBuilder.ToString());
        var ms = new MemoryStream(file);

        return new()
        {
            RequestedBy = Guid.NewGuid(),
            RequestedBySuperSupport = true,
            File = new FormFile(ms, 0, file.Length, "name", "reading.csv")
            {
                Headers = new HeaderDictionary(),
                ContentType = "text/csv"
            },
            StructureId = Guid.NewGuid()
        };
    }

    private static ConvertReadingsRequest Generate(XLWorkbook workbook)
    {
        var file = new MemoryStream();
        workbook.SaveAs(file);

        return new()
        {
            RequestedBy = Guid.NewGuid(),
            RequestedBySuperSupport = true,
            File = new FormFile(file, 0, file.Length, "name", "reading.xlsx")
            {
                Headers = new HeaderDictionary(),
                ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            },
            StructureId = Guid.NewGuid()
        };
    }
}
