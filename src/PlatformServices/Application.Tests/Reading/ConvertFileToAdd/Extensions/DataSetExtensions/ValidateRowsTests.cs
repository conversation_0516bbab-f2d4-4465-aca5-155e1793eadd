using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using Application.Reading.ConvertFileToAdd.Extensions;
using Application.Tests.Instrument.Builders;
using Application.Tests.Reading.Builders;
using Application.Tests.Reading.ConvertFileToAdd.Extensions.DataSetExtensions.Builders;
using Bogus;
using Domain.Enums;

namespace Application.Tests.Reading.ConvertFileToAdd.Extensions.
    DataSetExtensions;

[Trait("DataSetExtensions", "ValidateRows")]
public class ValidateRowsTests
{
    [Theory(DisplayName = "When rows are valid, then returns true")]
    [InlineData(InstrumentType.WaterLevelIndicator)]
    [InlineData(InstrumentType.OpenStandpipePiezometer)]
    [InlineData(InstrumentType.ElectricPiezometer)]
    [InlineData(InstrumentType.ConventionalInclinometer)]
    [InlineData(InstrumentType.IPIInclinometer)]
    [InlineData(InstrumentType.SurfaceLandmark)]
    [InlineData(InstrumentType.Prism)]
    [InlineData(InstrumentType.SettlementGauge)]
    [InlineData(InstrumentType.Geophone)]
    [InlineData(InstrumentType.LinimetricRuler)]
    [InlineData(InstrumentType.Pluviometer)]
    [InlineData(InstrumentType.Pluviograph)]
    public void WhenRowsAreValid_ThenReturnsTrue(InstrumentType instrumentType)
    {
        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType),
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];
        originalReadings[1].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        var result = dataSet.ValidateRows(instruments);

        result.Should().BeTrue();
    }

    [Theory(DisplayName =
        "When rows are valid, then file should not contain errors")]
    [InlineData(InstrumentType.WaterLevelIndicator)]
    [InlineData(InstrumentType.OpenStandpipePiezometer)]
    [InlineData(InstrumentType.ElectricPiezometer)]
    [InlineData(InstrumentType.ConventionalInclinometer)]
    [InlineData(InstrumentType.IPIInclinometer)]
    [InlineData(InstrumentType.SurfaceLandmark)]
    [InlineData(InstrumentType.Prism)]
    [InlineData(InstrumentType.SettlementGauge)]
    [InlineData(InstrumentType.Geophone)]
    [InlineData(InstrumentType.LinimetricRuler)]
    [InlineData(InstrumentType.Pluviometer)]
    [InlineData(InstrumentType.Pluviograph)]
    public void WhenRowsAreValid_ThenFileShouldNotContainErrors(
        InstrumentType instrumentType)
    {
        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType),
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];
        originalReadings[1].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        _ = dataSet.ValidateRows(instruments);

        dataSet.Tables[0].Rows.Cast<DataRow>()
            .All(row => string.IsNullOrEmpty(row["Erros"].ToString()))
            .Should()
            .BeTrue();
    }

    [Fact(DisplayName =
        "When rows contain invalid identifiers, then returns false")]
    public void WhenRowsContainInvalidIdentifiers_ThenReturnsFalse()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType),
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];
        originalReadings[1].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        instruments[0].Identifier = "InvalidInstrument";

        var result = dataSet.ValidateRows(instruments);

        result.Should().BeFalse();
    }


    [Fact(DisplayName =
        "When rows contain invalid identifiers, then file should contain errors")]
    public void WhenRowsContainInvalidIdentifiers_ThenFileShouldContainErrors()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType),
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];
        originalReadings[1].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        instruments[0].Identifier = "InvalidInstrument";

        _ = dataSet.ValidateRows(instruments);

        dataSet.Tables[0].Rows.Cast<DataRow>()
            .Any(row => !string.IsNullOrEmpty(row["Erros"].ToString()))
            .Should()
            .BeTrue();
    }

    [Fact(DisplayName =
        "When rows contain empty identifiers, then returns false")]
    public void WhenRowsContainEmptyIdentifiers_ThenReturnsFalse()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType),
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];
        originalReadings[1].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        instruments[0].Identifier = string.Empty;

        var result = dataSet.ValidateRows(instruments);

        result.Should().BeFalse();
    }
    
    [Fact(DisplayName =
        "When rows contain empty identifiers, then file should contain errors")]
    public void WhenRowsContainEmptyIdentifiers_ThenFileShouldContainErrors()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType),
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];
        originalReadings[1].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        instruments[0].Identifier = string.Empty;

        _ = dataSet.ValidateRows(instruments);

        dataSet.Tables[0].Rows.Cast<DataRow>()
            .Any(row => !string.IsNullOrEmpty(row["Erros"].ToString()))
            .Should()
            .BeTrue();
    }

    [Fact(DisplayName =
        "When rows contain null identifiers, then returns false")]
    public void WhenRowsContainNullIdentifiers_ThenReturnsFalse()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType),
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];
        originalReadings[1].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        // Manually set the first column to null to simulate null identifier
        dataSet.Tables[0].Rows[0][0] = DBNull.Value;

        var result = dataSet.ValidateRows(instruments);

        result.Should().BeFalse();
    }

    [Fact(DisplayName =
        "When rows contain null identifiers, then file should contain errors")]
    public void WhenRowsContainNullIdentifiers_ThenFileShouldContainErrors()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType),
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];
        originalReadings[1].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        // Manually set the first column to null to simulate null identifier
        dataSet.Tables[0].Rows[0][0] = DBNull.Value;

        _ = dataSet.ValidateRows(instruments);

        dataSet.Tables[0].Rows.Cast<DataRow>()
            .Any(row => !string.IsNullOrEmpty(row["Erros"].ToString()))
            .Should()
            .BeTrue();
    }

    [Fact(DisplayName =
        "When rows contain whitespace-only identifiers, then returns false")]
    public void WhenRowsContainWhitespaceOnlyIdentifiers_ThenReturnsFalse()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType),
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];
        originalReadings[1].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        // Set identifier to whitespace only
        dataSet.Tables[0].Rows[0][0] = "   ";

        var result = dataSet.ValidateRows(instruments);

        result.Should().BeFalse();
    }

    [Fact(DisplayName =
        "When identifier case differs from instrument, then returns true")]
    public void WhenIdentifierCaseDiffersFromInstrument_ThenReturnsTrue()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        // Change the case of the identifier in the dataset to test case-insensitive matching
        var originalIdentifier = instruments[0].Identifier;
        dataSet.Tables[0].Rows[0][0] = originalIdentifier.ToUpperInvariant();

        var result = dataSet.ValidateRows(instruments);

        result.Should().BeTrue();
    }

    [Fact(DisplayName =
        "When rows contain invalid date format, then returns false")]
    public void WhenRowsContainInvalidDateFormat_ThenReturnsFalse()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType),
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];
        originalReadings[1].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        // Set invalid date format
        var dateColumnIndex = dataSet.Tables[0].Columns.IndexOf("Data e hora");
        dataSet.Tables[0].Rows[0][dateColumnIndex] = "invalid-date";

        var result = dataSet.ValidateRows(instruments);

        result.Should().BeFalse();
    }

    [Fact(DisplayName =
        "When rows contain invalid date format, then file should contain errors")]
    public void WhenRowsContainInvalidDateFormat_ThenFileShouldContainErrors()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType),
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];
        originalReadings[1].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        // Set invalid date format
        var dateColumnIndex = dataSet.Tables[0].Columns.IndexOf("Data e hora");
        dataSet.Tables[0].Rows[0][dateColumnIndex] = "not-a-date";

        _ = dataSet.ValidateRows(instruments);

        var errorRow = dataSet.Tables[0].Rows[0];
        errorRow["Erros"].ToString().Should().Contain("Invalid date format.");
    }

    [Fact(DisplayName =
        "When rows contain empty date, then returns false")]
    public void WhenRowsContainEmptyDate_ThenReturnsFalse()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType),
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];
        originalReadings[1].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        // Set empty date
        var dateColumnIndex = dataSet.Tables[0].Columns.IndexOf("Data e hora");
        dataSet.Tables[0].Rows[0][dateColumnIndex] = string.Empty;

        var result = dataSet.ValidateRows(instruments);

        result.Should().BeFalse();
    }

    [Fact(DisplayName =
        "When rows contain null date, then returns false")]
    public void WhenRowsContainNullDate_ThenReturnsFalse()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType),
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];
        originalReadings[1].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        // Set null date
        var dateColumnIndex = dataSet.Tables[0].Columns.IndexOf("Data e hora");
        dataSet.Tables[0].Rows[0][dateColumnIndex] = DBNull.Value;

        var result = dataSet.ValidateRows(instruments);

        result.Should().BeFalse();
    }

    [Theory(DisplayName =
        "When rows contain valid date formats, then returns true")]
    [InlineData("31/12/2023")]
    [InlineData("01/01/2023 10:30:00")]
    [InlineData("29/02/2024 08:15")]
    public void WhenRowsContainValidDateFormats_ThenReturnsTrue(string validDate)
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        var dateColumnIndex = dataSet.Tables[0].Columns.IndexOf("Data e hora");
        dataSet.Tables[0].Rows[0][dateColumnIndex] = validDate;

        var result = dataSet.ValidateRows(instruments);

        result.Should().BeTrue();
    }

    [Theory(DisplayName =
        "When rows contain invalid date formats, then returns false")]
    [InlineData("32012023103000")] // Only numbers
    [InlineData("test")] // Only text
    [InlineData("2023-01-01 10:30:00")] // ISO format (not pt-BR)
    [InlineData("January 1, 2023 10:30:00")] // English format (not pt-BR)
    public void WhenRowsContainInvalidDateFormats_ThenReturnsFalse(string invalidDate)
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        // Set invalid date format
        var dateColumnIndex = dataSet.Tables[0].Columns.IndexOf("Data e hora");
        dataSet.Tables[0].Rows[0][dateColumnIndex] = invalidDate;

        var result = dataSet.ValidateRows(instruments);

        result.Should().BeFalse();
    }

    [Fact(DisplayName =
        "When row has multiple validation errors, then returns false")]
    public void WhenRowHasMultipleValidationErrors_ThenReturnsFalse()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        // Set both invalid identifier and invalid date
        dataSet.Tables[0].Rows[0][0] = string.Empty; // Invalid identifier
        var dateColumnIndex = dataSet.Tables[0].Columns.IndexOf("Data e hora");
        dataSet.Tables[0].Rows[0][dateColumnIndex] = "invalid-date"; // Invalid date

        var result = dataSet.ValidateRows(instruments);

        result.Should().BeFalse();
    }

    [Fact(DisplayName =
        "When row has multiple validation errors, then file should contain last error")]
    public void WhenRowHasMultipleValidationErrors_ThenFileShouldContainLastError()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        // Set both invalid identifier and invalid date
        dataSet.Tables[0].Rows[0][0] = string.Empty; // Invalid identifier
        var dateColumnIndex = dataSet.Tables[0].Columns.IndexOf("Data e hora");
        dataSet.Tables[0].Rows[0][dateColumnIndex] = "invalid-date"; // Invalid date

        _ = dataSet.ValidateRows(instruments);

        // Since AddErrors overwrites the error message, only the identifier error should remain
        // because the identifier validation returns early when identifier is empty
        var errorMessage = dataSet.Tables[0].Rows[0]["Erros"].ToString();
        errorMessage.Should().Be("The readings must contain the instrument identifier.");
    }

    [Fact(DisplayName =
        "When empty instruments list is provided, then returns false")]
    public void WhenEmptyInstrumentsListIsProvided_ThenReturnsFalse()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>();

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType)
        };

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        var result = dataSet.ValidateRows(instruments);

        result.Should().BeFalse();
    }

    [Fact(DisplayName =
        "When dataset has no rows, then returns true")]
    public void WhenDatasetHasNoRows_ThenReturnsTrue()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>();

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        var result = dataSet.ValidateRows(instruments);

        result.Should().BeTrue();
    }

    [Fact(DisplayName =
        "When identifier contains special characters, then validates correctly")]
    public void WhenIdentifierContainsSpecialCharacters_ThenValidatesCorrectly()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        // Set instrument identifier with special characters
        var specialIdentifier = "I-001_TEST@2023";
        instruments[0].Identifier = specialIdentifier;

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        // The DataSetBuilder will use the instrument's identifier, so we need to verify it matches
        dataSet.Tables[0].Rows[0][0] = specialIdentifier;

        var result = dataSet.ValidateRows(instruments);

        result.Should().BeTrue();
    }

    [Fact(DisplayName =
        "When identifier not found error occurs, then specific error message is set")]
    public void WhenIdentifierNotFoundErrorOccurs_ThenSpecificErrorMessageIsSet()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        var invalidIdentifier = "INVALID-INSTRUMENT-ID";
        dataSet.Tables[0].Rows[0][0] = invalidIdentifier;

        _ = dataSet.ValidateRows(instruments);

        var errorMessage = dataSet.Tables[0].Rows[0]["Erros"].ToString();
        errorMessage.Should().Be($"The instrument with identifier {invalidIdentifier} was not found in the informed structure.");
    }

    [Fact(DisplayName =
        "When empty identifier error occurs, then specific error message is set")]
    public void WhenEmptyIdentifierErrorOccurs_ThenSpecificErrorMessageIsSet()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        dataSet.Tables[0].Rows[0][0] = string.Empty;

        _ = dataSet.ValidateRows(instruments);

        var errorMessage = dataSet.Tables[0].Rows[0]["Erros"].ToString();
        errorMessage.Should().Be("The readings must contain the instrument identifier.");
    }

    [Fact(DisplayName =
        "When invalid date error occurs, then specific error message is set")]
    public void WhenInvalidDateErrorOccurs_ThenSpecificErrorMessageIsSet()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        var dateColumnIndex = dataSet.Tables[0].Columns.IndexOf("Data e hora");
        dataSet.Tables[0].Rows[0][dateColumnIndex] = "invalid-date";

        _ = dataSet.ValidateRows(instruments);

        var errorMessage = dataSet.Tables[0].Rows[0]["Erros"].ToString();
        errorMessage.Should().Be("Invalid date format.");
    }

    [Fact(DisplayName =
        "When multiple instruments with same identifier exist, then uses first match")]
    public void WhenMultipleInstrumentsWithSameIdentifierExist_ThenUsesFirstMatch()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
            InstrumentBuilder.Create(instrumentType)
        };

        // Set same identifier for both instruments
        var sharedIdentifier = "SHARED-ID";
        instruments[0].Identifier = sharedIdentifier;
        instruments[1].Identifier = sharedIdentifier;

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        // Manually set the identifier in the dataset to match the shared identifier
        dataSet.Tables[0].Rows[0][0] = sharedIdentifier;

        var result = dataSet.ValidateRows(instruments);

        result.Should().BeTrue();
    }

    [Fact(DisplayName =
        "When AcceptChanges is called, then dataset changes are committed")]
    public void WhenAcceptChangesIsCalled_ThenDatasetChangesAreCommitted()
    {
        const InstrumentType instrumentType =
            InstrumentType.WaterLevelIndicator;

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.Create(instrumentType),
        };

        var originalReadings = new List<Domain.Entities.Reading>
        {
            ReadingBuilder.CreateReading(instrumentType)
        };

        originalReadings[0].Instrument = instruments[0];

        var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

        // Verify that AcceptChanges is called by checking the row state before and after
        var initialRowState = dataSet.Tables[0].Rows[0].RowState;

        _ = dataSet.ValidateRows(instruments);

        // After AcceptChanges, row state should be Unchanged
        var finalRowState = dataSet.Tables[0].Rows[0].RowState;
        finalRowState.Should().Be(DataRowState.Unchanged);
    }

    [Fact(DisplayName =
        "When validation passes for all instrument types, then returns true")]
    public void WhenValidationPassesForAllInstrumentTypes_ThenReturnsTrue()
    {
        var faker = new Faker();
        var instrumentTypes = Enum.GetValues<InstrumentType>();

        foreach (var instrumentType in instrumentTypes)
        {
            var instruments = new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.Create(instrumentType),
            };

            var originalReadings = new List<Domain.Entities.Reading>
            {
                ReadingBuilder.CreateReading(instrumentType)
            };

            originalReadings[0].Instrument = instruments[0];

            var dataSet = DataSetBuilder.Create(instrumentType, originalReadings);

            var result = dataSet.ValidateRows(instruments);

            result.Should().BeTrue($"validation should pass for instrument type {instrumentType}");
        }
    }
}