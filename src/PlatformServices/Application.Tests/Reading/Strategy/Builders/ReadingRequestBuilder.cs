using Model.Reading._Shared;

namespace Application.Tests.Reading.Strategy.Builders
{
    public static class ReadingRequestBuilder
    {
        public static ReadingRequest CreateValid(
            bool isReferencial,
            bool? isAutomated) =>
            new()
            {
                DbInstrument = new Domain.Entities.Instrument()
                {
                    Identifier = "Test"
                },
                IsReferential = isReferencial,
                IsAutomated = isAutomated,
                Values = new()
                {
                    new()
                }
            };
    }
}
