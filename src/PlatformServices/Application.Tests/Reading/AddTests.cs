using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Application.Core;
using Application.Reading.Add;
using Application.Tests.Instrument.Builders;
using Application.Tests.Reading.Builders;
using Database.Repositories.Instrument;
using Database.Repositories.Reading;
using Database.Repositories.User;
using Model.Reading.Add.DTOs;

namespace Application.Tests.Reading;

[Trait("AddReadingUseCase", "Execute")]
public class AddTests
{
    private readonly Mock<IReadingRepository> _readingRepository = new();
    private readonly Mock<IInstrumentRepository> _instrumentRepository = new();
    private readonly Mock<IUserRepository> _userRepository = new();
    private readonly AddReadingUseCase _useCase;

    public AddTests()
    {
        _useCase = new AddReadingUseCase(
            _readingRepository.Object,
            _instrumentRepository.Object,
            _userRepository.Object);
    }

    [Fact(DisplayName = "WhenQuotaOfWaterLevelIndicatorReadingIsInvalid_ReturnsBadRequest")]
    public async Task WhenQuotaWaterLevelIndicatorReadingIsInvalid_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreateWaterLevelIndicatorReading()
            .WithQuota(115);

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.CreateWaterLevelIndicator().WithId(request.Readings[0].Instrument.Id)
        };
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(instruments);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenWaterLevelIndicatorReadingIsDryAndThePropertiesAreFilled_ReturnsBadRequest")]
    public async Task WhenWaterLevelIndicatorReadingIsDryAndThePropertiesAreFilled_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreateWaterLevelIndicatorReading()
            .WithDry(true);

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.CreateWaterLevelIndicator().WithId(request.Readings[0].Instrument.Id)
        };
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(instruments);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenPressureOfWaterLevelIndicatorReadingIsInvalid_ReturnsBadRequest")]
    public async Task WhenPressureOfWaterLevelIndicatorReadingIsInvalid_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreateWaterLevelIndicatorReading()
            .WithPressure(12);

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.CreateWaterLevelIndicator().WithId(request.Readings[0].Instrument.Id)
        };
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(instruments);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenPressureOfWaterLevelIndicatorReadingIsNegative_ReturnsBadRequest")]
    public async Task WhenPressureOfWaterLevelIndicatorReadingIsNegative_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreateWaterLevelIndicatorReading()
            .WithPressure(-12);

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.CreateWaterLevelIndicator().WithId(request.Readings[0].Instrument.Id)
        };
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(instruments);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenWaterLevelIndicatorReadingAlreadyExists_ReturnsBadRequest")]
    public async Task WhenWaterLevelIndicatorReadingAlreadyExists_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreateWaterLevelIndicatorReading()
            .WithPressure(9.81m);

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.CreateWaterLevelIndicator().WithId(request.Readings[0].Instrument.Id)
        };
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(instruments);

        _readingRepository
            .Setup(x => x.GetExistingReadings(It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(new []{new ExistingReadingsResult(Guid.NewGuid(), DateTime.Now, true)});

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenWaterLevelIndicatorReadingAlreadyExists_ReturnsErrorMessage")]
    public async Task WhenWaterLevelIndicatorReadingAlreadyExists_ReturnsErrorMessage()
    {
        var request = AddReadingsRequestBuilder
            .CreateWaterLevelIndicatorReading()
            .WithPressure(9.81m);

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.CreateWaterLevelIndicator().WithId(request.Readings[0].Instrument.Id)
        };
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(instruments);

        _readingRepository
            .Setup(x => x.GetExistingReadings(It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(new []{new ExistingReadingsResult(Guid.NewGuid(), DateTime.Now, true)});

        var response = await _useCase
            .Execute(request);

        response.Errors.Single().Message.Should()
            .MatchRegex($@"^A reading for instrument .* at .* already exists.$");
    }

    [Fact(DisplayName = "WhenWaterLevelIndicatorReadingIsValid_ReturnsOk")]
    public async Task WhenWaterLevelIndicatorReadingIsValid_ReturnsOk()
    {
        var request = AddReadingsRequestBuilder
            .CreateWaterLevelIndicatorReading()
            .WithPressure(9.81m);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreateWaterLevelIndicator()
                    .WithId(request.Readings[0].Instrument.Id)
            });

        _readingRepository
            .Setup(x => x.GetExistingReadings(It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(new []{new ExistingReadingsResult(Guid.Empty, DateTime.Now, false)});

        _readingRepository
            .Setup(x => x.AddAsync(It.IsAny<List<Domain.Entities.Reading>>()))
            .Returns(Task.CompletedTask);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "WhenQuotaOfOpenStandpipePiezometerReadingIsInvalid_ReturnsBadRequest")]
    public async Task WhenQuotaOpenStandpipePiezometerReadingIsInvalid_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreateOpenStandpipePiezometerReading()
            .WithQuota(115);

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.CreateOpenStandpipePiezometer().WithId(request.Readings[0].Instrument.Id)
        };
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(instruments);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenOpenStandpipePiezometerReadingIsDryAndThePropertiesAreFilled_ReturnsBadRequest")]
    public async Task WhenOpenStandpipePiezometerReadingIsDryAndThePropertiesAreFilled_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreateOpenStandpipePiezometerReading()
            .WithDry(true);

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.CreateOpenStandpipePiezometer().WithId(request.Readings[0].Instrument.Id)
        };
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(instruments);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenPressureOfOpenStandpipePiezometerReadingIsInvalid_ReturnsBadRequest")]
    public async Task WhenPressureOfOpenStandpipePiezometerReadingIsInvalid_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreateOpenStandpipePiezometerReading()
            .WithPressure(12);

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.CreateOpenStandpipePiezometer().WithId(request.Readings[0].Instrument.Id)
        };
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(instruments);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenPressureOfOpenStandpipePiezometerReadingIsNegative_ReturnsBadRequest")]
    public async Task WhenPressureOfOpenStandpipePiezometerReadingIsNegative_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreateWaterLevelIndicatorReading()
            .WithPressure(-12);

        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.CreateOpenStandpipePiezometer().WithId(request.Readings[0].Instrument.Id)
        };
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(instruments);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenOpenStandpipePiezometerReadingAlreadyExists_ReturnsBadRequest")]
    public async Task WhenOpenStandpipePiezometerReadingAlreadyExists_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreateOpenStandpipePiezometerReading()
            .WithPressure(9.81m);
        
        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.CreateOpenStandpipePiezometer().WithId(request.Readings[0].Instrument.Id)
        };
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(instruments);

        _readingRepository
            .Setup(x => x.GetExistingReadings(It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(new []{new ExistingReadingsResult(Guid.NewGuid(), DateTime.Now, true)});

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenOpenStandpipePiezometerReadingAlreadyExists_ReturnsErrorMessage")]
    public async Task WhenOpenStandpipePiezometerReadingAlreadyExists_ReturnsErrorMessage()
    {
        var request = AddReadingsRequestBuilder
            .CreateOpenStandpipePiezometerReading()
            .WithPressure(9.81m);
        
        var instruments = new List<Domain.Entities.Instrument>
        {
            InstrumentBuilder.CreateOpenStandpipePiezometer().WithId(request.Readings[0].Instrument.Id)
        };
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(instruments);

        _readingRepository
            .Setup(x => x.GetExistingReadings(It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(new []{new ExistingReadingsResult(Guid.NewGuid(), DateTime.Now, true)});

        var response = await _useCase
            .Execute(request);

        response.Errors.Single().Message.Should()
            .MatchRegex($@"^A reading for instrument .* at .* already exists.$");
    }

    [Fact(DisplayName = "WhenOpenStandpipePiezometerReadingIsValid_ReturnsOk")]
    public async Task WhenOpenStandpipePiezometerReadingIsValid_ReturnsOk()
    {
        var request = AddReadingsRequestBuilder
            .CreateOpenStandpipePiezometerReading()
            .WithPressure(9.81m);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreateOpenStandpipePiezometer()
                    .WithId(request.Readings[0].Instrument.Id)
            });

        _readingRepository
            .Setup(x => x.GetExistingReadings(It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(new []{new ExistingReadingsResult(Guid.NewGuid(), DateTime.Now, false)});

        _readingRepository
            .Setup(x => x.AddAsync(It.IsAny<List<Domain.Entities.Reading>>()))
            .Returns(Task.CompletedTask);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "WhenQuotaOfElectricPiezometerReadingIsInvalid_ReturnsBadRequest")]
    public async Task WhenQuotaElectricPiezometerReadingIsInvalid_ReturnsBadRequest()
    {
        var electricPiezometer = InstrumentBuilder.CreateElectricPiezometer();

        var request = AddReadingsRequestBuilder
            .CreateElectricPiezometerReading()
            .WithQuota(105)
            .WithMeasurements(electricPiezometer.Measurements);
        
        electricPiezometer = electricPiezometer.WithId(
            request.Readings[0].Instrument.Id);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>{electricPiezometer});

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenElectricPiezometerReadingIsDryAndThePropertiesAreFilled_ReturnsBadRequest")]
    public async Task WhenElectricPiezometerReadingIsDryAndThePropertiesAreFilled_ReturnsBadRequest()
    {
        var electricPiezometer = InstrumentBuilder.CreateElectricPiezometer();

        var request = AddReadingsRequestBuilder
            .CreateElectricPiezometerReading()
            .WithDry(true)
            .WithMeasurements(electricPiezometer.Measurements);
        
        electricPiezometer = electricPiezometer.WithId(
            request.Readings[0].Instrument.Id);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>{electricPiezometer});

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenPressureOfElectricPiezometerReadingIsNegative_ReturnsBadRequest")]
    public async Task WhenPressureOfElectricPiezometerReadingIsNegative_ReturnsBadRequest()
    {
        var electricPiezometer = InstrumentBuilder.CreateElectricPiezometer();

        var request = AddReadingsRequestBuilder
            .CreateElectricPiezometerReading()
            .WithPressure(-9.81m)
            .WithMeasurements(electricPiezometer.Measurements);
        
        electricPiezometer = electricPiezometer.WithId(
            request.Readings[0].Instrument.Id);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>{electricPiezometer});

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenElectricPiezometerReadingAlreadyExists_ReturnsBadRequest")]
    public async Task WhenElectricPiezometerReadingAlreadyExists_ReturnsBadRequest()
    {
        var electricPiezometer = InstrumentBuilder.CreateElectricPiezometer();

        var request = AddReadingsRequestBuilder
            .CreateElectricPiezometerReading()
            .WithPressure(null)
            .WithMeasurements(electricPiezometer.Measurements);

        electricPiezometer = electricPiezometer.WithId(
            request.Readings[0].Instrument.Id);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>{electricPiezometer});

        _readingRepository
            .Setup(x => x.GetExistingReadings(
                It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(
                new[]
                {
                    new ExistingReadingsResult(
                        Guid.NewGuid(),
                        DateTime.Now,
                        true)
                });

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenElectricPiezometerReadingAlreadyExists_ReturnsErrorMessage")]
    public async Task WhenElectricPiezometerReadingAlreadyExists_ReturnsErrorMessage()
    {
        var electricPiezometer = InstrumentBuilder.CreateElectricPiezometer();

        var request = AddReadingsRequestBuilder
            .CreateElectricPiezometerReading()
            .WithPressure(null)
            .WithMeasurements(electricPiezometer.Measurements);

        electricPiezometer = electricPiezometer.WithId(
            request.Readings[0].Instrument.Id);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>{electricPiezometer});

        _readingRepository
            .Setup(x => x.GetExistingReadings(
                It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(
                new[]
                {
                    new ExistingReadingsResult(
                        Guid.NewGuid(),
                        DateTime.Now,
                        true)
                });

        var response = await _useCase
            .Execute(request);

        response.Errors.Single().Message.Should()
            .MatchRegex(@"^A reading for instrument .* at .* already exists.*$");
    }

    [Fact(DisplayName = "WhenElectricPiezometerReadingIsValid_ReturnsOk")]
    public async Task WhenElectricPiezometerReadingIsValid_ReturnsOk()
    {
        var electricPiezometer = InstrumentBuilder.CreateElectricPiezometer();

        var request = AddReadingsRequestBuilder
            .CreateElectricPiezometerReading()
            .WithQuota(105)
            .WithPressure(null)
            .WithMeasurements(electricPiezometer.Measurements);
        
        electricPiezometer = electricPiezometer.WithId(
            request.Readings[0].Instrument.Id);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>{electricPiezometer});

        _readingRepository
            .Setup(x => x.GetExistingReadings(
                It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(
                new[]
                {
                    new ExistingReadingsResult(
                        Guid.NewGuid(),
                        DateTime.Now,
                        false)
                });

        _readingRepository
            .Setup(x => x.AddAsync(It.IsAny<List<Domain.Entities.Reading>>()))
            .Returns(Task.CompletedTask);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "WhenTheCountOfValuesOfConventionalInclinometerReadingIsDifferentFromNumberOfMeasurementPoints_ReturnsBadRequest")]
    public async Task WhenTheCountOfValuesOfConventionalInclinometerReadingIsDifferentFromNumberOfMeasurementPoints_ReturnsBadRequest()
    {
        var conventionalInclinometer = InstrumentBuilder.CreateConventionalInclinometer();

        var request = AddReadingsRequestBuilder
            .CreateConventionalInclinometerReading()
            .WithMeasurements(conventionalInclinometer.Measurements)
            .AddValue(new() { Measurement = new() { Id = Guid.NewGuid() } });
        
        conventionalInclinometer = conventionalInclinometer.WithId(request.Readings[0].Instrument.Id);

        foreach (var reading in request.Readings)
        {
            reading.ReferenceReading.Instrument = conventionalInclinometer;
        }

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>{conventionalInclinometer});

        _readingRepository
            .Setup(x => x.GetReferentialsAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Reading>
            {
                new ()
                {
                    Instrument = conventionalInclinometer,
                    Values = new List<Domain.Entities.ReadingValue>()
                }
            });

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);

        response.Errors.Single().Message.Should()
            .MatchRegex(@"^Instrument .* does not have reference readings for the following measurement points: .*. In that case, this reading should be the reference.$");
    }

    [Fact(DisplayName = "WhenDepthOfConventionalInclinometerReadingIsInvalid_ReturnsBadRequest")]
    public async Task WhenDepthOfConventionalInclinometerReadingIsInvalid_ReturnsBadRequest()
    {
        var conventionalInclinometer = InstrumentBuilder.CreateConventionalInclinometer();

        var request = AddReadingsRequestBuilder
            .CreateConventionalInclinometerReading()
            .WithDepth(2)
            .WithMeasurements(conventionalInclinometer.Measurements);
        
        conventionalInclinometer = conventionalInclinometer.WithId(request.Readings[0].Instrument.Id);

        foreach (var reading in request.Readings)
        {
            reading.ReferenceReading.Instrument = conventionalInclinometer;
        }
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>{conventionalInclinometer});

        _readingRepository
            .Setup(x => x.GetReferentialsAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Reading>{request.Readings[0].ReferenceReading});

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);

        response.Errors.Single().Message.Should()
            .Be("The depth value must be equal to the measurement depth.");
    }

    [Fact(DisplayName = "WhenElevationOfConventionalInclinometerReadingIsInvalid_ReturnsBadRequest")]
    public async Task WhenElevationOfConventionalInclinometerReadingIsInvalid_ReturnsBadRequest()
    {
        var conventionalInclinometer = InstrumentBuilder.CreateConventionalInclinometer();

        var request = AddReadingsRequestBuilder
            .CreateConventionalInclinometerReading()
            .WithQuota(0)
            .WithMeasurements(conventionalInclinometer.Measurements);
        
        conventionalInclinometer = conventionalInclinometer.WithId(request.Readings[0].Instrument.Id);
        
        foreach (var reading in request.Readings)
        {
            reading.ReferenceReading.Instrument = conventionalInclinometer;
        }
        
        _readingRepository
            .Setup(x => x.GetReferentialsAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Reading>{request.Readings[0].ReferenceReading});

         _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>{conventionalInclinometer});

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);

        response.Errors.Single().Message.Should()
            .Be("The quota value must be equal to the measurement quota.");
    }

    [Fact(DisplayName = "WhenConventionalInclinometerReadingIsValid_ReturnsOk")]
    public async Task WhenConventionalInclinometerReadingIsValid_ReturnsOk()
    {
        var conventionalInclinometer = InstrumentBuilder.CreateConventionalInclinometer();

        var request = AddReadingsRequestBuilder
            .CreateConventionalInclinometerReading()
            .WithMeasurements(conventionalInclinometer.Measurements);
        
        conventionalInclinometer = conventionalInclinometer.WithId(request.Readings[0].Instrument.Id);

        foreach (var reading in request.Readings)
        {
            reading.ReferenceReading.Instrument = conventionalInclinometer;
        }
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>{conventionalInclinometer});

        _readingRepository
            .Setup(x => x.GetReferentialsAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Reading>{request.Readings[0].ReferenceReading});


        _readingRepository
            .Setup(x => x.GetExistingReadings(
                It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(
                new[]
                {
                    new ExistingReadingsResult(
                        Guid.NewGuid(),
                        DateTime.Now,
                        false)
                });

        _readingRepository
            .Setup(x => x.AddAsync(It.IsAny<List<Domain.Entities.Reading>>()))
            .Returns(Task.CompletedTask);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "WhenTheCountOfValuesOfIPIInclinometerReadingIsDifferentFromNumberOfMeasurementPoints_ReturnsBadRequest")]
    public async Task WhenTheCountOfValuesOfIPIInclinometerReadingIsDifferentFromNumberOfMeasurementPoints_ReturnsBadRequest()
    {
        var ipiInclinometer = InstrumentBuilder.CreateIPIInclinometer();

        var request = AddReadingsRequestBuilder
            .CreateIPIInclinometerReading()
            .AddValue(new());
        
        ipiInclinometer = ipiInclinometer.WithId(request.Readings[0].Instrument.Id);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>{ipiInclinometer});

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);

        response.Errors.Single().Message.Should()
            .Be("The number of reading values must be equal to the number of active measurement points of the instrument.");
    }

    [Fact(DisplayName = "WhenDepthOfIPIInclinometerReadingIsInvalid_ReturnsBadRequest")]
    public async Task WhenDepthOfIPIInclinometerReadingIsInvalid_ReturnsBadRequest()
    {
        var ipiInclinometer = InstrumentBuilder.CreateIPIInclinometer();

        var request = AddReadingsRequestBuilder
            .CreateIPIInclinometerReading()
            .WithDepth(2)
            .WithMeasurements(ipiInclinometer.Measurements);
        
        ipiInclinometer = ipiInclinometer.WithId(request.Readings[0].Instrument.Id);

        _readingRepository
            .Setup(x => x.GetReferentialAsync(It.IsAny<Guid>()))
            .ReturnsAsync(request.Readings[0].ReferenceReading);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>{ipiInclinometer});

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);

        response.Errors.Single().Message.Should()
            .Be("The depth value must be equal to the measurement depth.");
    }

    [Fact(DisplayName = "WhenElevationOfIPIInclinometerReadingIsInvalid_ReturnsBadRequest")]
    public async Task WhenElevationOfIPIInclinometerReadingIsInvalid_ReturnsBadRequest()
    {
        var ipiInclinometer = InstrumentBuilder.CreateIPIInclinometer();

        var request = AddReadingsRequestBuilder
            .CreateIPIInclinometerReading()
            .WithQuota(0)
            .WithMeasurements(ipiInclinometer.Measurements);
        
        ipiInclinometer = ipiInclinometer.WithId(request.Readings[0].Instrument.Id);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>{ipiInclinometer});

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);

        response.Errors.Single().Message.Should()
            .Be("The quota value must be equal to the measurement quota.");
    }

    [Fact(DisplayName = "WhenIPIInclinometerReadingAlreadyExists_ReturnsBadRequest")]
    public async Task WhenIPIInclinometerReadingAlreadyExists_ReturnsBadRequest()
    {
        var ipiInclinometer = InstrumentBuilder.CreateIPIInclinometer();

        var request = AddReadingsRequestBuilder
            .CreateIPIInclinometerReading()
            .WithMeasurements(ipiInclinometer.Measurements);
        
        ipiInclinometer = ipiInclinometer.WithId(request.Readings[0].Instrument.Id);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>{ipiInclinometer});

        _readingRepository
            .Setup(x => x.GetExistingReadings(
                It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(
                new[]
                {
                    new ExistingReadingsResult(
                        Guid.NewGuid(),
                        DateTime.Now,
                        true)
                });

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenIPIInclinometerReadingAlreadyExists_ReturnsErrorMessage")]
    public async Task WhenIPIInclinometerReadingAlreadyExists_ReturnsErrorMessage()
    {
        var ipiInclinometer = InstrumentBuilder.CreateIPIInclinometer();

        var request = AddReadingsRequestBuilder
            .CreateIPIInclinometerReading()
            .WithMeasurements(ipiInclinometer.Measurements);
        
        ipiInclinometer = ipiInclinometer.WithId(request.Readings[0].Instrument.Id);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>{ipiInclinometer});

        _readingRepository
            .Setup(x => x.GetExistingReadings(
                It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(
                new[]
                {
                    new ExistingReadingsResult(
                        Guid.NewGuid(),
                        DateTime.Now,
                        true)
                });

        var response = await _useCase
            .Execute(request);

        response.Errors.Single().Message.Should()
            .MatchRegex(@"^A reading for instrument .* at .* already exists.*$");
    }

    [Fact(DisplayName = "WhenIPIInclinometerReadingIsValid_ReturnsOk")]
    public async Task WhenIPIInclinometerReadingIsValid_ReturnsOk()
    {
        var ipiInclinometer = InstrumentBuilder.CreateIPIInclinometer();

        var request = AddReadingsRequestBuilder
            .CreateIPIInclinometerReading()
            .WithMeasurements(ipiInclinometer.Measurements);
        
        ipiInclinometer = ipiInclinometer.WithId(request.Readings[0].Instrument.Id);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>{ipiInclinometer});

        
        _readingRepository
            .Setup(x => x.GetExistingReadings(
                It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(
                new[]
                {
                    new ExistingReadingsResult(
                        Guid.NewGuid(),
                        DateTime.Now,
                        false)
                });

        _readingRepository
            .Setup(x => x.AddAsync(It.IsAny<List<Domain.Entities.Reading>>()))
            .Returns(Task.CompletedTask);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "WhenQuotaOfSurfaceLandmarkReadingIsInvalid_ReturnsBadRequest")]
    public async Task WhenQuotaSurfaceLandmarkReadingIsInvalid_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreateSurfaceLandmarkReading()
            .WithQuota(null);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreateSurfaceLandmark()
                    .WithId(request.Readings[0].Instrument.Id)
            });
        
        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenSurfaceLandmarkReadingAlreadyExists_ReturnsBadRequest")]
    public async Task WhenSurfaceLandmarkReadingAlreadyExists_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreateSurfaceLandmarkReading()
            .WithPressure(9.81m);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreateSurfaceLandmark()
                    .WithId(request.Readings[0].Instrument.Id)
                
            });

        _readingRepository
            .Setup(x => x.GetExistingReadings(
                It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(
                new[]
                {
                    new ExistingReadingsResult(
                        Guid.NewGuid(),
                        DateTime.Now,
                        true)
                });

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenSurfaceLandmarkReadingIsValid_ReturnsOk")]
    public async Task WhenSurfaceLandmarkReadingIsValid_ReturnsOk()
    {
        var request = AddReadingsRequestBuilder
            .CreateSurfaceLandmarkReading();

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreateSurfaceLandmark()
                    .WithId(request.Readings[0].Instrument.Id)
            });

        
        _readingRepository
            .Setup(x => x.GetExistingReadings(
                It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(
                new[]
                {
                    new ExistingReadingsResult(
                        Guid.NewGuid(),
                        DateTime.Now,
                        false)
                });

        _readingRepository
            .Setup(x => x.AddAsync(It.IsAny<List<Domain.Entities.Reading>>()))
            .Returns(Task.CompletedTask);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "WhenQuotaOfPrismReadingIsInvalid_ReturnsBadRequest")]
    public async Task WhenQuotaPrismReadingIsInvalid_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreatePrismReading()
            .WithQuota(null);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreatePrism()
                    .WithId(request.Readings[0].Instrument.Id)
            });

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenPrismReadingAlreadyExists_ReturnsBadRequest")]
    public async Task WhenPrismReadingAlreadyExists_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreatePrismReading()
            .WithPressure(9.81m);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreatePrism()
                    .WithId(request.Readings[0].Instrument.Id)
            });

        _readingRepository
            .Setup(x => x.GetExistingReadings(
                It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(
                new[]
                {
                    new ExistingReadingsResult(
                        Guid.NewGuid(),
                        DateTime.Now,
                        true)
                });

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenPrismReadingIsValid_ReturnsOk")]
    public async Task WhenPrismReadingIsValid_ReturnsOk()
    {
        var request = AddReadingsRequestBuilder
            .CreatePrismReading();

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreatePrism()
                    .WithId(request.Readings[0].Instrument.Id)
            });
       
        _readingRepository
            .Setup(x => x.GetExistingReadings(
                It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(
                new[]
                {
                    new ExistingReadingsResult(
                        Guid.NewGuid(),
                        DateTime.Now,
                        false)
                });

        _readingRepository
            .Setup(x => x.AddAsync(It.IsAny<List<Domain.Entities.Reading>>()))
            .Returns(Task.CompletedTask);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "WhenNatureOfGeophoneReadingIsInvalid_ReturnsBadRequest")]
    public async Task WhenNatureGeophoneReadingIsInvalid_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreateGeophoneReading()
            .WithNature(null);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreateGeophone()
                    .WithId(request.Readings[0].Instrument.Id)
            });

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenCoordinateOfGeophoneReadingIsInvalid_ReturnsBadRequest")]
    public async Task WhenCoordinateOfGeophoneReadingIsInvalid_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreateGeophoneReading()
            .WithCoordinate(-1, -9);

         _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreateGeophone()
                    .WithId(request.Readings[0].Instrument.Id)
            });

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenGeophoneReadingAlreadyExists_ReturnsBadRequest")]
    public async Task WhenGeophoneReadingAlreadyExists_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreateGeophoneReading();

         _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreateGeophone()
                    .WithId(request.Readings[0].Instrument.Id)
            });

         _readingRepository
             .Setup(x => x.GetExistingReadings(
                 It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
             .ReturnsAsync(
                 new[]
                 {
                     new ExistingReadingsResult(
                         Guid.NewGuid(),
                         DateTime.Now,
                         true)
                 });
        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenGeophoneReadingIsValid_ReturnsOk")]
    public async Task WhenGeophoneReadingIsValid_ReturnsOk()
    {
        var request = AddReadingsRequestBuilder
            .CreateGeophoneReading();

         _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreateGeophone()
                    .WithId(request.Readings[0].Instrument.Id)
            });

         _readingRepository
             .Setup(x => x.GetExistingReadings(
                 It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
             .ReturnsAsync(
                 new[]
                 {
                     new ExistingReadingsResult(
                         Guid.NewGuid(),
                         DateTime.Now,
                         false)
                 });

        _readingRepository
            .Setup(x => x.AddAsync(It.IsAny<List<Domain.Entities.Reading>>()))
            .Returns(Task.CompletedTask);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "WhenPluviometerReadingIsInvalid_ReturnsBadRequest")]
    public async Task WhenPluviometerReadingIsInvalid_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreatePluviometerReading()
            .WithAbsoluteSettlement(2);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreatePluviometer()
                    .WithId(request.Readings[0].Instrument.Id)
            });

        _readingRepository
            .Setup(x => x.CheckIfExistsReadingWithin24Hours(It.IsAny<Guid>(), It.IsAny<DateTime>()))
            .ReturnsAsync(false);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenPluviometerReadingIsValid_ReturnsOk")]
    public async Task WhenPluviometerReadingIsValid_ReturnsOk()
    {
        var request = AddReadingsRequestBuilder
            .CreatePluviometerReading();

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreatePluviometer()
                    .WithId(request.Readings[0].Instrument.Id)
            });

        _readingRepository
            .Setup(x => x.AddAsync(It.IsAny<List<Domain.Entities.Reading>>()))
            .Returns(Task.CompletedTask);

        _readingRepository
            .Setup(x => x.CheckIfExistsReadingWithin24Hours(It.IsAny<Guid>(), It.IsAny<DateTime>()))
            .ReturnsAsync(false);

        _readingRepository
            .Setup(x => x.GetExistingReadings(
                It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(
                new[]
                {
                    new ExistingReadingsResult(
                        Guid.NewGuid(),
                        DateTime.Now,
                        false)
                });

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }
    
    [Fact(DisplayName = "WhenPluviographReadingIsInvalid_ReturnsBadRequest")]
    public async Task WhenPluviographReadingIsInvalid_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreatePluviographReading()
            .WithDepth(2);
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreatePluviograph()
                    .WithId(request.Readings[0].Instrument.Id)
            });

        _readingRepository
            .Setup(x => x.CheckIfExistsReadingWithin24Hours(It.IsAny<Guid>(), It.IsAny<DateTime>()))
            .ReturnsAsync(false);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenPluviographReadingIsValid_ReturnsOk")]
    public async Task WhenPluviographReadingIsValid_ReturnsOk()
    {
        var request = AddReadingsRequestBuilder
            .CreatePluviographReading();

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreatePluviograph()
                    .WithId(request.Readings[0].Instrument.Id)
            });

        _readingRepository
            .Setup(x => x.AddAsync(It.IsAny<List<Domain.Entities.Reading>>()))
            .Returns(Task.CompletedTask);

        _readingRepository
            .Setup(x => x.CheckIfExistsReadingWithin24Hours(It.IsAny<Guid>(), It.IsAny<DateTime>()))
            .ReturnsAsync(false);

        _readingRepository
            .Setup(x => x.GetExistingReadings(
                It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(
                new[]
                {
                    new ExistingReadingsResult(
                        Guid.NewGuid(),
                        DateTime.Now,
                        false)
                });

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }
    
    [Fact(DisplayName = "WhenQuotaOfLinimetricRulerReadingIsInvalid_ReturnsBadRequest")]
    public async Task WhenQuotaOfLinimetricRulerReadingIsInvalid_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreateLinimetricRulerReading()
            .WithQuota(null);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreateLinimetricRuler()
                    .WithId(request.Readings[0].Instrument.Id)
            });

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenLinimetricRulerReadingAlreadyExists_ReturnsBadRequest")]
    public async Task WhenLinimetricRulerReadingAlreadyExists_ReturnsBadRequest()
    {
        var request = AddReadingsRequestBuilder
            .CreateLinimetricRulerReading();

       _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreateLinimetricRuler()
                    .WithId(request.Readings[0].Instrument.Id)
            });

       _readingRepository
           .Setup(x => x.GetExistingReadings(
               It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
           .ReturnsAsync(
               new[]
               {
                   new ExistingReadingsResult(
                       Guid.NewGuid(),
                       DateTime.Now,
                       true)
               });

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenLinimetricRulerReadingIsValid_ReturnsOk")]
    public async Task WhenLinimetricRulerReadingIsValid_ReturnsOk()
    {
        var request = AddReadingsRequestBuilder
            .CreateLinimetricRulerReading();

       _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                InstrumentBuilder.CreateLinimetricRuler()
                    .WithId(request.Readings[0].Instrument.Id)
            });

       _readingRepository
           .Setup(x => x.GetExistingReadings(
               It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
           .ReturnsAsync(
               new[]
               {
                   new ExistingReadingsResult(
                       Guid.NewGuid(),
                       DateTime.Now,
                       false)
               });

        _readingRepository
            .Setup(x => x.AddAsync(It.IsAny<List<Domain.Entities.Reading>>()))
            .Returns(Task.CompletedTask);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }

    [Fact(DisplayName = "WhenSettlementGaugeReadingDepthIsInvalid_ReturnsBadRequest")]
    public async Task WhenSettlementGaugeReadingDepthIsInvalid_ReturnsBadRequest()
    {
        var settlementGauge = InstrumentBuilder
            .CreateSettlementGauge();

        var request = AddReadingsRequestBuilder
            .CreateSettlementGaugeReading()
            .WithMeasurements(settlementGauge.Measurements)
            .WithDepth(50);
        
        settlementGauge = settlementGauge.WithId(request.Readings[0].Instrument.Id);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                settlementGauge
            });

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);

        response.Errors.Single().Message.Should()
            .Be("The depth value must be equal to the difference between the top quota and the measurement quota minus the absolute settlement.");
    }

    [Fact(DisplayName = "WhenSettlementGaugeReadingDeltaRefIsInvalid_ReturnsBadRequest")]
    public async Task WhenSettlementGaugeReadingDeltaRefIsInvalid_ReturnsBadRequest()
    {
        var settlementGauge = InstrumentBuilder
            .CreateSettlementGauge();

        var request = AddReadingsRequestBuilder
            .CreateSettlementGaugeReading()
            .WithMeasurements(settlementGauge.Measurements)
            .WithDeltaRef(null);
        
        settlementGauge = settlementGauge.WithId(request.Readings[0].Instrument.Id);

        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                settlementGauge
            });

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenSettlementGaugeReadingAbsoluteSettlementIsInvalid_ReturnsBadRequest")]
    public async Task WhenSettlementGaugeReadingAbsoluteSettlementIsInvalid_ReturnsBadRequest()
    {
        var settlementGauge = InstrumentBuilder
            .CreateSettlementGauge();

        var request = AddReadingsRequestBuilder
            .CreateSettlementGaugeReading()
            .WithMeasurements(settlementGauge.Measurements)
            .WithAbsoluteSettlement(159);

        settlementGauge = settlementGauge.WithId(request.Readings[0].Instrument.Id);
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                settlementGauge
            });

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "WhenSettlementGaugeReadingRelativeSettlementIsInvalid_ReturnsBadRequest")]
    public async Task WhenSettlementGaugeReadingRelativeSettlementIsInvalid_ReturnsBadRequest()
    {
        var settlementGauge = InstrumentBuilder
            .CreateSettlementGauge();

        var request = AddReadingsRequestBuilder
            .CreateSettlementGaugeReading()
            .WithMeasurements(settlementGauge.Measurements)
            .WithRelativeSettlement(159);

        settlementGauge = settlementGauge.WithId(request.Readings[0].Instrument.Id);
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                settlementGauge
            });

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);

        response.Errors.Single().Message.Should()
            .Be("The relative settlement value must be equal to the difference between the delta ref and the measurement delta ref or the difference between absolute settlement and reference magnetic ring reading absolute settlement. And for the reference ring must be zero.");
    }

    [Fact(DisplayName = "WhenSettlementGaugeReadingQuotaIsInvalid_ReturnsBadRequest")]
    public async Task WhenSettlementGaugeReadingQuotaIsInvalid_ReturnsBadRequest()
    {
        var settlementGauge = InstrumentBuilder
            .CreateSettlementGauge();

        var request = AddReadingsRequestBuilder
            .CreateSettlementGaugeReading()
            .WithMeasurements(settlementGauge.Measurements)
            .WithQuota((decimal?)12.2m);

        settlementGauge = settlementGauge.WithId(request.Readings[0].Instrument.Id);
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                settlementGauge
            });

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);

        response.Errors.Single().Message.Should()
            .Be("The quota value must be equal to the sum of the delta ref and the reference magnetic ring quota. And for the reference ring must be the measurement quota.");
    }

    [Fact(DisplayName = "WhenSettlementGaugeReadingRelativeDepthIsInvalid_ReturnsBadRequest")]
    public async Task WhenSettlementGaugeReadingRelativeDepthIsInvalid_ReturnsBadRequest()
    {
        var settlementGauge = InstrumentBuilder
            .CreateSettlementGauge();

        var request = AddReadingsRequestBuilder
            .CreateSettlementGaugeReading()
            .WithMeasurements(settlementGauge.Measurements)
            .WithRelativeDepth(122);

        settlementGauge = settlementGauge.WithId(request.Readings[0].Instrument.Id);
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                settlementGauge
            });

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);

        response.Errors.Single().Message.Should()
            .Be("The relative depth value must be equal to the difference between the top quota and the quota.");
    }

    [Fact(DisplayName = "WhenSettlementGaugeReadingIsValid_ReturnsOk")]
    public async Task WhenSettlementGaugeReadingIsValid_ReturnsOk()
    {
        var settlementGauge = InstrumentBuilder.CreateSettlementGauge();

        var request = AddReadingsRequestBuilder
            .CreateSettlementGaugeReading()
            .WithMeasurements(settlementGauge.Measurements);

        settlementGauge = settlementGauge.WithId(request.Readings[0].Instrument.Id);
        
        _instrumentRepository
            .Setup(x => x.GetAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Domain.Entities.Instrument>
            {
                settlementGauge
            });

        _readingRepository
            .Setup(x => x.GetExistingReadings(
                It.IsAny<IEnumerable<ExistingReadingsQuery>>()))
            .ReturnsAsync(
                new[]
                {
                    new ExistingReadingsResult(
                        Guid.NewGuid(),
                        DateTime.Now,
                        false)
                });

        _readingRepository
            .Setup(x => x.AddAsync(It.IsAny<List<Domain.Entities.Reading>>()))
            .Returns(Task.CompletedTask);

        var response = await _useCase
            .Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }
}