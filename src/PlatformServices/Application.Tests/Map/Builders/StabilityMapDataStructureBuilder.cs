using Coordinate.Core.Enums;
using System;

namespace Application.Tests.Map.Builders
{
    public static class StabilityMapDataStructureBuilder
    {
        public static Domain.Entities.Structure CreateDefault()
        {
            var structure = new Domain.Entities.Structure
            {
                Id = Guid.NewGuid(),
                Name = "Lorem Ipsum",
                CoordinateSetting = new()
                {
                    Format = Format.DecimalGeodetic,
                    Systems = new()
                    {
                        DecimalGeodetic = new()
                        {
                            Latitude = -24.70067429,
                            Longitude = -48.11312367
                        },
                        Utm = new()
                        {
                            Easting = 792108.849651,
                            Northing = 7265081.951877,
                            ZoneLetter = 'J',
                            ZoneNumber = 22
                        }
                    },
                    Datum = Datum.CorregoAlegre
                },
                MapConfiguration = new()
                {
                    GeneralZoom = 15,
                    InstrumentsZoom = 12
                },
            };          

            structure.AddSection(new()
            {
                Name = "TestSection",
                Coordinates = new()
                {
                    Datum = Coordinate.Core.Enums.Datum.SIRGAS2000,
                    UpstreamCoordinateSetting = new()
                    {
                        Format = Coordinate.Core.Enums.Format.UTM,
                        CoordinateSystems = new()
                        {
                            DecimalGeodetic = new()
                            {
                                Latitude = -14.184252,
                                Longitude = -39.695969
                            },
                            Utm = new()
                            {
                                Easting = 424900.39766945917,
                                Northing = 8431784.410635721,
                                ZoneNumber = 24,
                                ZoneLetter = 'L'
                            }
                        }
                    },
                    DownstreamCoordinateSetting = new()
                    {
                        Format = Coordinate.Core.Enums.Format.UTM,
                        CoordinateSystems = new()
                        {
                            DecimalGeodetic = new()
                            {
                                Latitude = -14.1835,
                                Longitude = -39.692343
                            },
                            Utm = new()
                            {
                                Easting = 8431868.74643495,
                                Northing = 425291.4373917451,
                                ZoneNumber = 24,
                                ZoneLetter = 'L'
                            }
                        }
                    },
                    MidpointCoordinateSetting = null
                },
                SkewLineAzimuth = 15,
                NormalLineAzimuth = 19,
                MapLineSetting = new()
                {
                    Color = "#ffffff",
                    Type = Domain.Enums.LineType.Continuous,
                    Width = 2
                }
            });

            return structure;
        }
    }
}
