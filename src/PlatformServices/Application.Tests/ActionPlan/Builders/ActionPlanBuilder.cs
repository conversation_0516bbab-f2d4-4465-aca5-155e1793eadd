using System;

namespace Application.Tests.ActionPlan.Builders
{
    public static class ActionPlanBuilder
    {
        public static Domain.Entities.ActionPlan CreateDefault()
        {
            return new Domain.Entities.ActionPlan
            {
                Occurrence = null,
                Area = Domain.Enums.ActionPlanArea.Geology,
                ExpirationDate = DateTime.Now,
                Recommendation = "Recommendation",
                Attachments = new(),
                Severity = Domain.Enums.ActionPlanSeverity.NotSevere,
                Status = Domain.Enums.ActionPlanStatus.Cancelled,
                Structure = new() { Id = Guid.NewGuid() },
                Tendency = Domain.Enums.ActionPlanTendency.RapidlyDeteriorating,
                Urgency = Domain.Enums.ActionPlanUrgency.VeryUrgent,
                Active = true,
                Id = Guid.NewGuid(),
                CreatedBy = new() { Id = Guid.NewGuid() }
            };
        }
    }
}
