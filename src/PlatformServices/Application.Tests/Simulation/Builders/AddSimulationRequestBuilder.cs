using Domain.Enums;
using Model.Simulation.Add.Request;
using System;

namespace Application.Tests.Simulation.Builders
{
    public static class AddSimulationRequestBuilder
    {
        public static AddSimulationRequest CreateValid()
        {
            return new()
            {
                EndDate = DateTime.UtcNow.AddDays(1),
                StartDate = DateTime.UtcNow,
                ReadingStatisticalMeasure = ReadingStatisticalMeasure.Maximum,
                RequestedBy = Guid.NewGuid(),
                SafetyFactorTarget = 5,
                IgnoreDamagedInstruments = true,
                RequestedBySuperSupport = true,
                ShouldEvaluateDrainedCondition = true,
                ShouldEvaluatePseudoStaticCondition = true,
                ShouldEvaluateUndrainedCondition = true,
                SeismicCoefficient = new()
                {
                    Horizontal = 5,
                    Vertical = 5
                },
                WaterTableConfiguration = WaterTableConfiguration.Statistical,
                Slide2Configuration = new()
                {
                    CircularParameters = new()
                    {
                        CalculationMethods = new()
                        {
                            CalculationMethod.CorpsOfEngineers1
                        },
                        CirclesPerDivision = 10,
                        RadiusIncrement = 10,
                        CircularSearchMethod = CircularSearchMethod.GridSearch,
                        DivisionsAlongSlope = 10,
                        DivisionsNextIteration = 10,
                        NumberOfIterations = 10,
                        NumberOfSurfaces = 10,
                    },
                    NonCircularParameters = new()
                    {
                        CalculationMethods = new()
                        {
                            CalculationMethod.CorpsOfEngineers1
                        },
                        DivisionsAlongSlope = 10,
                        DivisionsNextIteration = 10,
                        NumberOfIterations = 10,
                        NumberOfSurfaces = 10,
                        InitialNumberOfIterations = 10,
                        InitialNumberOfSurfaceVertices = 10,
                        MaximumIterations = 10,
                        MaximumNumberOfSteps = 10,
                        NonCircularSearchMethod = NonCircularSearchMethod.PathSearch,
                        NumberOfFactorsSafetyComparedBeforeStopping = 10,
                        NumberOfNests = 10,
                        NumberOfParticles = 10,
                        NumberOfVerticesAlongSurface = 10,
                        SurfacesPerDivision = 10,
                        ToleranceForStoppingCriterion = 10
                    }
                },
                RequestedUserRole = Domain.Enums.Role.SuperSupport,
                Sections = new()
                {
                    new()
                    {
                        BeachLengthStatisticalMeasure = ReadingStatisticalMeasure.Maximum,
                        SectionId = Guid.NewGuid(),
                        MinimumDrainedDepth = 4,
                        MinimumPseudoStaticDepth = 4,
                        MinimumUndrainedDepth = 4
                    }
                }
            };
        }

        public static AddSimulationRequest CreateValidWithSpecificWaterTable()
        {
            return new()
            {
                DownstreamLinimetricRulerQuota = 50,
                DownstreamLinimetricRulerStatisticalMeasure = ReadingStatisticalMeasure.Specific,
                UpstreamLinimetricRulerQuota = 15,
                UpstreamLinimetricRulerStatisticalMeasure = ReadingStatisticalMeasure.Specific,
                ReadingStatisticalMeasure = ReadingStatisticalMeasure.Specific,
                RequestedBy = Guid.NewGuid(),
                SafetyFactorTarget = 5,
                IgnoreDamagedInstruments = true,
                RequestedBySuperSupport = true,
                ShouldEvaluateDrainedCondition = true,
                ShouldEvaluatePseudoStaticCondition = true,
                ShouldEvaluateUndrainedCondition = true,
                SeismicCoefficient = new()
                {
                    Horizontal = 5,
                    Vertical = 5
                },
                WaterTableConfiguration = WaterTableConfiguration.Specific,
                Slide2Configuration = new()
                {
                    CircularParameters = new()
                    {
                        CalculationMethods = new()
                        {
                            CalculationMethod.CorpsOfEngineers1
                        },
                        CirclesPerDivision = 10,
                        RadiusIncrement = 10,
                        CircularSearchMethod = CircularSearchMethod.GridSearch,
                        DivisionsAlongSlope = 10,
                        DivisionsNextIteration = 10,
                        NumberOfIterations = 10,
                        NumberOfSurfaces = 10,
                    },
                    NonCircularParameters = new()
                    {
                        CalculationMethods = new()
                        {
                            CalculationMethod.CorpsOfEngineers1
                        },
                        DivisionsAlongSlope = 10,
                        DivisionsNextIteration = 10,
                        NumberOfIterations = 10,
                        NumberOfSurfaces = 10,
                        InitialNumberOfIterations = 10,
                        InitialNumberOfSurfaceVertices = 10,
                        MaximumIterations = 10,
                        MaximumNumberOfSteps = 10,
                        NonCircularSearchMethod = NonCircularSearchMethod.PathSearch,
                        NumberOfFactorsSafetyComparedBeforeStopping = 10,
                        NumberOfNests = 10,
                        NumberOfParticles = 10,
                        NumberOfVerticesAlongSurface = 10,
                        SurfacesPerDivision = 10,
                        ToleranceForStoppingCriterion = 10
                    }
                },
                RequestedUserRole = Domain.Enums.Role.SuperSupport,
                Sections = new()
                {
                    new()
                    {
                        BeachLengthStatisticalMeasure = ReadingStatisticalMeasure.Specific,
                        BeachLength = 10,
                        SectionId = Guid.NewGuid(),
                        MinimumDrainedDepth = 4,
                        MinimumPseudoStaticDepth = 4,
                        MinimumUndrainedDepth = 4,
                        Instruments = new()
                        {
                            new()
                            {
                                InstrumentId = Guid.NewGuid(),
                                Quota = 25
                            }
                        }
                    }
                }
            };
        }
    }
}
