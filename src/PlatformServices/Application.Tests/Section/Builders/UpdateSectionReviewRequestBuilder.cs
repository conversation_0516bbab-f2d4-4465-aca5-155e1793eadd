using System;
using System.IO;
using Dxf.Core.Extensions;
using IxMilia.Dxf;
using Model._Shared;
using Model.Section.V2._Shared;
using Model.Section.V2.UpdateReview.Request;
using File = Model._Shared.File.File;

namespace Application.Tests.Section.Builders;

public static class UpdateSectionReviewRequestBuilder
{
    public static UpdateSectionReviewRequest CreateDefault()
    {
        return new UpdateSectionReviewRequest()
        {
            Id = Guid.NewGuid(),
            Section = new EntityReference(Guid.NewGuid()),
            SectionReviewV2 = SectionReviewRequestV2Builder.CreateDefault()
        };
    }

    public static UpdateSectionReviewRequest WithReviewId(
        this UpdateSectionReviewRequest request,
        Guid reviewId)
    {
        return request with { Id = reviewId };
    }

    public static UpdateSectionReviewRequest WithInvalidDrawing(
        this UpdateSectionReviewRequest request)
    {
        return request with
        {
            SectionReviewV2 = request.SectionReviewV2 with
            {
                Drawing = new File
                {
                    Name = $"{Guid.NewGuid().ToString()}.dxf",
                    Base64 = DxfFileBuilder
                        .CreateDefault()
                        .WithPolyline(new DxfPoint(0,10,0), new DxfPoint(600,10,0))
                        .AsBase64(),
                }
            }
        };
    }
}
