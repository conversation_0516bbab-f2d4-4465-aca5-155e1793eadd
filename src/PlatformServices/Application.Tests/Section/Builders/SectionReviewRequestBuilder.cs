using System;
using Bogus;
using Model.Section.V2._Shared;

namespace Application.Tests.Section.Builders;

public static class SectionReviewRequestBuilder
{
    public static SectionReviewRequest CreateDefault()
    {
        return new Faker<SectionReviewRequest>()
            .CustomInstantiator(faker => new SectionReviewRequest
            {
                Id = Guid.NewGuid(),
                Index = 1,
                StartDate = faker.Date.Recent(),
                SectionType = new(Guid.NewGuid())
            })
            .Generate();
    }
}