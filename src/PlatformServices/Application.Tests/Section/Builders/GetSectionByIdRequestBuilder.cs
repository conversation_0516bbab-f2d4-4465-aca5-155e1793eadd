using Model.Section.GetById.Request;
using System;

namespace Application.Tests.Section.Builders
{
    public static class GetSectionByIdRequestBuilder
    {
        public static GetSectionByIdRequest CreateDefault()
        {
            return new GetSectionByIdRequest
            {
                Id = Guid.NewGuid(),
                RequestedBy = Guid.NewGuid(),
                RequestedBySuperSupport = true,
                RequestedUserRole = Domain.Enums.Role.SuperSupport
            };
        }

        public static GetSectionByIdRequest WithRequest(
            this GetSectionByIdRequest request,
            Guid id)
        {
            request.Id = id;

            return request;
        }
    }
}
