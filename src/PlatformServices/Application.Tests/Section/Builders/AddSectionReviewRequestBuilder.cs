using System;
using IxMilia.Dxf;
using Model._Shared;
using Model._Shared.File;
using Model.Section.V2.AddReview.Request;

namespace Application.Tests.Section.Builders;

public static class AddSectionReviewRequestBuilder
{
    public static AddSectionReviewRequest CreateDefault()
    {
        return new AddSectionReviewRequest
        {
            Section = new EntityReference(Guid.NewGuid()),
            SectionReviewV2 = SectionReviewRequestV2Builder.CreateDefault()
        };
    }

    public static AddSectionReviewRequest WithIndex(
        this AddSectionReviewRequest request,
        int index)
    {
        return request with
        {
            SectionReviewV2 = request.SectionReviewV2 with
            {
                Index = index
            }
        };
    }

    public static AddSectionReviewRequest WithInvalidDrawing(
        this AddSectionReviewRequest request)
    {
        return request with
        {
            SectionReviewV2 = request.SectionReviewV2 with
            {
                Drawing = new File
                {
                    Name = $"{Guid.NewGuid().ToString()}.dxf",
                    Base64 = DxfFileBuilder
                        .CreateDefault()
                        .WithPolyline(
                            new DxfPoint(0, 10, 0),
                            new DxfPoint(600, 10, 0))
                        .AsBase64(),
                }
            }
        };
    }
}
