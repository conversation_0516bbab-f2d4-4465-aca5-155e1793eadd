using System;
using System.Collections.Generic;
using Domain.Entities;

namespace Application.Tests.Section.Builders
{
    public static class SectionBuilder
    {
        public static Domain.Entities.Section CreateDefault()
        {
            var section = new Domain.Entities.Section()
            {
                Name = "TestSection",
                Client = new() { Id = Guid.NewGuid() },
                ClientUnit = new() { Id = Guid.NewGuid() },
                Structure = new() { Id = Guid.NewGuid() },
                IsSkew = false,
                Coordinates = new()
                {
                    Datum = Coordinate.Core.Enums.Datum.SIRGAS2000,
                    UpstreamCoordinateSetting = new()
                    {
                        Format = Coordinate.Core.Enums.Format.UTM,
                        CoordinateSystems = new()
                        {
                            DecimalGeodetic = new()
                            {
                                Latitude = -14.184252,
                                Longitude = -39.695969
                            },
                            Utm = new()
                            {
                                Easting = 424900.39766945917,
                                Northing = 8431784.410635721,
                                ZoneNumber = 24,
                                ZoneLetter = 'L'
                            }
                        }
                    },
                    DownstreamCoordinateSetting = new()
                    {
                        Format = Coordinate.Core.Enums.Format.UTM,
                        CoordinateSystems = new()
                        {
                            DecimalGeodetic = new()
                            {
                                Latitude = -14.1835,
                                Longitude = -39.692343
                            },
                            Utm = new()
                            {
                                Easting = 8431868.74643495,
                                Northing = 425291.4373917451,
                                ZoneNumber = 24,
                                ZoneLetter = 'L'
                            }
                        }
                    },
                    MidpointCoordinateSetting = null
                },
                SkewLineAzimuth = 15,
                NormalLineAzimuth = 19,
                MapLineSetting = new()
                {
                    Color = "#ffffff",
                    Type = Domain.Enums.LineType.Continuous,
                    Width = 2
                }
            };

            var sectionReview = new Domain.Entities.SectionReview()
            {
                Index = 1,
                Drawing =
                    new()
                    {
                        Name = "test.dxf",
                        UniqueName = "22038d476a1b471f9031b9b12fb2c3b0",
                    },
                StartDate = DateTime.UtcNow,
                Sli = new()
                {
                    Name = "test.sli",
                    UniqueName = "22038d476a1b471f9031b9b12fb2c3b0",
                },
                Section = section
            };
            
            sectionReview.SetSectionType(new() { Id = Guid.NewGuid() });

            section.AddSectionReview(sectionReview);

            var downstreamLinimetricRuler = new Domain.Entities.Instrument()
            {
                Id = Guid.NewGuid(),
                Identifier = "TestDownstreamLinimetricRuler",
                LinimetricRulerPosition = Domain.Enums.LinimetricRulerPosition.Downstream,
                CoordinateSetting = new()
                {
                    Datum = Coordinate.Core.Enums.Datum.SIRGAS2000,
                    Format = Coordinate.Core.Enums.Format.UTM,
                    Systems = new() 
                    {
                        DecimalGeodetic = new()
                        {
                            Latitude = -14.184252,
                            Longitude = -39.695969
                        },
                        Utm = new()
                        {
                            Easting = 424900.39766945917,
                            Northing = 8431784.410635721,
                            ZoneNumber = 24,
                            ZoneLetter = 'L'
                        }
                    },
                }
            };

            downstreamLinimetricRuler.SetType(Domain.Enums.InstrumentType.LinimetricRuler);

            section.AddInstrument(downstreamLinimetricRuler);

            var upstreamLinimetricRuler = new Domain.Entities.Instrument()
            {
                Id = Guid.NewGuid(),
                Identifier = "TestUpstreamLinimetricRuler",
                LinimetricRulerPosition = Domain.Enums.LinimetricRulerPosition.Upstream,
                CoordinateSetting = new()
                {
                    Datum = Coordinate.Core.Enums.Datum.SIRGAS2000,
                    Format = Coordinate.Core.Enums.Format.UTM,
                    Systems = new()
                    {
                        DecimalGeodetic = new()
                        {
                            Latitude = -14.184252,
                            Longitude = -39.695969
                        },
                        Utm = new()
                        {
                            Easting = 424900.39766945917,
                            Northing = 8431784.410635721,
                            ZoneNumber = 24,
                            ZoneLetter = 'L'
                        }
                    },
                }
            };

            upstreamLinimetricRuler.SetType(Domain.Enums.InstrumentType.LinimetricRuler);

            section.AddInstrument(upstreamLinimetricRuler);

            return section;
        }

        public static Domain.Entities.Section WithId(
                       this Domain.Entities.Section section,
                                  Guid id)
        {
            section.Id = id;

            return section;
        }

        public static Domain.Entities.Section WithReview(
            this Domain.Entities.Section section,
            int index,
            Domain.ValueObjects.File file)
        {
            var sectionReview = new Domain.Entities.SectionReview()
            {
                Index = index,
                Drawing = file,
                StartDate = DateTime.UtcNow,
            };
            
            sectionReview.SetSectionType(new() { Id = Guid.NewGuid() });
            section.AddSectionReview(sectionReview);

            return section;
        }

        public static Domain.Entities.Section WithReview(
            this Domain.Entities.Section section,
            Domain.Entities.SectionReview sectionReview)
        {
            section.AddSectionReview(sectionReview);

            return section;
        }

        public static Domain.Entities.Section WithInstrument(
            this Domain.Entities.Section section,
            Domain.Entities.Instrument instrument)
        {
            section.AddInstrument(instrument);

            return section;
        }

        public static Domain.Entities.Section WithInstruments(
            this Domain.Entities.Section section,
            List<Domain.Entities.Instrument> instruments)
        {
            section.AddInstruments(instruments);

            return section;
        }

        public static Domain.Entities.Section WithConstructionStage(
            this Domain.Entities.Section section)
        {
            section.Reviews[0].ConstructionStages.Add(new ConstructionStage()
            {
                Id = Guid.NewGuid(),
                SectionReview = section.Reviews[0]
            });

            return section;
        }
    }
}
