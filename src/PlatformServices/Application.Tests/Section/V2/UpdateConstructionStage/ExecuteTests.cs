using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Application.Core;
using Application.Section.V2.UpdateConstructionStage;
using Application.Services.BlobStorage;
using Application.Tests._Shared;
using Application.Tests.Section.Builders;
using Database.Repositories.Section;
using Database.Repositories.StaticMaterial;
using Database.Repositories.User;
using Domain.Entities;
using Model.Section.V2.UpdateConstructionStage.Request;

namespace Application.Tests.Section.V2.UpdateConstructionStage;

[Trait("UpdateConstructionStageUseCase", "Execute")]
public class ExecuteTests
{
    private readonly Mock<ISectionRepository> _sectionRepository = new();
    private readonly Mock<IUserRepository> _userRepository = new();

    private readonly Mock<IStaticMaterialRepository> _staticMaterialRepository =
        new();

    private readonly Mock<IBlobStorageService> _blobStorageService = new();
    private readonly UpdateConstructionStageUseCase _useCase;

    public ExecuteTests()
    {
        _useCase = new UpdateConstructionStageUseCase(
            _sectionRepository.Object,
            _userRepository.Object,
            _staticMaterialRepository.Object,
            _blobStorageService.Object);
    }

    [Fact(DisplayName = "When request is null, then returns bad request")]
    public async Task WhenRequestIsNull_ReturnsBadRequest()
    {
        UpdateConstructionStageRequest request = null;

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "When request is invalid, then returns bad request")]
    public async Task WhenRequestIsInvalid_ReturnsBadRequest()
    {
        var request = new UpdateConstructionStageRequest();

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(
        DisplayName = "When section does not exist, then returns bad request")]
    public async Task WhenSectionDoesNotExist_ReturnsBadRequest()
    {
        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync((Domain.Entities.Section)null);

        var request = UpdateConstructionStageRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(
        DisplayName =
            "When section does not exist, then returns error message")]
    public async Task WhenSectionDoesNotExist_ReturnsErrorMessage()
    {
        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync((Domain.Entities.Section)null);

        var request = UpdateConstructionStageRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var response = await _useCase.Execute(request);

        response.Errors
            .First()
            .Message
            .Should()
            .Be("Section not found in database.");
    }

    [Fact(
        DisplayName =
            "When section review does not exist, then returns bad request")]
    public async Task WhenSectionReviewDoesNotExist_ReturnsBadRequest()
    {
        var section = SectionBuilder.CreateDefault();

        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync(section);

        var request = UpdateConstructionStageRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(
        DisplayName =
            "When section review does not exist, then returns error message")]
    public async Task WhenSectionReviewDoesNotExist_ReturnsErrorMessage()
    {
        var section = SectionBuilder.CreateDefault();

        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync(section);

        var request = UpdateConstructionStageRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var response = await _useCase.Execute(request);

        response.Errors
            .First()
            .Message
            .Should()
            .Be("Section review not found in database.");
    }

    [Fact(
        DisplayName =
            "When construction stage does not exist, then returns bad request")]
    public async Task WhenConstructionStageDoesNotExist_ReturnsBadRequest()
    {
        var section = SectionBuilder.CreateDefault();

        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync(section);

        var request = UpdateConstructionStageRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid())
            .WithReviewId(section.Reviews[0].Id);

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(
        DisplayName =
            "When construction stage does not exist, then returns error message")]
    public async Task WhenConstructionStageDoesNotExist_ReturnsErrorMessage()
    {
        var section = SectionBuilder.CreateDefault();

        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync(section);

        var request = UpdateConstructionStageRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid())
            .WithReviewId(section.Reviews[0].Id);

        var response = await _useCase.Execute(request);

        response.Errors
            .First()
            .Message
            .Should()
            .Be("Construction stage not found in database.");
    }

    [Fact(
        DisplayName =
            "When user is not allowed to perform the action, then returns forbidden")]
    public async Task WhenUserIsNotAllowed_ReturnsForbidden()
    {
        var section = SectionBuilder
            .CreateDefault()
            .WithConstructionStage();

        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync(section);

        var request = UpdateConstructionStageRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid(), false)
            .WithRequesterStructures(new List<Guid> { Guid.NewGuid() })
            .WithReviewId(section.Reviews[0].Id)
            .WithId(section.Reviews[0].ConstructionStages[0].Id);

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.Forbidden);
    }

    [Fact(
        DisplayName =
            "When drawing validation fails, then returns bad request")]
    public async Task WhenDrawingValidationFails_ReturnsBadRequest()
    {
        var section = SectionBuilder
            .CreateDefault()
            .WithConstructionStage();

        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync(section);

        _staticMaterialRepository
            .Setup(repo => repo.GetByStructure(It.IsAny<Guid>()))
            .ReturnsAsync(new List<Domain.Entities.StaticMaterial>());

        var request = UpdateConstructionStageRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid())
            .WithReviewId(section.Reviews[0].Id)
            .WithId(section.Reviews[0].ConstructionStages[0].Id)
            .WithInvalidDrawing();

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(
        DisplayName =
            "When drawing validation fails, then returns error message")]
    public async Task WhenDrawingValidationFails_ReturnsErrorMessage()
    {
        var section = SectionBuilder
            .CreateDefault()
            .WithConstructionStage();

        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync(section);

        _staticMaterialRepository
            .Setup(repo => repo.GetByStructure(It.IsAny<Guid>()))
            .ReturnsAsync(new List<Domain.Entities.StaticMaterial>());

        var request = UpdateConstructionStageRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid())
            .WithReviewId(section.Reviews[0].Id)
            .WithId(section.Reviews[0].ConstructionStages[0].Id)
            .WithInvalidDrawing();

        var response = await _useCase.Execute(request);

        response.Errors
            .First()
            .Message
            .Should()
            .StartWith("The DXF file");
    }

    [Fact(
        DisplayName =
            "When an unhandled exception is thrown, then returns internal server error")]
    public async Task WhenExceptionIsThrown_ReturnsInternalServerError()
    {
        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ThrowsAsync(new Exception());

        var request = UpdateConstructionStageRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.InternalServerError);
    }

    [Fact(DisplayName = "When request is valid, then returns OK")]
    public async Task WhenRequestIsValid_ReturnsOk()
    {
        var section = SectionBuilder
            .CreateDefault()
            .WithConstructionStage();

        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync(section);

        var request = UpdateConstructionStageRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid())
            .WithReviewId(section.Reviews[0].Id)
            .WithId(section.Reviews[0].ConstructionStages[0].Id);

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }
}
