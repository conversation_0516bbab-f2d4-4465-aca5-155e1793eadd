using FluentMigrator;

namespace Database.Migrations;

[Migration(202507111002)]
public class Migration202507111002AlterReadingValuesTable : Migration
{
    public override void Up()
    {
        Create.Index("ix-reading-values-reading-id")
            .OnTable("reading-values")
            .OnColumn("reading-id"); 
        
        Create.Index("ix-reading-values-nature-id")
            .OnTable("reading-values")
            .OnColumn("nature-id"); 
    }

    public override void Down()
    {
        Delete.Index("ix-reading-values-reading-id")
            .OnTable("reading-values");
        
        Delete.Index("ix-reading-values-nature-id")
            .OnTable("reading-values");
    }
}