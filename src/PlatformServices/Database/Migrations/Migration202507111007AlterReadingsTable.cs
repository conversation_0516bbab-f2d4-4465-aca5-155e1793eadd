using FluentMigrator;
using FluentMigrator.SqlServer;

namespace Database.Migrations;

[Migration(202507111007)]
public class Migration202507111007AlterReadingsTable : Migration
{
    public override void Up()
    {
        Create.Index("ix-readings-is-deleted-include")
            .OnTable("readings")
            .OnColumn("is-deleted")
            .Ascending()
            .Include("search-identifier")
            .Include("instrument-id")
            .Include("is-referential");
    }

    public override void Down()
    {
        Delete.Index("ix-readings-is-deleted-include")
            .OnTable("readings");
    }
}