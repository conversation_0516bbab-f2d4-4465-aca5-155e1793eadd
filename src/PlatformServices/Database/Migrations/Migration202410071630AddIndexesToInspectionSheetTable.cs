using FluentMigrator;

namespace Database.Migrations
{
    [Migration(202410071630)]
    public class Migration202410071630AddIndexesToInspectionSheetTable : Migration
    {
        public override void Up()
        {
            Create
               .Index("idx-inspection-sheets-search-identifier")
               .OnTable("inspection-sheets")
               .OnColumn("search-identifier");

            Create
                .Index("idx-inspection-sheets-active")
                .OnTable("inspection-sheets")
                .OnColumn("active");

            Create
                .Index("idx-inspection-sheets-start-date")
                .OnTable("inspection-sheets")
                .OnColumn("start-date");

            Create
                .Index("idx-inspection-sheets-end-date")
                .OnTable("inspection-sheets")
                .OnColumn("end-date");

            Create
                .Index("idx-inspection-sheets-status")
                .OnTable("inspection-sheets")
                .OnColumn("status");

            Create
                .Index("idx-inspection-sheets-inspection-type")
                .OnTable("inspection-sheets")
                .OnColumn("inspection-type");
        }

        public override void Down()
        {
            Delete
                .Index("idx-inspection-sheets-search-identifier")
                .OnTable("inspection-sheets");

            Delete
                .Index("idx-inspection-sheets-active")
                .OnTable("inspection-sheets");

            Delete
                .Index("idx-inspection-sheets-start-date")
                .OnTable("inspection-sheets");

            Delete
                .Index("idx-inspection-sheets-end-date")
                .OnTable("inspection-sheets");

            Delete
                .Index("idx-inspection-sheets-status")
                .OnTable("inspection-sheets");

            Delete
                .Index("idx-inspection-sheets-inspection-type")
                .OnTable("inspection-sheets");
        }
    }
}
