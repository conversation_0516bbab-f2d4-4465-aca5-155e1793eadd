using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Coordinate.Core;
using Coordinate.Core.Classes;
using Coordinate.Core.Enums;
using Dapper;
using Database.Repositories.Outbox;
using Database.Repositories.Section.Extensions;
using Domain.Entities;
using Domain.Enums;
using Domain.Messages.Commands.Section;
using Domain.Messages.Events.Instrument;
using Domain.ValueObjects;
using Model.Dashboard.GetLatestUpdates.Response;
using Model.Dashboard.GetSectionsMap.Request;
using Model.Dashboard.GetSectionsMap.Response;
using Model.Section.GetByFiltersMaps.Request;
using Model.Section.List.Request;
using Model.Section.List.Response;
using Model.Section.PatchSectionsChartLineColor.Dto;
using Model.Section.PatchSectionsChartLineColor.Request;
using Model.Section.Search.Request;
using Model.Section.Search.Response;
using Model.Section.SearchHistory.Request;
using Model.Section.SearchHistory.Response;
using Model.Section.V2.GetDrawingFile.Request;
using Model.Section.V2.GetLengthData.Response;
using MapLineSetting = Model.Section._Shared.MapLineSetting.MapLineSetting;

namespace Database.Repositories.Section
{
    public class SectionRepository : ISectionRepository
    {
        private readonly string _connectionString;

        public SectionRepository(string connectionString)
        {
            _connectionString = connectionString;
        }

        public async Task<int> CountHistoryAsync(SearchSectionHistoryRequest request)
        {
            var param = new
            {
                request.SectionId,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.ExecuteScalarAsync<int>(Queries.CountHistory, param);
        }

        public async Task<IEnumerable<SearchSectionHistoryResponse>> SearchHistoryAsync(SearchSectionHistoryRequest request)
        {
            var param = new
            {
                request.SectionId,
                Skip = request.GetSkip(),
                request.PageSize,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.QueryAsync<SearchSectionHistoryResponse>(
                sql: Queries.SearchHistory,
                new[]
                {
                    typeof(SectionHistory),
                    typeof(Domain.Entities.User)
                },
                (records) =>
                {
                    var history = records[0] as SectionHistory;
                    var user = records[1] as Domain.Entities.User;

                    return new()
                    {
                        Id = history.Id,
                        Changes = history.Changes,
                        CreatedDate = history.CreatedDate,
                        ModifiedBy = new()
                        {
                            Id = user.Id,
                            FirstName = user.FirstName,
                            Surname = user.Surname,
                            Username = user.Username
                        }
                    };
                },
                splitOn: "Id",
                param: param
                );
        }

        public async Task<GetDashboardSectionsMapResponse> GetDashboardMapAsync(GetDashboardSectionsMapRequest request)
        {
            var param = new
            {
                request.StructureId,
                Types = new[] { InstrumentType.WaterLevelIndicator, InstrumentType.ElectricPiezometer, InstrumentType.OpenStandpipePiezometer }
            };

            var lookup = new Dictionary<Guid, GetDashboardSectionsMapResponse>();

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.QueryAsync(
                sql: Queries.GetDashboardSectionMap,
                new[]
                {
                    typeof(GetDashboardSectionsMapResponse),
                    typeof(DecimalGeodetic),
                    typeof(GetDashboardMapSection),
                    typeof(DecimalGeodetic),
                    typeof(DecimalGeodetic),
                    typeof(DecimalGeodetic),
                    typeof(MapLineSetting),
                    typeof(GetDashboardMapInstrument),
                    typeof(DecimalGeodetic)
                },
                (records) =>
                {
                    var map = records[0] as GetDashboardSectionsMapResponse;
                    var mapSection = records[2] as GetDashboardMapSection;
                    
                    if (!lookup.TryGetValue(map.StructureId, out var dashboardMapResponse))
                    {
                        dashboardMapResponse = map;

                        var structureCoordinate = records[1] as DecimalGeodetic;
                        
                        if (dashboardMapResponse.Datum != Datum.SIRGAS2000)
                        {
                            structureCoordinate = Helper.ToDatum(dashboardMapResponse.Datum, Datum.SIRGAS2000, structureCoordinate);
                        }

                        dashboardMapResponse.StructureCoordinate = structureCoordinate;
                        
                        lookup.Add(dashboardMapResponse.StructureId, dashboardMapResponse);
                    }

                    if (dashboardMapResponse != null && 
                        mapSection != null && 
                        dashboardMapResponse.Sections.All(section => section.Id != mapSection.Id))
                    {
                        var upstreamCoordinate = records[3] as DecimalGeodetic;
                        var downstreamCoordinate = records[4] as DecimalGeodetic;
                        var midpointCoordinate = records[5] as DecimalGeodetic;

                        if (mapSection.Datum != Datum.SIRGAS2000)
                        {
                            upstreamCoordinate = Helper.ToDatum(mapSection.Datum, Datum.SIRGAS2000, upstreamCoordinate);
                            downstreamCoordinate = Helper.ToDatum(mapSection.Datum, Datum.SIRGAS2000, downstreamCoordinate);
                            midpointCoordinate = midpointCoordinate != null
                                ? Helper.ToDatum(mapSection.Datum, Datum.SIRGAS2000, midpointCoordinate)
                                : null;
                        }
                        
                        mapSection.UpstreamCoordinate = upstreamCoordinate;
                        mapSection.DownstreamCoordinate = downstreamCoordinate;
                        mapSection.MidpointCoordinate = midpointCoordinate;
                        mapSection.MapLineSetting = records[6] as MapLineSetting;
                        
                        dashboardMapResponse.Sections.Add(mapSection);
                    }
                    else if (dashboardMapResponse.Sections.Any(x => x.Id == mapSection.Id))
                    {
                        mapSection = dashboardMapResponse.Sections.First(x => x.Id == mapSection.Id);
                    }

                    if (records[7] is GetDashboardMapInstrument instrument &&
                        mapSection != null &&
                        mapSection.Instruments.All(i => i.Id != instrument.Id) &&
                        instrument.SectionId == mapSection.Id)
                    {
                        var instrumentCoordinate = records[8] as DecimalGeodetic;
                            
                        if (instrument.Datum != Datum.SIRGAS2000)
                        {
                            instrumentCoordinate = Helper.ToDatum(instrument.Datum, Datum.SIRGAS2000, instrumentCoordinate);
                        }

                        instrument.Coordinate = instrumentCoordinate;

                        mapSection.Instruments.Add(instrument);
                    }

                    return map;
                },
                splitOn: "Latitude,Id,Latitude,Latitude,Latitude,Color,Id,Latitude",
                param: param);

            return lookup.Values.FirstOrDefault();
        }

        public async Task<IEnumerable<Domain.Entities.Section>> GetByFiltersMapsAsync(
            GetSectionByFiltersMapsRequest request)
        {
            var param = new
            {
                request.StructureIds
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.QueryAsync(
                sql: Queries.GetByFiltersMaps,
                new[]
                {
                    typeof(Domain.Entities.Section),
                    typeof(SectionCoordinates),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(Domain.ValueObjects.MapLineSetting),
                },
                (records) =>
                {
                    var section = records[0] as Domain.Entities.Section;
                    var sectionCoordinates = records[1] as SectionCoordinates;
                    var upstreamDecimalGeodeticCoordinate = records[2] as DecimalGeodetic;
                    var upstreamUtmCoordinate = records[3] as Utm;
                    var upstreamCoordinateSetting = records[4] as SectionCoordinateSetting;
                    var downstreamDecimalGeodeticCoordinate = records[5] as DecimalGeodetic;
                    var downstreamUtmCoordinate = records[6] as Utm;
                    var downstreamCoordinateSetting = records[7] as SectionCoordinateSetting;
                    var midpointDecimalGeodeticCoordinate = records[8] as DecimalGeodetic;
                    var midpointUtmCoordinate = records[9] as Utm;
                    var midpointCoordinateSetting = records[10] as SectionCoordinateSetting;
                    var mapLineSetting = records[11] as Domain.ValueObjects.MapLineSetting;

                    upstreamCoordinateSetting.CoordinateSystems = new()
                    {
                        DecimalGeodetic = upstreamDecimalGeodeticCoordinate,
                        Utm = upstreamUtmCoordinate
                    };

                    downstreamCoordinateSetting.CoordinateSystems = new()
                    {
                        DecimalGeodetic = downstreamDecimalGeodeticCoordinate,
                        Utm = downstreamUtmCoordinate
                    };

                    if (midpointCoordinateSetting != null)
                    {
                        midpointCoordinateSetting.CoordinateSystems = new()
                        {
                            DecimalGeodetic = midpointDecimalGeodeticCoordinate,
                            Utm = midpointUtmCoordinate
                        };
                    }

                    sectionCoordinates.UpstreamCoordinateSetting = upstreamCoordinateSetting;
                    sectionCoordinates.DownstreamCoordinateSetting = downstreamCoordinateSetting;
                    sectionCoordinates.MidpointCoordinateSetting = midpointCoordinateSetting;

                    section.Coordinates = sectionCoordinates;
                    section.MapLineSetting = mapLineSetting;

                    return section;
                },
                splitOn: "Datum,Latitude,Northing,Format,Latitude,Northing,Format,Latitude,Northing,Format,Color",
                param: param);
        }

        public async Task UpdateActiveAsync(Domain.Entities.Section section)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                await connection.ExecuteAsync(
                    Queries.UpdateOnlyActive,
                    section.ToDbUpdateOnlyActiveParam(),
                    transaction);

                await connection.ExecuteAsync(
                    Queries.InsertSectionHistory, 
                    section.History.ToDbWriteParam(), 
                    transaction);

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<int> CountAsync(
            SearchSectionRequest request)
        {
            var param = new
            {
                request.Query.SearchIdentifier,
                request.Query.ClientId,
                request.Query.ClientUnitId,
                request.Query.Active,
                request.Body.SectionIds,
                SectionsCount = request.Body.SectionIds?.Count,
                request.Body.StructureIds,
                StructuresCount = request.Body.StructureIds?.Count,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.ExecuteScalarAsync<int>(Queries.Count, param);
        }

        public async Task<IEnumerable<SearchSectionResponse>> SearchAsync(
            SearchSectionRequest request)
        {
            var param = new
            {
                request.Query.SearchIdentifier,
                request.Query.ClientId,
                request.Query.ClientUnitId,
                request.Query.Active,
                Skip = request.Query.GetSkip(),
                request.Query.PageSize,
                request.Body.SectionIds,
                SectionsCount = request.Body.SectionIds?.Count,
                request.Body.StructureIds,
                StructuresCount = request.Body.StructureIds?.Count,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures,
                WaterLevelIndicatorType = (int)InstrumentType.WaterLevelIndicator,
                ElectricPiezometerType = (int)InstrumentType.ElectricPiezometer,
                OpenStandpipePiezometerType = (int)InstrumentType.OpenStandpipePiezometer,
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.QueryAsync<SearchSectionResponse>(
                sql: Queries.Search,
                new[]
                {
                    typeof(Domain.Entities.Section),
                    typeof(Domain.Entities.ClientUnit),
                    typeof(Domain.Entities.Structure),
                    typeof(int),
                    typeof(int),
                    typeof(int)
                },
                (records) =>
                {
                    var section = records[0] as Domain.Entities.Section;
                    var clientUnit = records[1] as Domain.Entities.ClientUnit;
                    var structure = records[2] as Domain.Entities.Structure;
                    var activePzCounter = records[3] as int?;
                    var activeWliCounter = records[4] as int?;
                    var reviewsCounter = records[5] as int?;

                    return new()
                    {
                        Id = section.Id,
                        Name = section.Name,
                        Active = section.Active,
                        SearchIdentifier = section.SearchIdentifier,
                        NormalLineAzimuth = section.NormalLineAzimuth,
                        SkewLineAzimuth = section.SkewLineAzimuth,
                        ClientUnit = new()
                        {
                            Id = clientUnit.Id,
                            Name = clientUnit.Name,
                            Active = clientUnit.Active,
                            ClientId = clientUnit.ClientId
                        },
                        Structure = new()
                        {
                            Id = structure.Id,
                            Name = structure.Name,
                            ClientUnitId = structure.ClientUnitId
                        },
                        ActivePzCounter = (int)activePzCounter,
                        ActiveWliCounter = (int)activeWliCounter,
                        ReviewsCounter = (int)reviewsCounter
                    };
                },
                splitOn: "Id,Id,ActivePzCounter,ActiveWliCounter,ReviewsCounter",
                param: param);
        }

        public async Task AddAsync(Domain.Entities.Section section)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                await connection.ExecuteAsync(
                   Queries.Insert, 
                   section.ToDbWriteParam(), 
                   transaction);

                await connection.ExecuteAsync(
                    Queries.InsertSectionReview, 
                    section.Reviews.ToDbInsertParam(), 
                    transaction);

                var constructionStages = section.Reviews
                    .SelectMany(review => review.ConstructionStages);
                
                await connection.ExecuteAsync(
                    Queries.InsertConstructionStage, 
                    constructionStages.ToDbInsertParam(), 
                    transaction);

                await connection.ExecuteAsync(
                    Queries.InsertInstrument, 
                    section.Instruments.ToDbInsertParam(section.Id), 
                    transaction);

                await connection.ExecuteAsync(
                    Queries.InsertSectionHistory, 
                    section.History.ToDbWriteParam(), 
                    transaction);

                await connection.ExecuteAsync(
                    OutboxQueries.Insert, 
                    section.ToCreateSliFileOutboxParam(), 
                    transaction);

                if (section.StabilityAnalysisConfiguration is not null)
                {
                    await connection.ExecuteAsync(
                        Queries.InsertStabilityAnalysisConfiguration,
                        section.StabilityAnalysisConfiguration.ToDbWriteParam(),
                        transaction);
                    
                    var circularCalculationMethods = section.StabilityAnalysisConfiguration?
                        .ToCircularCalculationMethodsDbWriteParam();

                    if (circularCalculationMethods is not null)
                    {
                        foreach (var calculationMethod in circularCalculationMethods)
                        {
                            await connection.ExecuteAsync(
                                Queries.UpsertCircularCalculationMethods,
                                calculationMethod,
                                transaction);
                        }
                    }

                    var nonCircularCalculationMethods = section.StabilityAnalysisConfiguration?
                        .ToNonCircularCalculationMethodsDbWriteParam();

                    if (nonCircularCalculationMethods is not null)
                    {
                        foreach (var calculationMethod in nonCircularCalculationMethods)
                        {
                            await connection.ExecuteAsync(
                                Queries.UpsertNonCircularCalculationMethods,
                                calculationMethod,
                                transaction);
                        }
                    }
                }
                
                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<bool> NameExists(
            string name,
            Guid structureId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.NameExists, new
            {
                Name = name,
                StructureId = structureId
            });

            return res > 0;
        }

        public async Task<bool> NameExists(
            string name,
            Guid structureId,
            Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.NameExistsWithOtherId, new
            {
                Id = id,
                Name = name,
                StructureId = structureId,
            });

            return res > 0;
        }

        public Task DeleteAsync(
            Guid id)
        {
            throw new NotImplementedException();
        }

        public async Task<IEnumerable<Guid>> GetByStructureIdAsync(
            Guid structureId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            return await connection.QueryAsync<Guid>(Queries.GetByStructureId, new
            {
                StructureId = structureId
            });
        }

        public async Task<List<SectionReview>> GetSectionReviewsAsync(Guid sectionId)
        {
            var param = new
            {
                SectionId = sectionId
            };

            var lookup = new Dictionary<Guid, SectionReview>();
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.QueryAsync(sql: Queries.GetReviewsBySectionId,
                new[]
                {
                    typeof(SectionReview),
                    typeof(File),
                    typeof(File),
                    typeof(ConstructionStage),
                    typeof(File),
                    typeof(File),
                },
                (records) =>
                {
                    var sectionReview = records[0] as SectionReview;
                    var reviewDxf = records[1] as File;
                    var reviewSli = records[2] as File;
                    var stage = records[3] as ConstructionStage;
                    var stageDxf = records[4] as File;
                    var stageSli = records[5] as File;

                    if (!lookup.TryGetValue(sectionReview.Id, out var sectionReviewModel))
                    {
                        lookup.Add(sectionReview.Id, sectionReview);
                        sectionReviewModel = sectionReview;
                    }

                    sectionReviewModel.Drawing = reviewDxf;
                    sectionReviewModel.Sli = reviewSli;

                    if (stage != null && !sectionReviewModel.ConstructionStages.Any(x => x.Id == stage.Id))
                    {
                        stage.Drawing = stageDxf;
                        stage.Sli = stageSli;

                        sectionReviewModel.ConstructionStages.Add(stage);
                    }

                    return sectionReviewModel;
                },
                splitOn: "UniqueName,UniqueName,Id,UniqueName,UniqueName",
                param: param);

            return lookup.Values.ToList();
        }

        public async Task<LatestUpdateResponseItem> GetDashboardLatestAsync(Guid structureId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();

            return await connection.QuerySingleOrDefaultAsync<LatestUpdateResponseItem>(
                sql: Queries.GetDashboardLatest,
                param: new { StructureId = structureId });
        }

        public async Task<SectionReview> GetReviewAsync(Guid reviewId)
        {
            var lookup = new Dictionary<Guid, SectionReview>();

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            await connection.QueryAsync(sql: Queries.GetReviewById,
                new[]
                {
                    typeof(SectionReview),
                    typeof(File),
                    typeof(File),
                    typeof(Domain.Entities.StructureType),
                    typeof(Domain.Entities.User),
                    typeof(ConstructionStage),
                    typeof(File),
                    typeof(File),
                },
                (records) =>
                {
                    var sectionReview = records[0] as SectionReview;
                    var reviewDxf = records[1] as File;
                    var reviewSli = records[2] as File;
                    var structureType = records[3] as Domain.Entities.StructureType;
                    var user = records[4] as Domain.Entities.User;
                    var stage = records[5] as ConstructionStage;
                    var stageDxf = records[6] as File;
                    var stageSli = records[7] as File;

                    if (!lookup.TryGetValue(sectionReview.Id, out var sectionReviewModel))
                    {
                        lookup.Add(sectionReview.Id, sectionReview);
                        sectionReviewModel = sectionReview;
                    }

                    sectionReviewModel.Drawing = reviewDxf;
                    sectionReviewModel.Sli = reviewSli;
                    sectionReviewModel.SetStructureType(structureType);
                    
                    sectionReviewModel.CreatedBy = user;

                    if (stage != null && !sectionReviewModel.ConstructionStages.Any(x => x.Id == stage.Id))
                    {
                        stage.Drawing = stageDxf;
                        stage.Sli = stageSli;
                        sectionReviewModel.ConstructionStages.Add(stage);
                    }

                    return sectionReviewModel;
                },
                splitOn: "UniqueName,UniqueName,Id,Id,Id,UniqueName,UniqueName",
                param: new
                {
                    Id = reviewId
                });

            return lookup.Values.FirstOrDefault();
        }

        public async Task<Domain.Entities.Section> GetAsync(
            Guid id)
        {
            var lookup = new Dictionary<Guid, Domain.Entities.Section>();
            var lookupStructureType = new Dictionary<Guid, Domain.Entities.SectionType>();
            
            var template = new SqlBuilder()
                .Where("[sections].[id] = @Id", new { Id = id })
                .AddTemplate(Queries.GetById);
            
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            await connection.QueryAsync(
                sql: template.RawSql,
                new[]
                {
                    typeof(Domain.Entities.Section),
                    typeof(SectionCoordinates),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(Domain.ValueObjects.MapLineSetting),
                    typeof(Domain.Entities.Structure),
                    typeof(Domain.Entities.Client),
                    typeof(Domain.Entities.ClientUnit),
                    typeof(Domain.Entities.SectionType),
                    typeof(SectionReview),
                    typeof(SectionLength),
                    typeof(File),
                    typeof(Domain.Entities.Instrument),
                    typeof(CoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(Measurement),
                    typeof(SectionTypeActivity),
                    typeof(File),
                    typeof(Domain.Entities.User),
                    typeof(ConstructionStage),
                    typeof(SectionLength),
                    typeof(File),
                    typeof(File),
                    typeof(StabilityAnalysisConfiguration),
                    typeof(CircularParameters),
                    typeof(NonCircularParameters),
                    typeof(Orientation),
                    typeof(CalculationMethod),
                    typeof(CalculationMethod),
                },
                (records) => BindValues(records, lookup, lookupStructureType),
                splitOn: "Datum,Latitude,Northing,Format,Latitude,Northing,Format,Latitude,Northing,Format,Color,Id,Id,Id,Id,Id,DxfLengthInMeters,UniqueName,Id,Datum,Latitude,Northing,Id,Id,UniqueName,Id,Id,DxfLengthInMeters,UniqueName,UniqueName,Id,CircularSearchMethod,NonCircularSearchMethod,Horizontal,CalculationMethods,CalculationMethods",
                param: template.Parameters);

            return lookup.Values.FirstOrDefault();
        }

        public async Task<IEnumerable<ListSectionResponse>> ListAsync(
            ListSectionRequest request)
        {
            var queryBuilder = new SqlBuilder();

            if (request.Active.HasValue)
            {
                queryBuilder.Where("[sections].[active] = @Active", new { Active = request.Active.Value });
            }

            if (request.StructureId.HasValue)
            {
                queryBuilder.Where("[sections].[structure-id] = @StructureId", new { StructureId = request.StructureId.Value });
            }

            if (request.HasDrawing.HasValue && request.HasDrawing.Value)
            {
                queryBuilder.Join("[section-reviews] ON [sections].[id] = [section-reviews].[section-id]");
                queryBuilder.Where("[section-reviews].[drawing-name] IS NOT NULL");
                queryBuilder.Where(@"[section-reviews].[start-date] = (
                        SELECT MAX([inner-sr].[start-date]) 
                        FROM [section-reviews] AS [inner-sr]
                        WHERE [inner-sr].[section-id] = [section-reviews].[section-id])");
            }

            if (!request.RequestedBySuperSupport)
            {
                queryBuilder.Where("[sections].[structure-id] IN @StructureIds", new { StructureIds = request.RequestedUserStructures });
            }

            var template = queryBuilder.AddTemplate(Queries.List);

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            return await connection.QueryAsync<ListSectionResponse>(
                sql: template.RawSql,
                param: template.Parameters);
        }

        public async Task UpdateConstructionStageAsync(ConstructionStage constructionStage)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();

            await connection.ExecuteAsync(
                Queries.UpdateConstructionStage, 
                constructionStage.ToDbUpdateParam());
        }

        public async Task UpsertConstructionStageAsync(
            ConstructionStage constructionStage,
            SectionHistory history)
        {
            await using var connection = 
                ApplicationDatabase.GetConnection(_connectionString);
            
            await connection.OpenAsync();
            
            await using var transaction = 
                await connection
                    .BeginTransactionAsync(IsolationLevel.Serializable);

            try
            {
                await connection.ExecuteAsync(
                    Queries.UpsertConstructionStage, 
                    constructionStage.ToDbWriteParam(), 
                    transaction);
                
                await connection.ExecuteAsync(
                    Queries.InsertSectionHistory,
                    history.ToDbWriteParam(),
                    transaction);
                
                await connection.ExecuteAsync(
                    OutboxQueries.Insert,
                    new
                    {
                        Id = Guid.NewGuid(),
                        Type = typeof(CreateSliFile).AssemblyQualifiedName,
                        Data = JsonSerializer.Serialize(new
                        {
                            SectionId = history.Section.Id
                        }),
                    },
                    transaction);
                
                await transaction.CommitAsync();
            }
            catch (Exception e)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task UpsertSectionReviewAsync(
            SectionReview sectionReview,
            SectionHistory history)
        {
            await using var connection = 
                ApplicationDatabase.GetConnection(_connectionString);

            await connection.OpenAsync();

            await using var transaction = 
                await connection
                    .BeginTransactionAsync(IsolationLevel.Serializable);

            try
            {
                await connection.ExecuteAsync(
                    Queries.UpsertReview, 
                    sectionReview.ToDbWriteParam(), 
                    transaction);
                
                await connection.ExecuteAsync(
                    Queries.InsertSectionHistory,
                    history.ToDbWriteParam(),
                    transaction);
                
                await connection.ExecuteAsync(
                    OutboxQueries.Insert,
                    new
                    {
                        Id = Guid.NewGuid(),
                        Type = typeof(CreateSliFile).AssemblyQualifiedName,
                        Data = JsonSerializer.Serialize(new
                        {
                            SectionId = history.Section.Id
                        }),
                    },
                    transaction);
                
                await transaction.CommitAsync();
            }
            catch (Exception e)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task UpdateReviewAsync(
            SectionReview review)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();

            await connection.ExecuteAsync(
                Queries.UpdateReview, 
                review.ToDbUpdateParam());
        }
        
        public async Task UpdateV2Async(Domain.Entities.Section section)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync(IsolationLevel.Serializable);

            try
            {
                await connection.ExecuteAsync(
                    Queries.Update, 
                    section.ToDbWriteParam(), 
                    transaction);

                foreach (var instrument in section.Instruments)
                {
                    var instrumentParam = instrument.ToDbWriteParam(section.Id);

                    var instrumentExists = await 
                        connection.ExecuteScalarAsync<int>(
                            Queries.CheckIfInstrumentExists, 
                            instrumentParam, 
                            transaction);

                    if (instrumentExists == 0)
                    {
                        await connection.ExecuteAsync(
                            Queries.InsertInstrument, 
                            instrumentParam, 
                            transaction);
                    }
                }

                var removedInstruments = await connection
                    .QueryAsync<Guid>(Queries.GetRemovedInstruments, new
                    {
                        SectionId = section.Id,
                        Instruments = section.Instruments.Select(x => x.Id)
                    }, transaction);

                if (removedInstruments.Any())
                {
                    await connection
                        .ExecuteAsync(Queries.DeleteInstruments, new
                        {
                            SectionId = section.Id,
                            Instruments = section.Instruments.Select(x => x.Id)
                        }, transaction);

                    await connection
                        .ExecuteAsync(OutboxQueries.Insert, new
                        {
                            Id = Guid.NewGuid(),
                            Type = typeof(InstrumentsRemovedFromSection).AssemblyQualifiedName,
                            Data = JsonSerializer.Serialize(new InstrumentsRemovedFromSection
                            {
                                InstrumentIds = removedInstruments.ToList()
                            }),
                        }, transaction);
                }

                foreach (var history in section.History)
                {
                    await connection.ExecuteAsync(
                        Queries.InsertSectionHistory, 
                        history.ToDbWriteParam(), 
                        transaction);
                }
                
                await connection.ExecuteAsync(
                    Queries.UpsertStabilityAnalysisConfiguration,
                    section.StabilityAnalysisConfiguration.ToDbWriteParam(),
                    transaction);

                var circularCalculationMethods = section.StabilityAnalysisConfiguration
                    .ToCircularCalculationMethodsDbWriteParam();

                foreach (var calculationMethod in circularCalculationMethods)
                {
                    await connection.ExecuteAsync(
                        Queries.UpsertCircularCalculationMethods,
                        calculationMethod,
                        transaction);
                }

                var nonCircularCalculationMethods = section.StabilityAnalysisConfiguration
                    .ToNonCircularCalculationMethodsDbWriteParam();

                foreach (var calculationMethod in nonCircularCalculationMethods)
                {
                    await connection.ExecuteAsync(
                        Queries.UpsertNonCircularCalculationMethods,
                        calculationMethod,
                        transaction);
                }

                await connection.ExecuteAsync(
                    OutboxQueries.Insert,
                    section.ToCreateSliFileOutboxParam(),
                    transaction);

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<File> GetSectionDrawingAsync(GetSectionDrawingFileRequest request)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var builder = new SqlBuilder();

            var template = request.ConstructionStageId.HasValue
                ? builder.AddTemplate(Queries.GetConstructionStageDrawing)
                : builder.AddTemplate(Queries.GetSectionReviewDrawing);

            var param = request.ToDbQueryParam();

            return await connection.QuerySingleOrDefaultAsync<File>(
                template.RawSql,
                param);
        }

        public async Task UpdateAsync(
            Domain.Entities.Section section)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync(IsolationLevel.Serializable);

            try
            {
                await connection.ExecuteAsync(
                    Queries.Update, 
                    section.ToDbWriteParam(), 
                    transaction);

                foreach (var review in section.Reviews)
                {
                    await connection.ExecuteAsync(
                        Queries.UpsertReview, 
                        review.ToDbWriteParam(), 
                        transaction);

                    foreach (var stage in review.ConstructionStages)
                    {
                        await connection.ExecuteAsync(
                            Queries.UpsertConstructionStage, 
                            stage.ToDbWriteParam(), 
                            transaction);
                    }

                    await connection
                        .ExecuteAsync(Queries.DeleteConstructionStage, new
                        {
                            SectionReviewId = review.Id,
                            Stages = review.ConstructionStages.Select(x => x.Id)
                        }, transaction);
                }

                foreach (var instrument in section.Instruments)
                {
                    var instrumentParam = instrument.ToDbWriteParam(section.Id);

                    var instrumentExists = await 
                        connection.ExecuteScalarAsync<int>(
                            Queries.CheckIfInstrumentExists, 
                            instrumentParam, 
                            transaction);

                    if (instrumentExists == 0)
                    {
                        await connection.ExecuteAsync(
                            Queries.InsertInstrument, 
                            instrumentParam, 
                            transaction);
                    }
                }

                var removedInstruments = await connection
                    .QueryAsync<Guid>(Queries.GetRemovedInstruments, new
                    {
                        SectionId = section.Id,
                        Instruments = section.Instruments.Select(x => x.Id)
                    }, transaction);

                if (removedInstruments.Any())
                {
                    await connection
                        .ExecuteAsync(Queries.DeleteInstruments, new
                        {
                            SectionId = section.Id,
                            Instruments = section.Instruments.Select(x => x.Id)
                        }, transaction);

                    await connection
                        .ExecuteAsync(OutboxQueries.Insert, new
                        {
                            Id = Guid.NewGuid(),
                            Type = typeof(InstrumentsRemovedFromSection).AssemblyQualifiedName,
                            Data = JsonSerializer.Serialize(new InstrumentsRemovedFromSection
                            {
                                InstrumentIds = removedInstruments.ToList()
                            }),
                        }, transaction);
                }

                foreach (var history in section.History)
                {
                    await connection.ExecuteAsync(
                        Queries.InsertSectionHistory, 
                        history.ToDbWriteParam(), 
                        transaction);
                }

                await connection.ExecuteAsync(
                    OutboxQueries.Insert,
                    section.ToCreateSliFileOutboxParam(),
                    transaction);

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<IEnumerable<Domain.Entities.Section>> GetByStructureAsync(
            Guid structureId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var lookup = new Dictionary<Guid, Domain.Entities.Section>();
            var lookupStructureType = new Dictionary<Guid, Domain.Entities.StructureType>();

            await connection.QueryAsync(
                sql: Queries.GetByStructure,
                new[]
                {
                    typeof(Domain.Entities.Section),
                    typeof(SectionCoordinates),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(Domain.ValueObjects.MapLineSetting),
                    typeof(Domain.Entities.Structure),
                    typeof(Domain.Entities.Client),
                    typeof(Domain.Entities.ClientUnit),
                    typeof(Domain.Entities.StructureType),
                    typeof(SectionReview),
                    typeof(File),
                    typeof(Domain.Entities.Instrument),
                    typeof(CoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(StructureTypeActivity),
                    typeof(File),
                    typeof(Domain.Entities.User),
                    typeof(ConstructionStage),
                    typeof(File),
                    typeof(File),
                },
                (records) =>
                {
                    var sectionDb = records[0] as Domain.Entities.Section;

                    if (!lookup.TryGetValue(sectionDb.Id, out var section))
                    {
                        lookup.Add(sectionDb.Id, sectionDb);
                        section = sectionDb;
                    }

                    var sectionCoordinates = records[1] as SectionCoordinates;
                    var upstreamDecimalGeodeticCoordinate = records[2] as DecimalGeodetic;
                    var upstreamUtmCoordinate = records[3] as Utm;
                    var upstreamCoordinateSetting = records[4] as SectionCoordinateSetting;
                    var downstreamDecimalGeodeticCoordinate = records[5] as DecimalGeodetic;
                    var downstreamUtmCoordinate = records[6] as Utm;
                    var downstreamCoordinateSetting = records[7] as SectionCoordinateSetting;
                    var midpointDecimalGeodeticCoordinate = records[8] as DecimalGeodetic;
                    var midpointUtmCoordinate = records[9] as Utm;
                    var midpointCoordinateSetting = records[10] as SectionCoordinateSetting;
                    var mapLineSetting = records[11] as Domain.ValueObjects.MapLineSetting;
                    var structure = records[12] as Domain.Entities.Structure;
                    var client = records[13] as Domain.Entities.Client;
                    var clientUnit = records[14] as Domain.Entities.ClientUnit;
                    var structureType = records[15] as Domain.Entities.StructureType;
                    var dxfFile = records[17] as File;
                    var coordinateSetting = records[19] as CoordinateSetting;
                    var decimalGeodetic = records[20] as DecimalGeodetic;
                    var sliFile = records[22] as File;
                    var reviewUser = records[23] as Domain.Entities.User;
                    var constructionStage = records[24] as ConstructionStage;
                    var stageDxf = records[25] as File;
                    var stageSli = records[26] as File;

                    if (!lookupStructureType.TryGetValue(structureType.Id, out var structureTypeLookup))
                    {
                        lookupStructureType.Add(structureType.Id, structureType);
                        structureTypeLookup = structureType;
                    }

                    upstreamCoordinateSetting.CoordinateSystems = new()
                    {
                        DecimalGeodetic = upstreamDecimalGeodeticCoordinate,
                        Utm = upstreamUtmCoordinate
                    };

                    downstreamCoordinateSetting.CoordinateSystems = new()
                    {
                        DecimalGeodetic = downstreamDecimalGeodeticCoordinate,
                        Utm = downstreamUtmCoordinate
                    };

                    if (midpointCoordinateSetting != null)
                    {
                        midpointCoordinateSetting.CoordinateSystems = new()
                        {
                            DecimalGeodetic = midpointDecimalGeodeticCoordinate,
                            Utm = midpointUtmCoordinate
                        };
                    }

                    sectionCoordinates.UpstreamCoordinateSetting = upstreamCoordinateSetting;
                    sectionCoordinates.DownstreamCoordinateSetting = downstreamCoordinateSetting;
                    sectionCoordinates.MidpointCoordinateSetting = midpointCoordinateSetting;

                    section.Coordinates = sectionCoordinates;
                    section.MapLineSetting = mapLineSetting;
                    section.Client = client;
                    section.ClientUnit = clientUnit;
                    section.Structure = structure;

                    if (records[16] is SectionReview sectionReview)
                    {
                        sectionReview.Drawing = dxfFile;
                        sectionReview.Sli = sliFile;
                        sectionReview.SetStructureType(structureTypeLookup);
                        sectionReview.CreatedBy = reviewUser;

                        section.AddSectionReview(sectionReview);
                    }

                    if (records[21] is StructureTypeActivity structureTypeActivity)
                    {
                        structureTypeLookup.AddActivity(structureTypeActivity);
                    }

                    if (records[18] is Domain.Entities.Instrument instrument)
                    {
                        coordinateSetting.Systems = new()
                        {
                            DecimalGeodetic = decimalGeodetic
                        };
                        instrument.CoordinateSetting = coordinateSetting;
                        section.AddInstrument(instrument);
                    }

                    if (constructionStage != null)
                    {
                        var review = section.Reviews.FirstOrDefault(x => x.Id == constructionStage.SectionReviewId);

                        if (review != null)
                        {
                            if (!review.ConstructionStages.Any(x => x.Id == constructionStage.Id))
                            {
                                constructionStage.Drawing = stageDxf;
                                constructionStage.Sli = stageSli;

                                review.ConstructionStages.Add(constructionStage);
                            }
                        }
                    }

                    return section;
                },
                splitOn: "Datum,Latitude,Northing,Format,Latitude,Northing,Format,Latitude,Northing,Format,Color,Id,Id,Id,Id,Id,UniqueName,Id,Datum,Latitude,Id,UniqueName,Id,Id,UniqueName,UniqueName",
                param: new
                {
                    Id = structureId
                });

            return lookup.Values;
        }

        public async Task<IEnumerable<Domain.Entities.Section>> GetAsync(
            IEnumerable<Guid> ids)
        {
            var lookup = new Dictionary<Guid, Domain.Entities.Section>();
            var lookupStructureType = new Dictionary<Guid, Domain.Entities.SectionType>();

            var template = new SqlBuilder()
                .Where("[sections].[id] IN @Ids", new { Ids = ids })
                .OrderBy("[section-reviews].[start-date] DESC")
                .AddTemplate(Queries.GetById);
            
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            
            await connection.QueryAsync(
                sql: template.RawSql,
                new[]
                {
                    typeof(Domain.Entities.Section),
                    typeof(SectionCoordinates),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(Domain.ValueObjects.MapLineSetting),
                    typeof(Domain.Entities.Structure),
                    typeof(Domain.Entities.Client),
                    typeof(Domain.Entities.ClientUnit),
                    typeof(Domain.Entities.SectionType),
                    typeof(SectionReview),
                    typeof(SectionLength),
                    typeof(File),
                    typeof(Domain.Entities.Instrument),
                    typeof(CoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(Measurement),
                    typeof(SectionTypeActivity),
                    typeof(File),
                    typeof(Domain.Entities.User),
                    typeof(ConstructionStage),
                    typeof(SectionLength),
                    typeof(File),
                    typeof(File),
                    typeof(StabilityAnalysisConfiguration),
                    typeof(CircularParameters),
                    typeof(NonCircularParameters),
                    typeof(Orientation),
                    typeof(CalculationMethod),
                    typeof(CalculationMethod),
                },
                (records) => BindValues(records, lookup, lookupStructureType),
                splitOn: "Datum,Latitude,Northing,Format,Latitude,Northing,Format,Latitude,Northing,Format,Color,Id,Id,Id,Id,Id,DxfLengthInMeters,UniqueName,Id,Datum,Latitude,Northing,Id,Id,UniqueName,Id,Id,DxfLengthInMeters,UniqueName,UniqueName,Id,CircularSearchMethod,NonCircularSearchMethod,Horizontal,CalculationMethods,CalculationMethods",
                param: template.Parameters);

            return lookup.Values;
        }

        private static Domain.Entities.Section BindValues
            (object[] records,
            Dictionary<Guid, Domain.Entities.Section> lookup,
            Dictionary<Guid, Domain.Entities.SectionType> lookupSectionType)
        {
            var sectionDb = records[0] as Domain.Entities.Section;

            if (!lookup.TryGetValue(sectionDb.Id, out var section))
            {
                lookup.Add(sectionDb.Id, sectionDb);
                section = sectionDb;
            }

            var sectionCoordinates = records[1] as SectionCoordinates;
            var upstreamDecimalGeodeticCoordinate = records[2] as DecimalGeodetic;
            var upstreamUtmCoordinate = records[3] as Utm;
            var upstreamCoordinateSetting = records[4] as SectionCoordinateSetting;
            var downstreamDecimalGeodeticCoordinate = records[5] as DecimalGeodetic;
            var downstreamUtmCoordinate = records[6] as Utm;
            var downstreamCoordinateSetting = records[7] as SectionCoordinateSetting;
            var midpointDecimalGeodeticCoordinate = records[8] as DecimalGeodetic;
            var midpointUtmCoordinate = records[9] as Utm;
            var midpointCoordinateSetting = records[10] as SectionCoordinateSetting;
            var mapLineSetting = records[11] as Domain.ValueObjects.MapLineSetting;
            var structure = records[12] as Domain.Entities.Structure;
            var client = records[13] as Domain.Entities.Client;
            var clientUnit = records[14] as Domain.Entities.ClientUnit;
            var sectionType = records[15] as Domain.Entities.SectionType;
            var dxfFile = records[18] as File;
            var coordinateSetting = records[20] as CoordinateSetting;
            var decimalGeodetic = records[21] as DecimalGeodetic;
            var utm = records[22] as Utm;
            var sliFile = records[25] as File;
            var reviewUser = records[26] as Domain.Entities.User;
            var seismicCoefficient = records[34] as Orientation;

            if (!lookupSectionType.TryGetValue(sectionType?.Id ?? Guid.Empty, out var sectionTypeDb))
            {
                lookupSectionType.Add(sectionType?.Id ?? Guid.Empty, sectionType);
                sectionTypeDb = sectionType;
            }
            
            var structureType = new Domain.Entities.StructureType()
            {
                Id = sectionType.Id,
                Name = sectionType.Name,
                Active = sectionType.Active,
                CreatedDate = sectionType.CreatedDate,
                Description = sectionType.Description,
                SearchIdentifier = sectionType.SearchIdentifier,
            };

            upstreamCoordinateSetting.CoordinateSystems = new()
            {
                DecimalGeodetic = upstreamDecimalGeodeticCoordinate,
                Utm = upstreamUtmCoordinate
            };

            downstreamCoordinateSetting.CoordinateSystems = new()
            {
                DecimalGeodetic = downstreamDecimalGeodeticCoordinate,
                Utm = downstreamUtmCoordinate
            };

            if (midpointCoordinateSetting != null)
            {
                midpointCoordinateSetting.CoordinateSystems = new()
                {
                    DecimalGeodetic = midpointDecimalGeodeticCoordinate,
                    Utm = midpointUtmCoordinate
                };
            }

            sectionCoordinates.UpstreamCoordinateSetting = upstreamCoordinateSetting;
            sectionCoordinates.DownstreamCoordinateSetting = downstreamCoordinateSetting;
            sectionCoordinates.MidpointCoordinateSetting = midpointCoordinateSetting;

            section.Coordinates = sectionCoordinates;
            section.MapLineSetting = mapLineSetting;
            section.Client = client;
            section.ClientUnit = clientUnit;
            section.Structure = structure;

            if (records[16] is SectionReview sectionReview)
            {
                sectionReview.Section = section;
                sectionReview.Drawing = dxfFile;
                sectionReview.Sli = sliFile;
                sectionReview.SetSectionType(sectionTypeDb);
                sectionReview.CreatedBy = reviewUser;
                
                if (records[17] is SectionLength dxfLength)
                {
                    sectionReview.LengthData = dxfLength;
                }

                section.AddSectionReview(sectionReview);
            }

            if (records[24] is SectionTypeActivity sectionTypeActivity)
            {
                sectionTypeDb.AddActivity(sectionTypeActivity);

                structureType.AddActivity(new StructureTypeActivity
                {
                    Id = sectionTypeActivity.Id,
                    CreatedDate = sectionTypeActivity.CreatedDate,
                    SearchIdentifier =
                        sectionTypeActivity.SearchIdentifier,
                    Activity = sectionTypeActivity.Activity,
                    Index = sectionTypeActivity.Index
                });
            }

            if (records[19] is Domain.Entities.Instrument instrument)
            {
                coordinateSetting.Systems = new()
                {
                    DecimalGeodetic = decimalGeodetic,
                    Utm = utm
                };

                instrument.CoordinateSetting = coordinateSetting;

                if (records[23] is Measurement measurement)
                {
                    instrument.AddMeasurement(measurement);
                }

                section.AddInstrument(instrument);
            }

            if (records[27] is ConstructionStage constructionStage)
            {
                var review = section.Reviews.FirstOrDefault(x => x.Id == constructionStage.SectionReviewId);

                if (review != null)
                {
                    if (!review.ConstructionStages.Any(x => x.Id == constructionStage.Id))
                    {
                        constructionStage.SectionReview = review;
                        
                        if (records[28] is SectionLength lengthData)
                        {
                            constructionStage.LengthData = lengthData;
                        }
                        
                        if (records[29] is File dxf)
                        {
                            constructionStage.Drawing = dxf;
                        }

                        if (records[30] is File sli)
                        {
                            constructionStage.Sli = sli;
                        }

                        review.ConstructionStages.Add(constructionStage);
                    }
                }
            }

            if (records[31] is StabilityAnalysisConfiguration
                stabilityAnalysisConfiguration)
            {
                if (records[32] is CircularParameters circularParameters)
                {
                    stabilityAnalysisConfiguration.CircularParameters = circularParameters;
                }

                if (records[33] is NonCircularParameters nonCircularParameters)
                {
                    stabilityAnalysisConfiguration.NonCircularParameters = nonCircularParameters;
                }
                
                section.StabilityAnalysisConfiguration = stabilityAnalysisConfiguration;
                section.StabilityAnalysisConfiguration.SeismicCoefficient =
                    seismicCoefficient;
            }

            if (section.StabilityAnalysisConfiguration?.CircularParameters != null
                && records[35] is CalculationMethod circularCalculationMethod
                && !section.StabilityAnalysisConfiguration.CircularParameters.CalculationMethods.Contains(circularCalculationMethod))
            {
                section.StabilityAnalysisConfiguration.CircularParameters.CalculationMethods.Add(circularCalculationMethod);
            }
            
            if (section.StabilityAnalysisConfiguration?.NonCircularParameters != null
                && records[36] is CalculationMethod nonCircularCalculationMethod
                && !section.StabilityAnalysisConfiguration.NonCircularParameters.CalculationMethods.Contains(nonCircularCalculationMethod))
            {
                section.StabilityAnalysisConfiguration.NonCircularParameters.CalculationMethods.Add(nonCircularCalculationMethod);
            }
            
            return section;
        }

        public async Task<IEnumerable<StructureIdBySectionDto>> GetStructuresIdBySectionsAsync(
            IEnumerable<Guid> sectionsId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();

            return await connection.QueryAsync<StructureIdBySectionDto>(
                sql: Queries.GetStructuresIdBySections,
                param: new { Ids = sectionsId });
        }

        public async Task UpdateChartLineColorByIdsAsync(
            List<ChartLineColorBySectionRequest> colorsBySections)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();

            await connection.ExecuteAsync(
                sql: Queries.UpdateChartLineColorById,
                param: colorsBySections);
        }

        public async Task<List<Domain.Entities.Section>> GetAllAsync()
        {
            var template = new SqlBuilder()
                .AddTemplate(Queries.GetById);

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var lookup = new Dictionary<Guid, Domain.Entities.Section>();
            var lookupStructureType = new Dictionary<Guid, Domain.Entities.SectionType>();

            await connection.QueryAsync(
                sql: template.RawSql,
                new[]
                {
                    typeof(Domain.Entities.Section),
                    typeof(SectionCoordinates),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(Domain.ValueObjects.MapLineSetting),
                    typeof(Domain.Entities.Structure),
                    typeof(Domain.Entities.Client),
                    typeof(Domain.Entities.ClientUnit),
                    typeof(Domain.Entities.StructureType),
                    typeof(SectionReview),
                    typeof(File),
                    typeof(Domain.Entities.Instrument),
                    typeof(CoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(StructureTypeActivity),
                    typeof(File),
                    typeof(Domain.Entities.User),
                    typeof(ConstructionStage),
                    typeof(File),
                    typeof(File)
                },
                (records) => 
                {
                    var section = records[0] as Domain.Entities.Section;
                    var sectionCoordinates = records[1] as SectionCoordinates;
                    var upstreamDecimalGeodeticCoordinate = records[2] as DecimalGeodetic;
                    var upstreamUtmCoordinate = records[3] as Utm;
                    var upstreamCoordinateSetting = records[4] as SectionCoordinateSetting;
                    var downstreamDecimalGeodeticCoordinate = records[5] as DecimalGeodetic;
                    var downstreamUtmCoordinate = records[6] as Utm;
                    var downstreamCoordinateSetting = records[7] as SectionCoordinateSetting;
                    var midpointDecimalGeodeticCoordinate = records[8] as DecimalGeodetic;
                    var midpointUtmCoordinate = records[9] as Utm;
                    var midpointCoordinateSetting = records[10] as SectionCoordinateSetting;
                    var mapLineSetting = records[11] as Domain.ValueObjects.MapLineSetting;
                    var structure = records[12] as Domain.Entities.Structure;
                    var client = records[13] as Domain.Entities.Client;
                    var clientUnit = records[14] as Domain.Entities.ClientUnit;
                    var structureType = records[15] as Domain.Entities.StructureType;
                    var sectionReview = records[16] as SectionReview;
                    var reviewDrawing = records[17] as File;
                    var instrument = records[18] as Domain.Entities.Instrument;
                    var instrumentCoordinateSetting = records[19] as CoordinateSetting;
                    var instrumentDecimalGeodetic = records[20] as DecimalGeodetic;
                    var structureTypeActivity = records[21] as StructureTypeActivity;
                    var reviewSli = records[22] as File;
                    var user = records[23] as Domain.Entities.User;
                    var constructionStage = records[24] as ConstructionStage;
                    var stageDrawing = records[25] as File;
                    var stageSli = records[26] as File;

                    if (!lookup.TryGetValue(section.Id, out var sectionModel))
                    {
                        lookup.Add(section.Id, section);
                        sectionModel = section;
                        sectionModel.Reviews = new List<SectionReview>();
                        sectionModel.Instruments = new List<Domain.Entities.Instrument>();
                    }
                    
                    upstreamCoordinateSetting.CoordinateSystems = new()
                    {
                        DecimalGeodetic = upstreamDecimalGeodeticCoordinate,
                        Utm = upstreamUtmCoordinate
                    };

                    downstreamCoordinateSetting.CoordinateSystems = new()
                    {
                        DecimalGeodetic = downstreamDecimalGeodeticCoordinate,
                        Utm = downstreamUtmCoordinate
                    };

                    if (midpointCoordinateSetting != null)
                    {
                        midpointCoordinateSetting.CoordinateSystems = new()
                        {
                            DecimalGeodetic = midpointDecimalGeodeticCoordinate,
                            Utm = midpointUtmCoordinate
                        };
                    }

                    sectionCoordinates.UpstreamCoordinateSetting = upstreamCoordinateSetting;
                    sectionCoordinates.DownstreamCoordinateSetting = downstreamCoordinateSetting;
                    sectionCoordinates.MidpointCoordinateSetting = midpointCoordinateSetting;

                    section.Coordinates = sectionCoordinates;

                    if (sectionReview != null && !sectionModel.Reviews.Any(x => x.Id == sectionReview.Id))
                    {
                        sectionReview.Drawing = reviewDrawing;
                        sectionReview.ConstructionStages = new List<ConstructionStage>();
                        sectionModel.Reviews.Add(sectionReview);
                    }

                    if (constructionStage != null && sectionReview != null)
                    {
                        var existingReview = sectionModel.Reviews.FirstOrDefault(x => x.Id == sectionReview.Id);
                        if (existingReview != null && !existingReview.ConstructionStages.Any(x => x.Id == constructionStage.Id))
                        {
                            constructionStage.Drawing = stageDrawing;
                            existingReview.ConstructionStages.Add(constructionStage);
                        }
                    }

                    return sectionModel;
                },
                splitOn: "Datum,Latitude,Northing,Format,Latitude,Northing,Format,Latitude,Northing,Format,Color,Id,Id,Id,Id,Id,UniqueName,Id,Datum,Latitude,Id,UniqueName,Id,Id,UniqueName,UniqueName",
                param: template.Parameters);

            return lookup.Values.ToList();
        }

        public async Task UpdateSectionReviewLengthDataAsync(Guid reviewId, SectionLength lengthData)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            await connection.ExecuteAsync(
                Queries.UpdateSectionReviewLengthData,
                new
                {
                    ReviewId = reviewId,
                    IsConsistent = lengthData.IsConsistent,
                    DifferenceInMeters = lengthData.DifferenceInMeters,
                    CoordinatesLengthInMeters = lengthData.CoordinatesLengthInMeters,
                    DxfLengthInMeters = lengthData.DxfLengthInMeters
                });
        }

        public async Task UpdateConstructionStageLengthDataAsync(Guid stageId, SectionLength lengthData)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            await connection.ExecuteAsync(
                Queries.UpdateConstructionStageLengthData,
                new
                {
                    StageId = stageId,
                    IsConsistent = lengthData.IsConsistent,
                    DifferenceInMeters = lengthData.DifferenceInMeters,
                    CoordinatesLengthInMeters = lengthData.CoordinatesLengthInMeters,
                    DxfLengthInMeters = lengthData.DxfLengthInMeters
                });
        }

        public async Task<IEnumerable<LengthDataReportItem>> GetLengthDataReportAsync()
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            return await connection.QueryAsync<LengthDataReportItem>(Queries.GetLengthDataReport);
        }
    }
}
