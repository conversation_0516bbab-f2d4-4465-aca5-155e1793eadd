namespace Database.Repositories.Section
{
    public static class Queries
    {
        public const string Insert =
            @"
                INSERT INTO [sections] 
                (
                    [id], 
                    [structure-id], 
                    [active], 
                    [name], 
                    [is-skew], 
                    [normal-line-azimuth], 
                    [skew-line-azimuth], 
                    [datum], 
                    [upstream-latitude], 
                    [upstream-longitude], 
                    [upstream-northing], 
                    [upstream-easting], 
                    [upstream-zone-number], 
                    [upstream-zone-letter], 
                    [upstream-coordinate-format], 
                    [downstream-latitude], 
                    [downstream-longitude],
                    [downstream-northing], 
                    [downstream-easting], 
                    [downstream-zone-number], 
                    [downstream-zone-letter], 
                    [downstream-coordinate-format], 
                    [midpoint-latitude], 
                    [midpoint-longitude], 
                    [midpoint-northing], 
                    [midpoint-easting], 
                    [midpoint-zone-number], 
                    [midpoint-zone-letter], 
                    [midpoint-coordinate-format], 
                    [line-color], 
                    [line-type],
                    [line-width],
                    [minimum-drained-depth],
                    [minimum-undrained-depth],
                    [minimum-pseudo-static-depth]
                )
                VALUES 
                (
                    @Id, 
                    @StructureId, 
                    @Active, 
                    @Name, 
                    @IsSkew, 
                    @NormalLineAzimuth, 
                    @SkewLineAzimuth, 
                    @Datum, 
                    @UpstreamLatitude, 
                    @UpstreamLongitude, 
                    @UpstreamNorthing, 
                    @UpstreamEasting, 
                    @UpstreamZoneNumber,
                    @UpstreamZoneLetter,
                    @UpstreamCoordinateFormat,
                    @DownstreamLatitude, 
                    @DownstreamLongitude, 
                    @DownstreamNorthing, 
                    @DownstreamEasting, 
                    @DownstreamZoneNumber, 
                    @DownstreamZoneLetter, 
                    @DownstreamCoordinateFormat, 
                    @MidpointLatitude, 
                    @MidpointLongitude, 
                    @MidpointNorthing, 
                    @MidpointEasting, 
                    @MidpointZoneNumber, 
                    @MidpointZoneLetter, 
                    @MidpointCoordinateFormat, 
                    @LineColor, 
                    @LineType,
                    @LineWidth,
                    @MinimumDrainedDepth,
                    @MinimumUndrainedDepth,
                    @MinimumPseudoStaticDepth
                )
            ";

        public const string SearchHistory =
        @"
            SELECT 
            [sections-history].[id] AS [Id], 
            [changes] AS [Changes], 
            [sections-history].[created-date] AS [CreatedDate],
            [users].Id AS [Id],
            [users].[username] AS [Username],
            [users].[first-name] AS [Firstname],
            [users].[surname] AS [Surname]
            FROM [sections-history]
            JOIN [users] ON [users].[id] = [sections-history].[modified-by-user-id]
            JOIN [sections] ON [sections].[id] = [sections-history].[section-id]
            WHERE [sections-history].[section-id] = @SectionId
            AND (@RequestedBySuperSupport = 1 OR [sections].[structure-id] IN @RequestedUserStructures)
            ORDER BY [sections-history].[created-date] DESC
            OFFSET @Skip ROWS 
            FETCH NEXT @PageSize ROWS ONLY
        ";

        public const string CountHistory =
        @"
            SELECT COUNT(*)
            FROM [sections-history]
            JOIN [users] ON [users].[id] = [sections-history].[modified-by-user-id]
            JOIN [sections] ON [sections].[id] = [sections-history].[section-id]
            WHERE [sections-history].[section-id] = @SectionId
            AND (@RequestedBySuperSupport = 1 OR [sections].[structure-id] IN @RequestedUserStructures)
        ";

        public const string DeleteConstructionStage = @"
            DELETE [construction-stages]
            WHERE [id] NOT IN @Stages
            AND [section-review-id] = @SectionReviewId
        ";

        public const string UpsertConstructionStage =
            @"
                UPDATE [construction-stages]
                SET
                [drawing-name] = @DrawingName,
                [drawing-unique-name] = @DrawingUniqueName,
                [stage] = @Stage,
                [description] = @Description,
                [is-current-stage] = @IsCurrentStage,
                [dxf-has-waterline] = @DxfHasWaterline,
                [length-is-consistent] = @IsConsistent,
                [length-difference-in-meters] = @DifferenceInMeters,
                [coordinates-length-in-meters] = @CoordinatesLengthInMeters,
                [dxf-length-in-meters] = @DxfLengthInMeters
                WHERE [id] = @Id
                IF @@ROWCOUNT = 0
                    INSERT INTO [construction-stages] 
                    (
                        [id], 
                        [section-review-id], 
                        [stage], 
                        [description], 
                        [is-current-stage], 
                        [drawing-unique-name], 
                        [drawing-name],
                        [dxf-has-waterline],
                        [length-is-consistent],
                        [length-difference-in-meters],
                        [coordinates-length-in-meters],
                        [dxf-length-in-meters]
                    )
                    VALUES 
                    (
                        @Id, 
                        @SectionReviewId, 
                        @Stage,
                        @Description, 
                        @IsCurrentStage, 
                        @DrawingUniqueName, 
                        @DrawingName,
                        @DxfHasWaterline,
                        @IsConsistent,
                        @DifferenceInMeters,
                        @CoordinatesLengthInMeters,
                        @DxfLengthInMeters
                    )
            ";

        public const string UpsertReview =
            @"
            UPDATE [section-reviews] 
            SET
            [start-date] = @StartDate,
            [structure-type-id] = @StructureTypeId,
            [section-type-id] = @SectionTypeId,
            [drawing-unique-name] = @DrawingUniqueName,
            [drawing-name] = @DrawingName,
            [index] = @Index,
            [description] = @Description,
            [is-under-construction] = @IsUnderConstruction,
            [dxf-has-waterline] = @DxfHasWaterline,
            [length-is-consistent] = @IsConsistent,
            [length-difference-in-meters] = @DifferenceInMeters,
            [coordinates-length-in-meters] = @CoordinatesLengthInMeters,
            [dxf-length-in-meters] = @DxfLengthInMeters
            WHERE [id] = @Id
            IF @@ROWCOUNT = 0
                INSERT INTO [section-reviews] 
                (
                    [id], 
                    [section-id], 
                    [start-date], 
                    [structure-type-id], 
                    [section-type-id], 
                    [drawing-unique-name], 
                    [drawing-name],
                    [index],
                    [description],
                    [created-by],
                    [is-under-construction],
                    [dxf-has-waterline],
                    [length-is-consistent],
                    [length-difference-in-meters],
                    [coordinates-length-in-meters],
                    [dxf-length-in-meters]
                )
                VALUES 
                (
                    @Id, 
                    @SectionId, 
                    @StartDate, 
                    @StructureTypeId,   
                    @SectionTypeId,   
                    @DrawingUniqueName, 
                    @DrawingName,
                    @Index,
                    @Description,
                    @CreatedBy,
                    @IsUnderConstruction,
                    @DxfHasWaterline,
                    @IsConsistent,
                    @DifferenceInMeters,
                    @CoordinatesLengthInMeters,
                    @DxfLengthInMeters
                )
            ";

        public const string InsertInstrument =
            @"
            INSERT INTO [section-instruments] 
                (
                    [section-id], 
                    [instrument-id]
                )
                VALUES 
                (
                    @SectionId, 
                    @InstrumentId
                )
            ";

        public const string InsertSectionHistory =
            @"
                INSERT INTO [sections-history] 
                (
                    [id], 
                    [section-id], 
                    [modified-by-user-id], 
                    [changes]
                )
                VALUES 
                (
                    @Id, 
                    @SectionId, 
                    @ModifiedByUserId, 
                    @Changes
                )
            ";

        public const string InsertSectionReview =
            @"
                INSERT INTO [section-reviews] 
                (
                    id, 
                    [section-id], 
                    [start-date], 
                    [structure-type-id], 
                    [section-type-id], 
                    [drawing-unique-name], 
                    [drawing-name],
                    [index],
                    [description],
                    [created-by],
                    [is-under-construction],
                    [dxf-has-waterline],
                    [length-is-consistent],
                    [length-difference-in-meters],
                    [coordinates-length-in-meters],
                    [dxf-length-in-meters]
                )
                VALUES 
                (
                    @Id, 
                    @SectionId, 
                    @StartDate, 
                    @StructureTypeId,   
                    @SectionTypeId,   
                    @DrawingUniqueName, 
                    @DrawingName,
                    @Index,
                    @Description,
                    @CreatedBy,
                    @IsUnderConstruction,
                    @DxfHasWaterline,
                    @IsConsistent,
                    @DifferenceInMeters,
                    @CoordinatesLengthInMeters,
                    @DxfLengthInMeters
                )
            ";

        public const string InsertConstructionStage =
            @"
                INSERT INTO [construction-stages] 
                (
                    [id], 
                    [section-review-id], 
                    [stage], 
                    [description], 
                    [is-current-stage], 
                    [drawing-unique-name], 
                    [drawing-name],
                    [dxf-has-waterline],
                    [length-is-consistent],
                    [length-difference-in-meters],
                    [coordinates-length-in-meters],
                    [dxf-length-in-meters]
                )
                VALUES 
                (
                    @Id, 
                    @SectionReviewId, 
                    @Stage, 
                    @Description, 
                    @IsCurrentStage,
                    @DrawingUniqueName,
                    @DrawingName,
                    @DxfHasWaterline,
                    @IsConsistent,
                    @DifferenceInMeters,
                    @CoordinatesLengthInMeters,
                    @DxfLengthInMeters
                )
            ";

        public const string NameExists =
            @"
                SELECT TOP(1) 1 FROM [sections]
                WHERE [name] = @Name
                AND [structure-id] = @StructureId
            ";

        public const string NameExistsWithOtherId =
            @"
                SELECT TOP(1) 1 FROM [sections]
                WHERE [name] = @Name
                AND [id] != @Id
                AND [structure-id] = @StructureId
            ";

        public const string ListSectionReviews =
            @"
                SELECT
                [section-reviews].[id] AS [Id],
                [section-reviews].[index] AS [Index]
                FROM [section-reviews]
                LEFT JOIN [sections] ON [section-reviews].[section-id] = [sections].[id]
                LEFT JOIN [structure-types] ON [structure-types].[id] = [section-reviews].[structure-type-id]
                WHERE ([section-reviews].[section-id] = @SectionId)
                AND (@RequestedBySuperSupport = 1 OR [sections].[structure-id] IN @RequestedUserStructures)
                ORDER BY [section-reviews].[index] DESC
            ";

        public const string GetReviewsBySectionId =
            @"
                SELECT
                [section-reviews].[id] AS [Id],
                [section-reviews].[index] AS [Index],
                [section-reviews].[is-under-construction] AS [IsUnderConstruction],
                [section-reviews].[description] AS [Description],
                [section-reviews].[start-date] AS [StartDate],
                [section-reviews].[dxf-has-waterline] AS [DxfHasWaterline],
                [sections].[structure-id] AS [StructureId],
                [section-reviews].[drawing-unique-name] AS [UniqueName],    
                [section-reviews].[drawing-name] AS [Name],
                [section-reviews].[unique-name-sli-file] AS [UniqueName],
                [section-reviews].[name-sli-file] AS [Name],
                [construction-stages].[id] AS [Id],
                [construction-stages].[stage] AS [Stage],
                [construction-stages].[dxf-has-waterline] AS [DxfHasWaterline],
                [construction-stages].[is-current-stage] AS [IsCurrentStage],
                [construction-stages].[drawing-unique-name] AS [UniqueName],
                [construction-stages].[drawing-name] AS [Name],
                [construction-stages].[sli-file-unique-name] AS [UniqueName],
                [construction-stages].[sli-file-name] AS [Name]
                FROM [section-reviews]
                JOIN [sections] ON [sections].[id] = [section-reviews].[section-id]
                LEFT JOIN [construction-stages] ON [construction-stages].[section-review-id] = [section-reviews].[id]
                WHERE [section-reviews].[section-id] = @SectionId
                ORDER BY [section-reviews].[start-date] DESC
            ";

        public const string GetReviewById =
            @"
                SELECT
                [section-reviews].[id] AS [Id],
                [section-reviews].[index] AS [Index],
                [section-reviews].[description] AS [Description],
                [section-reviews].[is-under-construction] AS [IsUnderConstruction],
                [section-reviews].[start-date] AS [StartDate],
                [section-reviews].[dxf-has-waterline] AS [DxfHasWaterline],
                [section-reviews].[drawing-unique-name] AS [UniqueName],
                [section-reviews].[drawing-name] AS [Name],
                [section-reviews].[unique-name-sli-file] AS [UniqueName],
                [section-reviews].[name-sli-file] AS [Name],
                [structure-types].[id] AS [Id],
                [structure-types].[name] AS [Name],
                [users].[id] AS [Id],
                [users].[username] AS [Username],
                [users].[first-name] AS [FirstName],
                [users].[surname] AS [Surname],
                [construction-stages].[id] AS [Id],
                [construction-stages].[stage] AS [Stage],
                [construction-stages].[dxf-has-waterline] AS [DxfHasWaterline],
                [construction-stages].[description] AS [Description],
                [construction-stages].[is-current-stage] AS [IsCurrentStage],
                [construction-stages].[section-review-id] AS [SectionReviewId],
                [construction-stages].[drawing-unique-name] AS [UniqueName],
                [construction-stages].[drawing-name] AS [Name],
                [construction-stages].[sli-file-unique-name] AS [UniqueName],
                [construction-stages].[sli-file-name] AS [Name]
                FROM [section-reviews]
                LEFT JOIN [structure-types] ON [structure-types].[id] = [section-reviews].[structure-type-id]
                LEFT JOIN [users] ON [users].[id] = [section-reviews].[created-by]
                LEFT JOIN [construction-stages] ON [construction-stages].[section-review-id] = [section-reviews].[id]
                WHERE ([section-reviews].[id] = @Id)
            ";

        public const string UpdateConstructionStage =
            @"
                UPDATE [construction-stages]
                SET
                [drawing-name] = @DrawingName,
                [drawing-unique-name] = @DrawingUniqueName,
                [stage] = @Stage,
                [description] = @Description,
                [is-current-stage] = @IsCurrentStage,
                [sli-file-name] = @SliName,
                [sli-file-unique-name] = @SliUniqueName
                WHERE [id] = @Id
            ";

        public const string UpdateReview =
            @"
                UPDATE [section-reviews] 
                SET
                [start-date] = @StartDate,
                [structure-type-id] = @StructureTypeId,
                [section-type-id] = @SectionTypeId,
                [drawing-unique-name] = @DrawingUniqueName,
                [drawing-name] = @DrawingName,
                [index] = @Index,
                [name-sli-file] = @SliName,
                [unique-name-sli-file] = @SliUniqueName
                WHERE [id] = @Id
            ";

        public const string GetById =
            @"
            SELECT 
                [sections].[id] AS [Id], 
                [sections].[search-identifier] AS [SearchIdentifier], 
                [sections].[active] AS [Active], 
                [sections].[name] AS [Name], 
                [sections].[created-date] AS [CreatedDate],
                [sections].[minimum-drained-depth] AS [MinimumDrainedDepth],
                [sections].[minimum-undrained-depth] AS [MinimumUndrainedDepth],
                [sections].[minimum-pseudo-static-depth] AS [MinimumPseudoStaticDepth],
                [is-skew] AS [IsSkew],
                [normal-line-azimuth] AS [NormalLineAzimuth], 
                [skew-line-azimuth] AS [SkewLineAzimuth], 
                [sections].[datum] AS [Datum], -- Cut
                [upstream-latitude] AS [Latitude], -- Cut
                [upstream-longitude] AS [Longitude], 
                [upstream-northing] AS [Northing], -- Cut
                [upstream-easting] AS [Easting], 
                [upstream-zone-number] AS [ZoneNumber], 
                [upstream-zone-letter] AS [ZoneLetter], 
                [upstream-coordinate-format] AS [Format], -- Cut
                [downstream-latitude] AS [Latitude], -- Cut
                [downstream-longitude] AS [Longitude], 
                [downstream-northing] AS [Northing], -- Cut
                [downstream-easting] AS [Easting], 
                [downstream-zone-number] AS [ZoneNumber], 
                [downstream-zone-letter] AS [ZoneLetter], 
                [downstream-coordinate-format] AS [Format], -- Cut
                [midpoint-latitude] AS [Latitude],-- Cut
                [midpoint-longitude] AS [Longitude],
                [midpoint-northing] AS [Northing], -- Cut
                [midpoint-easting] AS [Easting],
                [midpoint-zone-number] AS [ZoneNumber],
                [midpoint-zone-letter] AS [ZoneLetter],
                [midpoint-coordinate-format] AS [Format], -- Cut
                [line-color] AS [Color], -- Cut
                [line-type] AS [Type], 
                [line-width] AS [Width], 
                [structures].[id] AS [Id], -- Cut
                [structures].[name] AS [Name],
                [structures].[client-unit-id] AS [ClientUnitId],
                [clients].[id] AS [Id], -- Cut
                [clients].[name] AS [Name],
                [clients].[active] AS [Active],
                [client-units].[id] AS [Id], -- Cut
                [client-units].[name] AS [Name],
                [client-units].[active] AS [Active],
                [client-units].[client-id] AS [ClientId],
                [section-types].[id] AS [Id], -- Cut
                [section-types].[name] AS [Name],
                [section-reviews].[id] AS [Id], -- Cut
                [section-reviews].[index] AS [Index],
                [section-reviews].[description] AS [Description],
                [section-reviews].[dxf-has-waterline] AS [DxfHasWaterline],
                [section-reviews].[is-under-construction] AS [IsUnderConstruction],
                [section-reviews].[start-date] AS [StartDate],
                [section-reviews].[dxf-length-in-meters] AS [DxfLengthInMeters], -- Cut
                [section-reviews].[coordinates-length-in-meters] AS [CoordinatesLengthInMeters],
                [section-reviews].[length-difference-in-meters] AS [DifferenceInMeters],
                [section-reviews].[length-is-consistent] AS [IsConsistent],
                [section-reviews].[drawing-unique-name] AS [UniqueName],-- Cut
                [section-reviews].[drawing-name] AS [Name],
                [instruments].[id] AS [Id],-- Cut
                [instruments].[type] AS [Type],
                [instruments].[top-quota] AS [TopQuota],
                [instruments].[base-quota] AS [BaseQuota],
                [instruments].[elevation] AS [Elevation],
                [instruments].[linimetric-ruler-position] AS [LinimetricRulerPosition],
                [instruments].[dry-type] AS [DryType],
                [instruments].[base-quota] AS [BaseQuota],
                [instruments].[online] AS [Online],
                [instruments].[identifier] AS [Identifier],
                [instruments].[datum] AS [Datum], -- Cut
                [instruments].[coordinate-format] AS [Format],
                [instruments].[latitude] AS [Latitude], -- Cut
                [instruments].[longitude] AS [Longitude],
                [instruments].[northing] AS [Northing], -- Cut
                [instruments].[easting] AS [Easting],
                [instruments].[zone-number] AS [ZoneNumber],
                [instruments].[zone-letter] AS [ZoneLetter],
                [measurements].[id] AS [Id], -- Cut
                [measurements].[identifier] AS [Identifier],
                [measurements].[quota] AS [Quota],
                [measurements].[active] AS [Active],
                [section-type-activities].[Id] AS [Id], -- Cut
                [section-type-activities].[Activity] AS [Activity],
                [section-type-activities].[Index] AS [Index],
                [section-reviews].[unique-name-sli-file] AS [UniqueName], -- Cut
                [section-reviews].[name-sli-file] AS [Name],
                [users].[id] AS [Id], -- Cut
                [users].[username] AS [Username],
                [users].[first-name] AS [FirstName],
                [users].[surname] AS [Surname],
                [construction-stages].[id] AS [Id], -- Cut
                [construction-stages].[stage] AS [Stage],
                [construction-stages].[dxf-has-waterline] AS [DxfHasWaterline],
                [construction-stages].[description] AS [Description],
                [construction-stages].[is-current-stage] AS [IsCurrentStage],
                [construction-stages].[section-review-id] AS [SectionReviewId],
                [construction-stages].[dxf-length-in-meters] AS [DxfLengthInMeters], -- Cut
                [construction-stages].[coordinates-length-in-meters] AS [CoordinatesLengthInMeters],
                [construction-stages].[length-difference-in-meters] AS [DifferenceInMeters],
                [construction-stages].[length-is-consistent] AS [IsConsistent],
                [construction-stages].[drawing-unique-name] AS [UniqueName], -- Cut
                [construction-stages].[drawing-name] AS [Name],
                [construction-stages].[sli-file-unique-name] AS [UniqueName], -- Cut
                [construction-stages].[sli-file-name] AS [Name],
                [stability-configurations].id AS Id, -- Cut
                [stability-configurations].[search-identifier] AS SearchIdentifier,
                [stability-configurations].[created-date] AS CreatedDate,
                [stability-configurations].[section-id] AS SectionId,
                [stability-configurations].[should-evaluate-drained-condition] AS ShouldEvaluateDrainedCondition,
                [stability-configurations].[should-evaluate-undrained-condition] AS ShouldEvaluateUndrainedCondition,
                [stability-configurations].[should-evaluate-pseudo-static-condition] AS ShouldEvaluatePseudoStaticCondition,
                [stability-configurations].[minimum-drained-depth] AS MinimumDrainedDepth,
                [stability-configurations].[minimum-undrained-depth] AS MinimumUndrainedDepth,
                [stability-configurations].[minimum-pseudo-static-depth] AS MinimumPseudoStaticDepth,
                [stability-configurations].gravity AS Gravity,
                [stability-configurations].[direction] AS Direction,
                [stability-configurations].[circular-search-method] AS CircularSearchMethod, -- Cut
                [stability-configurations].[circular-divisions-along-slope] AS DivisionsAlongSlope,
                [stability-configurations].[circles-per-division] AS CirclesPerDivision,
                [stability-configurations].[circular-number-of-iterations] AS NumberOfIterations,
                [stability-configurations].[circular-divisions-next-iteration] AS DivisionsNextIteration,
                [stability-configurations].[radius-increment] AS RadiusIncrement,
                [stability-configurations].[circular-number-of-surfaces] AS NumberOfSurfaces,
                [stability-configurations].[non-circular-search-method] AS NonCircularSearchMethod,-- Cut
                [stability-configurations].[non-circular-divisions-along-slope] AS DivisionsAlongSlope,
                [stability-configurations].[surfaces-per-division] AS SurfacesPerDivision,
                [stability-configurations].[non-circular-number-of-iterations] AS NumberOfIterations,
                [stability-configurations].[non-circular-divisions-next-iteration] AS DivisionsNextIteration,
                [stability-configurations].[number-of-vertices-along-surface] AS NumberOfVerticesAlongSurface,
                [stability-configurations].[non-circular-number-of-surfaces] AS NumberOfSurfaces,
                [stability-configurations].[number-of-nests] AS NumberOfNests,
                [stability-configurations].[maximum-iterations] AS MaximumIterations,
                [stability-configurations].[initial-number-of-surface-vertices] AS InitialNumberOfSurfaceVertices,
                [stability-configurations].[initial-number-of-iterations] AS InitialNumberOfIterations,
                [stability-configurations].[maximum-number-of-steps] AS MaximumNumberOfSteps,
                [stability-configurations].[number-of-factors-safety-compared-before-stopping] AS NumberOfFactorsSafetyComparedBeforeStopping,
                [stability-configurations].[tolerance-for-stopping-criterion] AS ToleranceForStoppingCriterion,
                [stability-configurations].[number-of-particles] AS NumberOfParticles,
                [stability-configurations].[seismic-coefficient-horizontal] AS Horizontal, -- Cut
                [stability-configurations].[seismic-coefficient-vertical] AS Vertical,
                [circular-calculation-methods].[calculation-method] AS [CalculationMethods], -- Cut
                [non-circular-calculation-methods].[calculation-method] AS [CalculationMethods] -- Cut
            FROM [sections]
                JOIN [structures] ON [structures].[id] = [sections].[structure-id]
                JOIN [client-units] ON [client-units].[id] = [structures].[client-unit-id]
                JOIN [clients] ON [clients].[id] = [client-units].[client-id]
                LEFT JOIN [section-reviews] ON [section-reviews].[section-id] = [sections].[id]
                LEFT JOIN [structure-types] ON [structure-types].[id] = [section-reviews].[structure-type-id]
                LEFT JOIN [section-types] ON [section-types].[id] = [section-reviews].[section-type-id]
                LEFT JOIN [structure-type-activities] ON [structure-type-activities].[structure-type-id] = [structure-types].[id]
                LEFT JOIN [section-type-activities] ON [section-type-activities].[section-type-id] = [section-types].[id]
                LEFT JOIN [section-instruments] ON [section-instruments].[section-id] = [sections].[id]
                LEFT JOIN [instruments] ON [instruments].[id] = [section-instruments].[instrument-id]
                LEFT JOIN [measurements] ON [measurements].[instrument-id] = [instruments].[id]
                LEFT JOIN [users] ON [users].[id] = [section-reviews].[created-by]
                LEFT JOIN [construction-stages] ON [construction-stages].[section-review-id] = [section-reviews].[id]
                LEFT JOIN [stability-configurations] ON [sections].[id] = [stability-configurations].[section-id]
                LEFT JOIN [circular-calculation-methods] ON [circular-calculation-methods].[section-id] = [sections].[id]
                LEFT JOIN [non-circular-calculation-methods] ON [non-circular-calculation-methods].[section-id] = [sections].[id]
            /**where**/
            /**orderby**/
           ";

        public const string GetDashboardSectionMap =
            @"
                SELECT 
                [structures].[id] AS [StructureId],
                [structures].[datum] AS [Datum],
                [structures].[latitude] AS [Latitude],
                [structures].[longitude] AS [Longitude],
                [sections].[id] AS [Id], 
                [sections].[name] AS [Name], 
                [sections].[datum] AS [Datum], 
                [sections].[structure-id] AS [StructureId], 
                [upstream-latitude] AS [Latitude], 
                [upstream-longitude] AS [Longitude], 
                [downstream-latitude] AS [Latitude], 
                [downstream-longitude] AS [Longitude], 
                [midpoint-latitude] AS [Latitude],
                [midpoint-longitude] AS [Longitude],
                [line-color] AS [Color], 
                [line-type] AS [Type], 
                [line-width] AS [Width],
                [instruments].[id] AS [Id],
                [section-instruments].[section-id] AS [SectionId],
                [instruments].[identifier] AS [Identifier],
                [instruments].[type] AS [Type],
                [instruments].[online] AS [Online],
                [instruments].[datum] AS [Datum],
                [instruments].[latitude] AS [Latitude],
                [instruments].[longitude] AS [Longitude]
                FROM [sections]
                INNER JOIN [structures] ON [structures].[id] = [sections].[structure-id]
                LEFT JOIN [section-instruments] ON [section-instruments].[section-id] = [sections].[id]
                LEFT JOIN [instruments] ON [instruments].[id] = [section-instruments].[instrument-id] AND [instruments].[online] = 1
                WHERE [structures].[id] = @StructureId
            ";

        public const string GetByFiltersMaps =
            @"
            SELECT 
            [sections].[id] AS [Id], 
            [sections].[name] AS [Name], 
            [sections].[datum] AS [Datum], 
            [upstream-latitude] AS [Latitude], 
            [upstream-longitude] AS [Longitude], 
            [upstream-northing] AS [Northing], 
            [upstream-easting] AS [Easting], 
            [upstream-zone-number] AS [ZoneNumber], 
            [upstream-zone-letter] AS [ZoneLetter], 
            [upstream-coordinate-format] AS [Format], 
            [downstream-latitude] AS [Latitude], 
            [downstream-longitude] AS [Longitude], 
            [downstream-northing] AS [Northing], 
            [downstream-easting] AS [Easting], 
            [downstream-zone-number] AS [ZoneNumber], 
            [downstream-zone-letter] AS [ZoneLetter], 
            [downstream-coordinate-format] AS [Format], 
            [midpoint-latitude] AS [Latitude],
            [midpoint-longitude] AS [Longitude],
            [midpoint-northing] AS [Northing], 
            [midpoint-easting] AS [Easting],
            [midpoint-zone-number] AS [ZoneNumber],
            [midpoint-zone-letter] AS [ZoneLetter],
            [midpoint-coordinate-format] AS [Format],
            [line-color] AS [Color], 
            [line-type] AS [Type], 
            [line-width] AS [Width] 
            FROM [sections]
            WHERE [sections].[structure-id] IN @StructureIds
            ";

        public const string GetByStructureId =
            @"
            SELECT 
            [sections].[id] AS [Id]
            FROM [sections]
            WHERE [sections].[structure-id] = @StructureId
        ";

        public const string List =
            @"
            SELECT 
                [sections].[id] AS [Id],
                [sections].[name] AS [Name]
            FROM [sections]
                /**join**/
            /**where**/ 
            ORDER BY [sections].[name] ASC
       ";

        public const string UpdateOnlyActive =
            @"UPDATE [sections]
             SET [active] = @Active
             WHERE [id] = @Id";

        public const string Search =
            @"SELECT 
            [sections].[id] AS [Id],
            [sections].[search-identifier] AS [SearchIdentifier],
            [sections].[active] AS [Active],
            [sections].[name] AS [Name],
            [sections].[normal-line-azimuth] AS [NormalLineAzimuth],
            [sections].[skew-line-azimuth] AS [SkewLineAzimuth],
            [client-units].[id] AS [Id],
            [client-units].[name] AS [Name],
            [client-units].[active] AS [Active],
            [client-units].[client-id] AS [ClientId],
            [structures].[id] AS [Id],
            [structures].[name] AS [Name],
            [structures].[client-unit-id] AS [ClientUnitId],
            (SELECT count(*) FROM [section-instruments] INNER JOIN [instruments] ON [instruments].[id] = [section-instruments].[instrument-id] 
            WHERE [section-instruments].[section-id] = [sections].[id] AND [instruments].[online] = 1 AND ([instruments].[type] = @ElectricPiezometerType OR [instruments].[type] = @OpenStandpipePiezometerType)) AS [ActivePzCounter],
            (SELECT count(*) FROM [section-instruments] INNER JOIN [instruments] ON [instruments].[id] = [section-instruments].[instrument-id] 
            WHERE [section-instruments].[section-id] = [sections].[id] AND [instruments].[online] = 1 AND [instruments].[type] = @WaterLevelIndicatorType)  AS [ActiveWliCounter],
            (SELECT count(*) FROM [section-reviews] WHERE [section-id] = [sections].[id]) AS [ReviewsCounter] 
            FROM [sections]
                JOIN [structures] ON [structures].[id] = [sections].[structure-id]
                JOIN [client-units] ON [client-units].[id] = [structures].[client-unit-id]
            WHERE (@ClientUnitId IS NULL OR [structures].[client-unit-id] = @ClientUnitId)
                AND (@ClientId IS NULL OR [client-units].[client-id] = @ClientId)
                AND (@StructuresCount IS NULL OR @StructuresCount = 0 OR [sections].[structure-id] IN @StructureIds)
                AND (@SectionsCount IS NULL OR @SectionsCount = 0 OR [sections].[id] IN @SectionIds)
                AND (@Active IS NULL OR [sections].[active] = @Active)
                AND (@SearchIdentifier IS NULL OR [sections].[search-identifier] = @SearchIdentifier)
                AND (@RequestedBySuperSupport = 1 OR [sections].[structure-id] IN @RequestedUserStructures)
            ORDER BY [structures].[name], [sections].[name]
            OFFSET @Skip ROWS
            FETCH NEXT @PageSize ROWS ONLY";

        public const string Count =
            @"SELECT count(*)
            FROM [sections]
                JOIN [structures] ON [structures].[id] = [sections].[structure-id]
                JOIN [client-units] ON [client-units].[id] = [structures].[client-unit-id]
            WHERE (@ClientUnitId IS NULL OR [structures].[client-unit-id] = @ClientUnitId)
                AND (@ClientId IS NULL OR [client-units].[client-id] = @ClientId)
                AND (@StructuresCount IS NULL OR @StructuresCount = 0 OR [sections].[structure-id] IN @StructureIds)
                AND (@SectionsCount IS NULL OR @SectionsCount = 0 OR [sections].[id] IN @SectionIds)
                AND (@Active IS NULL OR [sections].[active] = @Active)
                AND (@SearchIdentifier IS NULL OR [sections].[search-identifier] = @SearchIdentifier)
                AND (@RequestedBySuperSupport = 1 OR [sections].[structure-id] IN @RequestedUserStructures)
            ";

        public const string Update =
            @"
            UPDATE [sections]
            SET [name] = @Name,
                [is-skew] = @IsSkew,
                [normal-line-azimuth] = @NormalLineAzimuth,
                [skew-line-azimuth] = @SkewLineAzimuth,
                [datum] = @Datum,
                [upstream-latitude] = @UpstreamLatitude,
                [upstream-longitude] = @UpstreamLongitude,
                [upstream-northing] = @UpstreamNorthing,
                [upstream-easting] = @UpstreamEasting,
                [upstream-zone-number] = @UpstreamZoneNumber,
                [upstream-zone-letter] = @UpstreamZoneLetter,
                [upstream-coordinate-format] = @UpstreamCoordinateFormat,
                [downstream-latitude] = @DownstreamLatitude,
                [downstream-longitude] = @DownstreamLongitude,
                [downstream-northing] = @DownstreamNorthing,
                [downstream-easting] = @DownstreamEasting,
                [downstream-zone-number] = @DownstreamZoneNumber,
                [downstream-zone-letter] = @DownstreamZoneLetter,
                [downstream-coordinate-format] = @DownstreamCoordinateFormat,
                [midpoint-latitude] = @MidpointLatitude,
                [midpoint-longitude] = @MidpointLongitude,
                [midpoint-northing] = @MidpointNorthing,
                [midpoint-easting] = @MidpointEasting,
                [midpoint-zone-number] = @MidpointZoneNumber,
                [midpoint-zone-letter] = @MidpointZoneLetter,
                [midpoint-coordinate-format] = @MidpointCoordinateFormat,
                [line-color] = @LineColor,
                [line-type] = @LineType,
                [line-width] = @LineWidth,
                [structure-id] = @StructureId,
                [minimum-drained-depth] = @MinimumDrainedDepth,
                [minimum-undrained-depth] = @MinimumUndrainedDepth,
                [minimum-pseudo-static-depth] = @MinimumPseudoStaticDepth
            WHERE [id] = @Id";

        public const string CheckIfInstrumentExists =
            @"
                SELECT TOP(1) 1 FROM [section-instruments]
                WHERE [section-id] = @SectionId
                AND [instrument-id] = @InstrumentId
            ";

        public const string GetRemovedInstruments =
            @"  
                SELECT [instrument-id] FROM [section-instruments]
                WHERE [section-id] = @SectionId
                AND [instrument-id] NOT IN @Instruments
            ";

        public const string DeleteInstruments =
            @"
                DELETE FROM [section-instruments]
                WHERE [section-id] = @SectionId
                AND [instrument-id] NOT IN @Instruments
            ";

        public const string GetByStructure =
            @"SELECT 
            [sections].[id] AS [Id], 
            [sections].[search-identifier] AS [SearchIdentifier], 
            [sections].[active] AS [Active], 
            [sections].[name] AS [Name], 
            [sections].[created-date] AS [CreatedDate],
            [sections].[minimum-drained-depth] AS [MinimumDrainedDepth],
            [sections].[minimum-undrained-depth] AS [MinimumUndrainedDepth],
            [sections].[minimum-pseudo-static-depth] AS [MinimumPseudoStaticDepth],
            [is-skew] AS [IsSkew], 
            [normal-line-azimuth] AS [NormalLineAzimuth], 
            [skew-line-azimuth] AS [SkewLineAzimuth], 
            [sections].[datum] AS [Datum], 
            [upstream-latitude] AS [Latitude], 
            [upstream-longitude] AS [Longitude], 
            [upstream-northing] AS [Northing], 
            [upstream-easting] AS [Easting], 
            [upstream-zone-number] AS [ZoneNumber], 
            [upstream-zone-letter] AS [ZoneLetter], 
            [upstream-coordinate-format] AS [Format], 
            [downstream-latitude] AS [Latitude], 
            [downstream-longitude] AS [Longitude], 
            [downstream-northing] AS [Northing], 
            [downstream-easting] AS [Easting], 
            [downstream-zone-number] AS [ZoneNumber], 
            [downstream-zone-letter] AS [ZoneLetter], 
            [downstream-coordinate-format] AS [Format], 
            [midpoint-latitude] AS [Latitude],
            [midpoint-longitude] AS [Longitude],
            [midpoint-northing] AS [Northing], 
            [midpoint-easting] AS [Easting],
            [midpoint-zone-number] AS [ZoneNumber],
            [midpoint-zone-letter] AS [ZoneLetter],
            [midpoint-coordinate-format] AS [Format],
            [line-color] AS [Color], 
            [line-type] AS [Type], 
            [line-width] AS [Width], 
            [structures].[id] AS [Id],
            [structures].[name] AS [Name],
            [structures].[client-unit-id] AS [ClientUnitId],
            [clients].[id] AS [Id],
            [clients].[name] AS [Name],
            [clients].[active] AS [Active],
            [client-units].[id] AS [Id],
            [client-units].[name] AS [Name],
            [client-units].[active] AS [Active],
            [client-units].[client-id] AS [ClientId],
            [structure-types].[id] AS [Id],
            [structure-types].[name] AS [Name],
            [section-reviews].[id] AS [Id],
            [section-reviews].[index] AS [Index],
            [section-reviews].[dxf-has-waterline] AS [DxfHasWaterline],
            [section-reviews].[is-under-construction] AS [IsUnderConstruction],
            [section-reviews].[description] AS [Description],
            [section-reviews].[start-date] AS [StartDate],
            [section-reviews].[drawing-unique-name] AS [UniqueName],
            [section-reviews].[drawing-name] AS [Name],
            [instruments].[id] AS [Id],
            [instruments].[type] AS [Type],
            [instruments].[online] AS [Online],
            [instruments].[identifier] AS [Identifier],
            [instruments].[datum] AS [Datum],
            [instruments].[latitude] AS [Latitude],
            [instruments].[longitude] AS [Longitude],
            [structure-type-activities].[Id] AS [Id],
            [structure-type-activities].[Activity] AS [Activity],
            [structure-type-activities].[Index] AS [Index],
            [section-reviews].[unique-name-sli-file] AS [UniqueName],
            [section-reviews].[name-sli-file] AS [Name],
            [users].[id] AS [Id],
            [users].[username] AS [Username],
            [users].[first-name] AS [FirstName],
            [users].[surname] AS [Surname],
            [construction-stages].[id] AS [Id],
            [construction-stages].[stage] AS [Stage],
            [construction-stages].[dxf-has-waterline] AS [DxfHasWaterline],
            [construction-stages].[description] AS [Description],
            [construction-stages].[is-current-stage] AS [IsCurrentStage],
            [construction-stages].[section-review-id] AS [SectionReviewId],
            [construction-stages].[drawing-unique-name] AS [UniqueName],
            [construction-stages].[drawing-name] AS [Name],
            [construction-stages].[sli-file-unique-name] AS [UniqueName],
            [construction-stages].[sli-file-name] AS [Name]
            FROM [sections]
            LEFT JOIN [section-reviews] ON [section-reviews].[section-id] = [sections].[id]
            LEFT JOIN [structure-types] ON [structure-types].[id] = [section-reviews].[structure-type-id]
            LEFT JOIN [structure-type-activities] ON [structure-type-activities].[structure-type-id] = [structure-types].[id]
            JOIN [structures] ON [structures].[id] = [sections].[structure-id]
            JOIN [client-units] ON [client-units].[id] = [structures].[client-unit-id]
            JOIN [clients] ON [clients].[id] = [client-units].[client-id]
            LEFT JOIN [section-instruments] ON [section-instruments].[section-id] = [sections].[id]
            LEFT JOIN [instruments] ON [instruments].[id] = [section-instruments].[instrument-id]
            LEFT JOIN [users] ON [users].[id] = [section-reviews].[created-by]
            LEFT JOIN [construction-stages] ON [construction-stages].[section-review-id] = [section-reviews].[id]
            WHERE [sections].[structure-id] = @Id";

        public const string GetStructuresIdBySections =
            @"
                SELECT 
                    [id] AS 'Id',
                    [structure-id] AS 'StructureId'
                FROM [sections]
                WHERE [sections].[id] IN @Ids
            ";

        public const string UpdateChartLineColorById =
            @"
                UPDATE sections	
                    SET [chart-line-color] = @ChartLineColor
                WHERE id = @Id
            ";

        public const string GetDashboardLatest = @"
            SELECT TOP 1
                CONCAT(sections.name, '-', [latest-review].[index]) AS Title,
                [latest-review].[created-date] AS Date,
                4 AS type
            FROM sections
                OUTER APPLY
                 (
                     SELECT TOP 1
                         [section-reviews].[index],
                         [section-reviews].[created-date]
                     FROM [section-reviews]
                     WHERE [section-reviews].[section-id] = sections.id
                     ORDER BY [section-reviews].[index] DESC
                 ) AS [latest-review]
            WHERE sections.[structure-id] = @StructureId
              AND sections.active = 1
            ORDER BY [latest-review].[index] DESC";

        public const string InsertStabilityAnalysisConfiguration = @"
            INSERT INTO [stability-configurations] (
                [id],
                [created-date],
                [section-id],
                [should-evaluate-drained-condition],
                [should-evaluate-undrained-condition],
                [should-evaluate-pseudo-static-condition],
                [seismic-coefficient-horizontal],
                [seismic-coefficient-vertical],
                [minimum-drained-depth],
                [minimum-undrained-depth],
                [minimum-pseudo-static-depth],
                [gravity],
                [circular-search-method],
                [circular-divisions-along-slope],
                [circles-per-division],
                [circular-number-of-iterations],
                [circular-divisions-next-iteration],
                [radius-increment],
                [circular-number-of-surfaces],
                [non-circular-search-method],
                [non-circular-divisions-along-slope],
                [surfaces-per-division],
                [non-circular-number-of-iterations],
                [non-circular-divisions-next-iteration],
                [number-of-vertices-along-surface],
                [non-circular-number-of-surfaces],
                [number-of-nests],
                [maximum-iterations],
                [initial-number-of-surface-vertices],
                [initial-number-of-iterations],
                [maximum-number-of-steps],
                [number-of-factors-safety-compared-before-stopping],
                [tolerance-for-stopping-criterion],
                [number-of-particles],
                [direction]
            ) VALUES (
                @Id,
                @CreatedDate,
                @SectionId,
                @ShouldEvaluateDrainedCondition,
                @ShouldEvaluateUndrainedCondition,
                @ShouldEvaluatePseudoStaticCondition,
                @SeismicCoefficientHorizontal,
                @SeismicCoefficientVertical,
                @MinimumDrainedDepth,
                @MinimumUndrainedDepth,
                @MinimumPseudoStaticDepth,
                @Gravity,
                @CircularSearchMethod,
                @CircularDivisionsAlongSlope,
                @CirclesPerDivision,
                @CircularNumberOfIterations,
                @CircularDivisionsNextIteration,
                @RadiusIncrement,
                @CircularNumberOfSurfaces,
                @NonCircularSearchMethod,
                @NonCircularDivisionsAlongSlope,
                @SurfacesPerDivision,
                @NonCircularNumberOfIterations,
                @NonCircularDivisionsNextIteration,
                @NumberOfVerticesAlongSurface,
                @NonCircularNumberOfSurfaces,
                @NumberOfNests,
                @MaximumIterations,
                @InitialNumberOfSurfaceVertices,
                @InitialNumberOfIterations,
                @MaximumNumberOfSteps,
                @NumberOfFactorsSafetyComparedBeforeStopping,
                @ToleranceForStoppingCriterion,
                @NumberOfParticles,
                @Direction
            );
            ";

        public const string UpsertStabilityAnalysisConfiguration = @"
            UPDATE [stability-configurations] 
            SET
                [created-date] = @CreatedDate,
                [section-id] = @SectionId,
                [should-evaluate-drained-condition] = @ShouldEvaluateDrainedCondition,
                [should-evaluate-undrained-condition] = @ShouldEvaluateUndrainedCondition,
                [should-evaluate-pseudo-static-condition] = @ShouldEvaluatePseudoStaticCondition,
                [seismic-coefficient-horizontal] = @SeismicCoefficientHorizontal,
                [seismic-coefficient-vertical] = @SeismicCoefficientVertical,
                [minimum-drained-depth] = @MinimumDrainedDepth,
                [minimum-undrained-depth] = @MinimumUndrainedDepth,
                [minimum-pseudo-static-depth] = @MinimumPseudoStaticDepth,
                [gravity] = @Gravity,
                [circular-search-method] = @CircularSearchMethod,
                [circular-divisions-along-slope] = @CircularDivisionsAlongSlope,
                [circles-per-division] = @CirclesPerDivision,
                [circular-number-of-iterations] = @CircularNumberOfIterations,
                [circular-divisions-next-iteration] = @CircularDivisionsNextIteration,
                [radius-increment] = @RadiusIncrement,
                [circular-number-of-surfaces] = @CircularNumberOfSurfaces,
                [non-circular-search-method] = @NonCircularSearchMethod,
                [non-circular-divisions-along-slope] = @NonCircularDivisionsAlongSlope,
                [surfaces-per-division] = @SurfacesPerDivision,
                [non-circular-number-of-iterations] = @NonCircularNumberOfIterations,
                [non-circular-divisions-next-iteration] = @NonCircularDivisionsNextIteration,
                [number-of-vertices-along-surface] = @NumberOfVerticesAlongSurface,
                [non-circular-number-of-surfaces] = @NonCircularNumberOfSurfaces,
                [number-of-nests] = @NumberOfNests,
                [maximum-iterations] = @MaximumIterations,
                [initial-number-of-surface-vertices] = @InitialNumberOfSurfaceVertices,
                [initial-number-of-iterations] = @InitialNumberOfIterations,
                [maximum-number-of-steps] = @MaximumNumberOfSteps,
                [number-of-factors-safety-compared-before-stopping] = @NumberOfFactorsSafetyComparedBeforeStopping,
                [tolerance-for-stopping-criterion] = @ToleranceForStoppingCriterion,
                [number-of-particles] = @NumberOfParticles,
                [direction] = @Direction
            WHERE id = @Id
            IF @@ROWCOUNT = 0
                INSERT INTO [stability-configurations] (
                    [id],
                    [created-date],
                    [section-id],
                    [should-evaluate-drained-condition],
                    [should-evaluate-undrained-condition],
                    [should-evaluate-pseudo-static-condition],
                    [seismic-coefficient-horizontal],
                    [seismic-coefficient-vertical],
                    [minimum-drained-depth],
                    [minimum-undrained-depth],
                    [minimum-pseudo-static-depth],
                    [gravity],
                    [circular-search-method],
                    [circular-divisions-along-slope],
                    [circles-per-division],
                    [circular-number-of-iterations],
                    [circular-divisions-next-iteration],
                    [radius-increment],
                    [circular-number-of-surfaces],
                    [non-circular-search-method],
                    [non-circular-divisions-along-slope],
                    [surfaces-per-division],
                    [non-circular-number-of-iterations],
                    [non-circular-divisions-next-iteration],
                    [number-of-vertices-along-surface],
                    [non-circular-number-of-surfaces],
                    [number-of-nests],
                    [maximum-iterations],
                    [initial-number-of-surface-vertices],
                    [initial-number-of-iterations],
                    [maximum-number-of-steps],
                    [number-of-factors-safety-compared-before-stopping],
                    [tolerance-for-stopping-criterion],
                    [number-of-particles],
                    [direction]
                ) VALUES (
                    @Id,
                    @CreatedDate,
                    @SectionId,
                    @ShouldEvaluateDrainedCondition,
                    @ShouldEvaluateUndrainedCondition,
                    @ShouldEvaluatePseudoStaticCondition,
                    @SeismicCoefficientHorizontal,
                    @SeismicCoefficientVertical,
                    @MinimumDrainedDepth,
                    @MinimumUndrainedDepth,
                    @MinimumPseudoStaticDepth,
                    @Gravity,
                    @CircularSearchMethod,
                    @CircularDivisionsAlongSlope,
                    @CirclesPerDivision,
                    @CircularNumberOfIterations,
                    @CircularDivisionsNextIteration,
                    @RadiusIncrement,
                    @CircularNumberOfSurfaces,
                    @NonCircularSearchMethod,
                    @NonCircularDivisionsAlongSlope,
                    @SurfacesPerDivision,
                    @NonCircularNumberOfIterations,
                    @NonCircularDivisionsNextIteration,
                    @NumberOfVerticesAlongSurface,
                    @NonCircularNumberOfSurfaces,
                    @NumberOfNests,
                    @MaximumIterations,
                    @InitialNumberOfSurfaceVertices,
                    @InitialNumberOfIterations,
                    @MaximumNumberOfSteps,
                    @NumberOfFactorsSafetyComparedBeforeStopping,
                    @ToleranceForStoppingCriterion,
                    @NumberOfParticles,
                    @Direction
                );
";

        public const string UpsertCircularCalculationMethods = @"
            UPDATE [circular-calculation-methods]
            SET [calculation-method] = @CalculationMethod
            WHERE [section-id] = @SectionId AND [structure-id] = @StructureId AND [calculation-method] = @CalculationMethod
            IF @@ROWCOUNT = 0
                INSERT INTO [circular-calculation-methods]
                    ([calculation-method], [section-id], [structure-id])
                VALUES (@CalculationMethod, @SectionId, @StructureId);
            ";
        
        public const string UpsertNonCircularCalculationMethods = @"
            UPDATE [non-circular-calculation-methods]
            SET [calculation-method] = @CalculationMethod
            WHERE [section-id] = @SectionId AND [structure-id] = @StructureId AND [calculation-method] = @CalculationMethod
            IF @@ROWCOUNT = 0
                INSERT INTO [non-circular-calculation-methods]
                    ([calculation-method], [section-id], [structure-id])
                VALUES (@CalculationMethod, @SectionId, @StructureId);
            ";
        
        public const string GetSectionReviewDrawing = 
            @"SELECT TOP (1)
                [section-reviews].[drawing-unique-name] AS [UniqueName],
                [section-reviews].[drawing-name] AS [Name]
            FROM 
                [section-reviews]
            WHERE [section-reviews].[id] = @SectionReviewId";
        
        public const string GetConstructionStageDrawing =
            @"SELECT TOP (1)
                [construction-stages].[drawing-unique-name] AS [UniqueName],
                [construction-stages].[drawing-name] AS [Name]
            FROM
                [construction-stages]
            WHERE [construction-stages].[id] = @ConstructionStageId";

        public const string UpdateSectionReviewLengthData =
            @"UPDATE [section-reviews]
              SET [length-is-consistent] = @IsConsistent,
                  [length-difference-in-meters] = @DifferenceInMeters,
                  [coordinates-length-in-meters] = @CoordinatesLengthInMeters,
                  [dxf-length-in-meters] = @DxfLengthInMeters
              WHERE [id] = @ReviewId";

        public const string UpdateConstructionStageLengthData =
            @"UPDATE [construction-stages]
              SET [length-is-consistent] = @IsConsistent,
                  [length-difference-in-meters] = @DifferenceInMeters,
                  [coordinates-length-in-meters] = @CoordinatesLengthInMeters,
                  [dxf-length-in-meters] = @DxfLengthInMeters
              WHERE [id] = @StageId";

        public const string GetLengthDataReport = @"
            WITH LengthData AS (
            (
                SELECT 
                    clients.name AS Empresa,
                    [client-units].name AS Unidade,
                    structures.name AS Estrutura,
                    sections.name AS Secao,
                    [section-reviews].[index] AS Revisao,
                    NULL AS Etapa,
                    [section-reviews].[dxf-length-in-meters] AS Distancia_DXF,
                    [section-reviews].[coordinates-length-in-meters] AS Distancia_LatLong,
                    [section-reviews].[length-difference-in-meters] AS Diferenca_Absoluta
                FROM [sections] 
                    JOIN [section-reviews] ON [section-reviews].[section-id] = [sections].[id]
                    JOIN [structures] ON [sections].[structure-id] = structures.id
                    JOIN [client-units] ON structures.[client-unit-id] = [client-units].id
                    JOIN clients on clients.id = [client-units].[client-id]
                WHERE [section-reviews].[is-under-construction] = 0
            )
            UNION ALL 
            (
                SELECT
                    clients.name AS Empresa,
                    [client-units].name AS Unidade,
                    structures.name AS Estrutura,
                    sections.name AS Secao,
                    [section-reviews].[index] AS Revisao,
                    [construction-stages].stage AS Etapa,
                    [construction-stages].[dxf-length-in-meters] AS Distancia_DXF,
                    [construction-stages].[coordinates-length-in-meters] AS Distancia_LatLong,
                    [construction-stages].[length-difference-in-meters] AS Diferenca_Absoluta
                FROM [sections]
                         JOIN [section-reviews] ON [section-reviews].[section-id] = [sections].[id]
                         JOIN [construction-stages] ON [section-reviews].id = [construction-stages].[section-review-id]
                         JOIN [structures] ON [sections].[structure-id] = structures.id
                         JOIN [client-units] ON structures.[client-unit-id] = [client-units].id
                         JOIN clients on clients.id = [client-units].[client-id]
                )
            )
            SELECT * 
            FROM LengthData
            ORDER BY Empresa, Unidade, Estrutura, Secao, Revisao,Etapa;
        ";
    }
}
