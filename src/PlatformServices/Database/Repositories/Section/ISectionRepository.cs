using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Database.Core;
using Domain.Entities;
using Domain.ValueObjects;
using Model.Dashboard.GetLatestUpdates.Response;
using Model.Dashboard.GetSectionsMap.Request;
using Model.Dashboard.GetSectionsMap.Response;
using Model.Section.GetByFiltersMaps.Request;
using Model.Section.List.Request;
using Model.Section.List.Response;
using Model.Section.PatchSectionsChartLineColor.Dto;
using Model.Section.PatchSectionsChartLineColor.Request;
using Model.Section.Search.Request;
using Model.Section.Search.Response;
using Model.Section.SearchHistory.Request;
using Model.Section.SearchHistory.Response;
using Model.Section.V2.GetDrawingFile.Request;
using Model.Section.V2.GetLengthData.Response;

namespace Database.Repositories.Section
{
    public interface ISectionRepository : IRepository<Domain.Entities.Section>
    {
        Task<IEnumerable<Domain.Entities.Section>> GetAsync(IEnumerable<Guid> ids);
        Task<bool> NameExists(string name, Guid structureId);
        Task<bool> NameExists(string name, Guid structureId, Guid id);
        Task UpdateActiveAsync(Domain.Entities.Section section);
        Task<IEnumerable<ListSectionResponse>> ListAsync(ListSectionRequest request);
        Task<IEnumerable<SearchSectionResponse>> SearchAsync(SearchSectionRequest request);
        Task<int> CountAsync(SearchSectionRequest request);
        Task<IEnumerable<Guid>> GetByStructureIdAsync(Guid structureId);
        Task<IEnumerable<Domain.Entities.Section>> GetByStructureAsync(Guid structureId);
        Task<IEnumerable<Domain.Entities.Section>> GetByFiltersMapsAsync(GetSectionByFiltersMapsRequest request);
        Task<SectionReview> GetReviewAsync(Guid reviewId);
        Task UpdateReviewAsync(SectionReview review);
        Task<IEnumerable<StructureIdBySectionDto>> GetStructuresIdBySectionsAsync(IEnumerable<Guid> sectionsId);
        Task UpdateChartLineColorByIdsAsync(List<ChartLineColorBySectionRequest> colorsBySections);
        Task<GetDashboardSectionsMapResponse> GetDashboardMapAsync(GetDashboardSectionsMapRequest request);
        Task<int> CountHistoryAsync(SearchSectionHistoryRequest request);
        Task<IEnumerable<SearchSectionHistoryResponse>> SearchHistoryAsync(SearchSectionHistoryRequest request);
        Task UpdateConstructionStageAsync(ConstructionStage constructionStage);
        Task<List<SectionReview>> GetSectionReviewsAsync(Guid sectionId);
        Task<LatestUpdateResponseItem> GetDashboardLatestAsync(Guid structureId);
        Task UpsertConstructionStageAsync(ConstructionStage constructionStage, SectionHistory history);
        Task UpsertSectionReviewAsync(SectionReview sectionReview, SectionHistory history);
        Task UpdateV2Async(Domain.Entities.Section section);
        Task<File> GetSectionDrawingAsync(GetSectionDrawingFileRequest request);
        Task<List<Domain.Entities.Section>> GetAllAsync();
        Task UpdateSectionReviewLengthDataAsync(Guid reviewId, SectionLength lengthData);
        Task UpdateConstructionStageLengthDataAsync(Guid stageId, SectionLength lengthData);
        Task<IEnumerable<LengthDataReportItem>> GetLengthDataReportAsync();
    }
}
