using Dapper;
using Database.Repositories.Outbox;
using Domain.Entities;
using Domain.Enums;
using Domain.Messages.Events.Reading;
using Domain.Messages.Events.ReadingValue;
using Model._Shared.Reading;
using Model.Chart.ClimateInstrument.Request;
using Model.Chart.Inclinometers.Request;
using Model.Chart.LinimetricRulers.Request;
using Model.Chart.SettlementGauge.Request;
using Model.Chart.SurfaceLandmarkOrPrism.Request;
using Model.Instrument.GetAdjacentReadingValues.Response;
using Model.Reading.GetBeachLengthStats.Request;
using Model.Reading.GetBeachLengthStats.Response;
using Model.Reading.GetById.Response;
using Model.Reading.GetRelatedReadingValues;
using Model.Reading.GetStats.Request;
using Model.Reading.GetStats.Response;
using Model.Reading.Patch.Request;
using Model.Reading.Search.Request;
using Model.Reading.Search.Response;
using Model.Reading.SearchBeachLength.Request;
using Model.Reading.SearchBeachLength.Response;
using Model.Reading.SearchBeachLengthHistory.Request;
using Model.Reading.SearchBeachLengthHistory.Response;
using Model.Reading.SearchHistory.Request;
using Model.Reading.SearchHistory.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Database.Core.SqlBulk;
using Database.Repositories.Reading.DTOs;
using Database.Repositories.Reading.Extensions;
using Microsoft.Data.SqlClient;
using Model.Core.Search.Pagination;
using Model.Dashboard.GetLatestUpdates.Response;
using Model.Reading.Add.DTOs;
using NotificationCommand = Domain.Messages.Commands.Notification.CreateNotification;

namespace Database.Repositories.Reading
{
    public class ReadingRepository : BulkOperationRepository, IReadingRepository
    {
        private const string ReadingsTableName = "readings";
        private const string ReadingValuesTableName = "[reading-values]";
        private const string ReadingSecurityLevelsTableName = "[reading-security-levels]";
        private const string ReadingHistoryTableName = "[readings-history]";
        private const string OutboxTableName = "[outbox]";
        private readonly string _connectionString;

        public ReadingRepository(string connectionString)
        {
            _connectionString = connectionString;
        }

        public Task AddAsync(Domain.Entities.Reading reading)
        {
            throw new NotImplementedException();
        }

        public async Task<decimal> GetTotalRainfallAsync(Guid instrumentId, DateTime date)
        {
            var param = new
            {
                instrumentId,
                date
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.ExecuteScalarAsync<decimal>(Queries.GetTotalRainfall, param);
        }

        public async Task<List<ReadingValue>> GetClimateInstrumentDataAsync(GetClimateInstrumentDataRequest request)
        {
            var param = new
            {
                request.StartDate,
                request.EndDate,
                InstrumentId = request.Id,
                request.Interval,
                request.Aggregation,
                DailyInterval = Interval.Daily,
                WeeklyInterval = Interval.Weekly,
                MonthlyInterval = Interval.Monthly,
                YearlyInterval = Interval.Yearly,
                MaximumAggregation = Aggregation.Maximum,
                AccumulatedAggregation = Aggregation.Accumulated
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            return (await connection.QueryAsync<ReadingValue>(sql: Queries.GetClimateInstrumentData, param: param)).ToList();
        }

        public async Task<List<Domain.Entities.Reading>> GetSurfaceLandmarkOrPrismDataAsync(GetSurfaceLandmarkOrPrismDataRequest request)
        {
            var param = new
            {
                request.InstrumentId,
                request.StartDate,
                request.EndDate
            };

            var lookup = new Dictionary<Guid, Domain.Entities.Reading>();
            var lookupReadingValues = new Dictionary<Guid, ReadingValue>();

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            await connection.QueryAsync(sql: Queries.GetSurfaceLandmarkOrPrism,
               new[]
               {
                    typeof(Domain.Entities.Reading),
                    typeof(SecurityLevels),
                    typeof(Domain.Entities.Instrument),
                    typeof(ReadingValue)
               },
               (records) =>
               {
                   var reading = records[0] as Domain.Entities.Reading;
                   var instrument = records[2] as Domain.Entities.Instrument;
                   var readingValue = records[3] as ReadingValue;

                   if (!lookup.TryGetValue(reading.Id, out var readingEntry))
                   {
                       readingEntry = reading;
                       readingEntry.Instrument = instrument;
                       lookup.Add(readingEntry.Id, readingEntry);
                   }

                   if (!lookupReadingValues.TryGetValue(readingValue.Id, out var readingValueEntry))
                   {
                       readingValueEntry = readingValue;
                       lookupReadingValues.Add(readingValueEntry.Id, readingValueEntry);
                   }

                   if (!readingEntry.Values.Any(x => x.Id == readingValueEntry.Id))
                   {
                       readingEntry.Values.Add(readingValueEntry);
                   }

                   if (records[1] is SecurityLevels securityLevel && !readingValueEntry.SecurityLevels.Any(x => x.Id == securityLevel.Id))
                   {
                       readingValueEntry.SecurityLevels.Add(securityLevel);
                   }

                   return reading;
               },
               splitOn: "Id,Id,Id",
               param: param);

            return lookup.Values.ToList();
        }

        public async Task<List<Domain.Entities.Reading>> GetPercolationDataAsync(List<Guid> instruments, DateTime? startDate, DateTime? endDate)
        {
            var param = new
            {
                startDate,
                endDate,
                instruments
            };

            var lookup = new Dictionary<Guid, Domain.Entities.Reading>();
            var lookupReadingValues = new Dictionary<Guid, ReadingValue>();

            var builder = new SqlBuilder();

            if (startDate.HasValue)
            {
                builder.Where("[reading-values].[date] >= @startDate");
            }

            if (endDate.HasValue)
            {
                builder.Where("[reading-values].[date] <= @endDate");
            }

            builder.Where("[readings].[is-deleted] = 0")
                .Where("[readings].[instrument-id] IN @instruments");

            var query = builder.AddTemplate(Queries.GetPercolationDataWithDateInterval);

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            await connection.QueryAsync(sql: query.RawSql,
                new[]
                {
                    typeof(Domain.Entities.Reading),
                    typeof(Domain.Entities.Instrument),
                    typeof(ReadingValue),
                    typeof(SecurityLevels),
                    typeof(Measurement)
                },
                (records) =>
                {
                    var reading = records[0] as Domain.Entities.Reading;
                    var instrument = records[1] as Domain.Entities.Instrument;
                    var readingValue = records[2] as ReadingValue;
                    var measurement = records[4] as Measurement;

                    if (!lookup.TryGetValue(reading.Id, out var readingEntry))
                    {
                        readingEntry = reading;
                        readingEntry.Instrument = instrument;
                        lookup.Add(readingEntry.Id, readingEntry);
                    }

                    if (!lookupReadingValues.TryGetValue(readingValue.Id, out var readingValueEntry))
                    {
                        readingValueEntry = readingValue;
                        lookupReadingValues.Add(readingValueEntry.Id, readingValueEntry);
                    }

                    if (!readingEntry.Values.Any(x => x.Id == readingValueEntry.Id))
                    {
                        readingEntry.Values.Add(readingValueEntry);
                    }

                    if (records[3] is SecurityLevels securityLevel && !readingValueEntry.SecurityLevels.Any(x => x.Id == securityLevel.Id))
                    {
                        readingValueEntry.SecurityLevels.Add(securityLevel);
                    }

                    readingValueEntry.Measurement = measurement;

                    return reading;
                },
                splitOn: "Id,Id,Id,Id",
                param: param);

            return lookup.Values.ToList();
        }

        public async Task<List<Domain.Entities.Reading>> GetPercolationDataAsync(List<Guid> instruments, FilterPercolationInstrumentPeriod period)
        {
            var param = new
            {
                period,
                instruments,
                FilterPercolationInstrumentPeriod.OneMonth,
                FilterPercolationInstrumentPeriod.ThreeMonths,
                FilterPercolationInstrumentPeriod.SixMonths,
                FilterPercolationInstrumentPeriod.OneYear,
                FilterPercolationInstrumentPeriod.TwoYears,
                FilterPercolationInstrumentPeriod.FiveYears
            };

            var lookup = new Dictionary<Guid, Domain.Entities.Reading>();
            var lookupReadingValues = new Dictionary<Guid, ReadingValue>();

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            await connection.QueryAsync(sql: Queries.GetPercolationData,
                new[]
                {
                    typeof(Domain.Entities.Reading),
                    typeof(Domain.Entities.Instrument),
                    typeof(ReadingValue),
                    typeof(SecurityLevels),
                    typeof(Measurement)
                },
                (records) =>
                {
                    var reading = records[0] as Domain.Entities.Reading;
                    var instrument = records[1] as Domain.Entities.Instrument;
                    var readingValue = records[2] as ReadingValue;
                    var measurement = records[4] as Measurement;

                    if (!lookup.TryGetValue(reading.Id, out var readingEntry))
                    {
                        readingEntry = reading;
                        readingEntry.Instrument = instrument;
                        lookup.Add(readingEntry.Id, readingEntry);
                    }

                    if (!lookupReadingValues.TryGetValue(readingValue.Id, out var readingValueEntry))
                    {
                        readingValueEntry = readingValue;
                        lookupReadingValues.Add(readingValueEntry.Id, readingValueEntry);
                    }

                    if (!readingEntry.Values.Any(x => x.Id == readingValueEntry.Id))
                    {
                        readingEntry.Values.Add(readingValueEntry);
                    }

                    if (records[3] is SecurityLevels securityLevel && !readingValueEntry.SecurityLevels.Any(x => x.Id == securityLevel.Id))
                    {
                        readingValueEntry.SecurityLevels.Add(securityLevel);
                    }

                    readingValueEntry.Measurement = measurement;

                    return reading;
                },
                splitOn: "Id,Id,Id,Id",
                param: param);

            return lookup.Values.ToList();
        }

        public async Task<List<ReadingValue>> GetInclinometerDataAsync(GetInclinometerDataRequest request)
        {
            var param = new
            {
                request.StartDate,
                request.EndDate,
                InstrumentId = request.Id
            };

            var lookupReadingValues = new Dictionary<Guid, ReadingValue>();

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            return (await connection.QueryAsync(sql: Queries.GetInclinometerData,
                new[]
                {
                    typeof(ReadingValue),
                    typeof(SecurityLevels),
                    typeof(Measurement)
                },
                (records) =>
                {
                    var readingValue = records[0] as ReadingValue;
                    var measurement = records[2] as Measurement;

                    if (!lookupReadingValues.TryGetValue(readingValue.Id, out var readingValueEntry))
                    {
                        readingValueEntry = readingValue;
                        lookupReadingValues.Add(readingValueEntry.Id, readingValueEntry);
                    }

                    if (records[1] is SecurityLevels securityLevel && !readingValueEntry.SecurityLevels.Any(x => x.Id == securityLevel.Id))
                    {
                        readingValueEntry.SecurityLevels.Add(securityLevel);
                    }

                    readingValueEntry.Measurement = measurement;

                    return readingValueEntry;
                },
                splitOn: "Id,Identifier",
                param: param)).ToList();
        }

        public async Task<List<Domain.Entities.Reading>> GetLinimetricRulerDataAsync(GetLinimetricRulerDataRequest request)
        {
            var param = new
            {
                request.StartDate,
                request.EndDate,
                request.UpstreamLinimetricRulerId,
                request.DownstreamLinimetricRulerId
            };

            var lookupReadingValues = new Dictionary<Guid, ReadingValue>();

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            return (await connection.QueryAsync(sql: Queries.GetLinimetricRulerData,
                new[]
                {
                    typeof(Domain.Entities.Reading),
                    typeof(SecurityLevels),
                    typeof(ReadingValue),
                    typeof(Domain.Entities.Instrument)
                },
                (records) =>
                {
                    var reading = records[0] as Domain.Entities.Reading;
                    var readingValue = records[2] as ReadingValue;
                    var instrument = records[3] as Domain.Entities.Instrument;

                    if (!lookupReadingValues.TryGetValue(readingValue.Id, out var readingValueEntry))
                    {
                        readingValueEntry = readingValue;
                        lookupReadingValues.Add(readingValueEntry.Id, readingValueEntry);
                    }

                    if (!reading.Values.Any(x => x.Id == readingValueEntry.Id))
                    {
                        reading.Values.Add(readingValueEntry);
                    }

                    if (records[1] is SecurityLevels securityLevel && !readingValueEntry.SecurityLevels.Any(x => x.Id == securityLevel.Id))
                    {
                        readingValueEntry.SecurityLevels.Add(securityLevel);
                    }

                    reading.Instrument = instrument;

                    return reading;
                },
                splitOn: "Id,Id,Id",
                param: param)).ToList();
        }

        public async Task<List<ReadingValue>> GetSettlementGaugeDataAsync(GetSettlementGaugeDataRequest request)
        {
            var param = new
            {
                request.StartDate,
                request.EndDate,
                request.InstrumentId
            };

            var lookupReadingValues = new Dictionary<Guid, ReadingValue>();

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            return (await connection.QueryAsync(sql: Queries.GetSettlementGaugeData,
                new[]
                {
                    typeof(ReadingValue),
                    typeof(SecurityLevels),
                    typeof(Measurement)
                },
                (records) =>
                {
                    var readingValue = records[0] as ReadingValue;
                    var measurement = records[2] as Measurement;

                    if (!lookupReadingValues.TryGetValue(readingValue.Id, out var readingValueEntry))
                    {
                        readingValueEntry = readingValue;
                        lookupReadingValues.Add(readingValueEntry.Id, readingValueEntry);
                    }

                    readingValueEntry.Measurement = measurement;

                    if (records[1] is SecurityLevels securityLevel && !readingValueEntry.SecurityLevels.Any(x => x.Id == securityLevel.Id))
                    {
                        readingValueEntry.SecurityLevels.Add(securityLevel);
                    }

                    return readingValueEntry;
                },
                splitOn: "Id,Identifier",
                param: param)).ToList();
        }

        public async Task<bool> ExistsBeachLengthAsync(Guid sectionId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.CheckIfExistsBeachLength, new
            {
                sectionId
            });

            return res > 0;
        }

        public async Task<bool> ExistsAsync(Guid instrumentId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.CheckIfExistsReading, new
            {
                instrumentId
            });

            return res > 0;
        }

        public async Task<bool> CheckIfExistsReadingWithin24Hours(Guid instrumentId, DateTime date, Guid readingId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.CheckIfExistsOtherReadingWithin24Hours, new
            {
                instrumentId,
                readingId,
                date
            });

            return res > 0;
        }

        public async Task<bool> CheckIfExistsReadingWithin24Hours(Guid instrumentId, DateTime date)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.CheckIfExistsReadingWithin24Hours, new
            {
                instrumentId,
                date
            });

            return res > 0;
        }

        public async Task<int> CountBeachLengthAsync(SearchBeachLengthRequest request)
        {
            var param = new
            {
                request.Query.ClientId,
                request.Query.ClientUnitId,
                request.Query.StructureId,
                request.Query.StartDate,
                request.Query.EndDate,
                request.Query.SearchIdentifier,
                request.Body.SectionIds,
                SectionsCount = request.Body.SectionIds?.Count,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.ExecuteScalarAsync<int>(Queries.CountBeachLength, param);
        }

        public async Task<GetInstrumentReadingValuesResponse> GetAdjacentReadingAsync(
            AdjacencyIndicator adjacency,
            Guid instrumentId,
            DateTime date,
            Guid? measurementId = null)
        {
            var param = new
            {
                Date = date,
                InstrumentId = instrumentId,
                MeasurementId = measurementId
            };

            var builder = new SqlBuilder()
                .Where("[readings].[is-deleted] = 0")
                .Where("[readings].[instrument-id] = @InstrumentId");

            if (measurementId.HasValue)
            {
                builder.Where("[reading-values].[measurement-id] = @MeasurementId");
            }

            var query = adjacency switch
            {
                AdjacencyIndicator.Previous => builder
                    .Where("[reading-values].[date] < @Date")
                    .OrderBy("[reading-values].[date] DESC")
                    .AddTemplate(Queries.GetAdjacentReading),

                AdjacencyIndicator.Next => builder
                    .Where("[reading-values].[date] > @Date")
                    .OrderBy("[reading-values].[date]")
                    .AddTemplate(Queries.GetAdjacentReading),

                _ => throw new InvalidOperationException()
            };

            var lookup = new Dictionary<Guid, GetInstrumentReadingValuesResponse>();

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            _ = await connection.QueryAsync(
                sql: query.RawSql,
                types: new[]
                {
                    typeof(GetInstrumentReadingValuesResponse),
                    typeof(Model._Shared.SecurityLevels.SecurityLevels),
                    typeof(Model._Shared.Instrument.Measurement),
                    typeof(Model._Shared.Nature.Nature)
                },
                map: (records) =>
                {
                    var reading = records[0] as GetInstrumentReadingValuesResponse;
                    var securityLevel = records[1] as Model._Shared.SecurityLevels.SecurityLevels;
                    var measurement = records[2] as Model._Shared.Instrument.Measurement;
                    var nature = records[3] as Model._Shared.Nature.Nature;

                    if (!lookup.TryGetValue(reading.Id, out var readingValueEntry))
                    {
                        reading.Measurement = measurement;
                        reading.Nature = nature;

                        readingValueEntry = reading;
                        lookup.Add(readingValueEntry.Id, readingValueEntry);
                    }

                    if (!readingValueEntry.SecurityLevels.Contains(securityLevel))
                    {
                        readingValueEntry.SecurityLevels.Add(securityLevel);
                    }

                    return reading;
                },
                splitOn: "Id,Id,Id",
                param: param);

            return lookup.FirstOrDefault().Value;
        }

        public async Task<List<SearchBeachLengthResponse>> SearchBeachLengthAsync(SearchBeachLengthRequest request)
        {
            var param = new
            {
                request.Query.ClientId,
                request.Query.ClientUnitId,
                request.Query.StructureId,
                request.Query.StartDate,
                request.Query.EndDate,
                request.Query.SearchIdentifier,
                request.Query.PageSize,
                Skip = request.Query.GetSkip(),
                request.Body.SectionIds,
                SectionsCount = request.Body.SectionIds?.Count,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return (await connection.QueryAsync<SearchBeachLengthResponse>(
                sql: Queries.SearchBeachLength,
                new[]
                {
                    typeof(BeachLength),
                    typeof(Domain.Entities.Section),
                    typeof(Domain.Entities.Structure),
                },
                (records) =>
                {
                    var beachLength = records[0] as BeachLength;
                    var section = records[1] as Domain.Entities.Section;
                    var structure = records[2] as Domain.Entities.Structure;

                    return new()
                    {
                        Id = beachLength.Id,
                        SearchIdentifier = beachLength.SearchIdentifier,
                        Date = beachLength.Date,
                        Length = beachLength.Length,
                        Section = new()
                        {
                            Id = section.Id,
                            Name = section.Name
                        },
                        Structure = new()
                        {
                            Id = structure.Id,
                            Name = structure.Name,
                            ClientUnitId = structure.ClientUnitId
                        }
                    };
                },
                splitOn: "Id,Id",
                param: param
                )).ToList();
        }

        public async Task<IEnumerable<SearchReadingHistoryResponse>> SearchHistoryAsync(SearchReadingHistoryRequest request)
        {
            var param = new
            {
                request.ReadingId,
                Skip = request.GetSkip(),
                request.PageSize,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.QueryAsync<SearchReadingHistoryResponse>(
                sql: Queries.SearchHistory,
                new[]
                {
                    typeof(Domain.Entities.ReadingHistory),
                    typeof(Domain.Entities.User)
                },
                (records) =>
                {
                    var history = records[0] as Domain.Entities.ReadingHistory;
                    var user = records[1] as Domain.Entities.User;

                    return new()
                    {
                        Id = history.Id,
                        Changes = history.Changes,
                        CreatedDate = history.CreatedDate,
                        ModifiedBy = new()
                        {
                            Id = user.Id,
                            FirstName = user.FirstName,
                            Surname = user.Surname,
                            Username = user.Username
                        }
                    };
                },
                splitOn: "Id",
                param: param
                );
        }

        public async Task<IEnumerable<SearchBeachLengthHistoryResponse>> SearchBeachLengthHistoryAsync(SearchBeachLengthHistoryRequest request)
        {
            var param = new
            {
                request.BeachLengthId,
                Skip = request.GetSkip(),
                request.PageSize,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.QueryAsync<SearchBeachLengthHistoryResponse>(
                sql: Queries.SearchBeachLengthHistory,
                new[]
                {
                    typeof(BeachLengthHistory),
                    typeof(Domain.Entities.User)
                },
                (records) =>
                {
                    var history = records[0] as BeachLengthHistory;
                    var user = records[1] as Domain.Entities.User;

                    return new()
                    {
                        Id = history.Id,
                        Changes = history.Changes,
                        CreatedDate = history.CreatedDate,
                        ModifiedBy = new()
                        {
                            Id = user.Id,
                            FirstName = user.FirstName,
                            Surname = user.Surname,
                            Username = user.Username
                        }
                    };
                },
                splitOn: "Id",
                param: param);
        }

        public async Task<int> CountBeachLengthHistoryAsync(SearchBeachLengthHistoryRequest request)
        {
            var param = new
            {
                request.BeachLengthId,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.ExecuteScalarAsync<int>(Queries.CountBeachLengthHistory, param);
        }

        public async Task<int> CountHistoryAsync(SearchReadingHistoryRequest request)
        {
            var param = new
            {
                request.ReadingId,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.ExecuteScalarAsync<int>(Queries.CountHistory, param);
        }

        public async Task<BeachLength> GetBeachLengthAsync(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            var beachLength = await connection.QueryAsync(
                sql: Queries.GetBeachLength,
                new[]
                {
                    typeof(BeachLength),
                    typeof(Domain.Entities.Section),
                    typeof(Domain.Entities.Structure),
                },
                (records) =>
                {
                    var beachLength = records[0] as BeachLength;
                    var section = records[1] as Domain.Entities.Section;
                    var structure = records[2] as Domain.Entities.Structure;

                    section.Structure = structure;
                    beachLength.Section = section;

                    return beachLength;
                },
                splitOn: "Id,Id",
                param: new { id }
                );

            return beachLength.FirstOrDefault();
        }

        public async Task<Domain.Entities.Reading> GetReferentialAsync(Guid instrumentId)
        {
            var lookup = new Dictionary<Guid, Domain.Entities.Reading>();
            
            var template = new SqlBuilder()
                .Where("[readings].[instrument-id] = @Id", new { Id = instrumentId })
                .Where("[readings].[is-deleted] = 0")
                .Where("[readings].[is-referential] = 1")
                .AddTemplate(Queries.GetAllReferencesReadingByInstrumentId);

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.QueryAsync(
                sql: template.RawSql,
                new[]
                {
                    typeof(Domain.Entities.Reading),
                    typeof(Domain.Entities.Instrument),
                    typeof(Domain.Entities.Structure),
                    typeof(ReadingValue),
                    typeof(Measurement),
                    typeof(Domain.Entities.Nature)
                },
                (records) =>
                {
                    var reading = records[0] as Domain.Entities.Reading;
                    var instrument = records[1] as Domain.Entities.Instrument;
                    var structure = records[2] as Domain.Entities.Structure;
                    var readingValue = records[3] as Domain.Entities.ReadingValue;

                    if (!lookup.TryGetValue(reading.Id, out var readingEntry))
                    {
                        readingEntry = reading;
                        instrument.Structure = structure;
                        readingEntry.Instrument = instrument;

                        lookup.Add(readingEntry.Id, readingEntry);
                    }

                    if (records[4] is Measurement measurement)
                    {
                        readingValue.Measurement = measurement;
                    }

                    if (records[5] is Domain.Entities.Nature nature)
                    {
                        readingValue.Nature = nature;
                    }

                    readingEntry.Values.Add(readingValue);

                    return readingEntry;
                },
                splitOn: "Id,Id,Id,Id,Id,Id",
                param: template.Parameters
            );

            return lookup.Values.FirstOrDefault();
        }

        public async Task UpdateDateAsync(List<PatchReadingValueRequest> request)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                foreach (var readingValue in request)
                {
                    var param = new
                    {
                        readingValue.Id,
                        readingValue.Date
                    };

                    var dateChanged = await connection
                        .ExecuteScalarAsync<int>(Queries.CheckIfDateChanged, param, transaction);

                    if (dateChanged == 0)
                    {
                        await connection.ExecuteAsync(OutboxQueries.Insert, new
                        {
                            Id = Guid.NewGuid(),
                            Type = typeof(ReadingValueDateChanged).AssemblyQualifiedName,
                            Data = JsonSerializer.Serialize(new
                            {
                                readingValue.Id
                            })
                        }, transaction);

                        await connection.ExecuteAsync(OutboxQueries.Insert, new
                        {
                            Id = Guid.NewGuid(),
                            Type = typeof(NotificationCommand).AssemblyQualifiedName,
                            Data = JsonSerializer.Serialize(new NotificationCommand()
                            {
                                Id = readingValue.Id,
                                Theme = NotificationTheme.ReadingUpdated,
                                CreatedDate = DateTime.UtcNow
                            })
                        }, transaction);
                    }

                    await connection
                        .ExecuteAsync(Queries.UpdateDate, param, transaction);
                }

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task UpdateBeachLengthAsync(BeachLength beachLength)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                var param = new
                {
                    beachLength.Id,
                    beachLength.Length,
                    beachLength.Date,
                    SectionId = beachLength.Section.Id
                };

                await connection.ExecuteAsync(Queries.UpdateBeachLength, param, transaction);

                foreach (var history in beachLength.History)
                {
                    var historyParam = new
                    {
                        history.Id,
                        BeachLengthId = beachLength.Id,
                        history.Changes,
                        ModifiedByUserId = history.ModifiedBy.Id
                    };

                    await connection.ExecuteAsync(Queries.InsertBeachLengthHistory, historyParam, transaction);
                }

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task UpdateAsync(List<Domain.Entities.Reading> readings)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                foreach (var reading in readings)
                {
                    if (reading.IsReferential != null && (bool)reading.IsReferential)
                    {
                        await connection
                            .ExecuteAsync(Queries.DisableReferenceReading, new
                            {
                                InstrumentId = reading.Instrument.Id
                            }, transaction);
                    }

                    var param = new
                    {
                        reading.Id,
                        reading.IsReferential
                    };

                    await connection
                        .ExecuteAsync(Queries.Update, param, transaction);

                    foreach (var readingValue in reading.Values)
                    {
                        var readingValueParam = new
                        {
                            readingValue.Id,
                            readingValue.Date,
                            readingValue.Quota,
                            readingValue.Depth,
                            readingValue.Pluviometry,
                            readingValue.Intensity,
                            readingValue.Pressure,
                            readingValue.Dry,
                            readingValue.PositiveA,
                            readingValue.NegativeA,
                            readingValue.PositiveB,
                            readingValue.NegativeB,
                            readingValue.AverageDisplacementA,
                            readingValue.AverageDisplacementB,
                            readingValue.AccumulatedDisplacementA,
                            readingValue.AccumulatedDisplacementB,
                            readingValue.DeviationA,
                            readingValue.DeviationB,
                            readingValue.AAxisReading,
                            readingValue.BAxisReading,
                            readingValue.Datum,
                            readingValue.EastCoordinate,
                            readingValue.NorthCoordinate,
                            readingValue.EastDisplacement,
                            readingValue.NorthDisplacement,
                            readingValue.ZDisplacement,
                            readingValue.TotalPlanimetricDisplacement,
                            readingValue.ADisplacement,
                            readingValue.BDisplacement,
                            readingValue.RelativeDepth,
                            readingValue.DeltaRef,
                            readingValue.AbsoluteSettlement,
                            readingValue.RelativeSettlement,
                            NatureId = readingValue.Nature?.Id,
                            readingValue.AAxisPga,
                            readingValue.BAxisPga,
                            readingValue.ZAxisPga
                        };

                        var dateChanged = await connection
                            .ExecuteScalarAsync<int>(Queries.CheckIfDateChanged, readingValueParam, transaction);

                        if (dateChanged == 0)
                        {
                            await connection.ExecuteAsync(OutboxQueries.Insert, new
                            {
                                Id = Guid.NewGuid(),
                                Type = typeof(ReadingValueDateChanged).AssemblyQualifiedName,
                                Data = JsonSerializer.Serialize(new
                                {
                                    readingValue.Id
                                })
                            }, transaction);
                        }

                        await connection
                            .ExecuteAsync(Queries.UpdateReadingValue, readingValueParam, transaction);
                    }

                    foreach (var history in reading.History)
                    {
                        var historyParam = new
                        {
                            history.Id,
                            ReadingId = reading.Id,
                            history.Changes,
                            ModifiedByUserId = history.ModifiedBy.Id
                        };

                        await connection.ExecuteAsync(Queries.InsertReadingHistory, historyParam, transaction);
                    }

                    await connection.ExecuteAsync(OutboxQueries.Insert, new
                    {
                        Id = Guid.NewGuid(),
                        Type = typeof(ReadingUpdated).AssemblyQualifiedName,
                        Data = JsonSerializer.Serialize(new
                        {
                            reading.Id
                        })
                    }, transaction);

                    await connection.ExecuteAsync(OutboxQueries.Insert, new
                    {
                        Id = Guid.NewGuid(),
                        Type = typeof(NotificationCommand).AssemblyQualifiedName,
                        Data = JsonSerializer.Serialize(new NotificationCommand()
                        {
                            Id = reading.Id,
                            Theme = NotificationTheme.ReadingUpdated,
                            CreatedById = reading.History.Any()
                            ? reading.History
                                .OrderByDescending(x => x.CreatedDate)
                                .FirstOrDefault()!.ModifiedBy.Id
                            : Guid.Empty,
                            CreatedDate = DateTime.Now
                        })
                    }, transaction);
                }

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task AddBeachLengthAsync(BeachLength beachLength)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                var param = new
                {
                    beachLength.Id,
                    beachLength.Date,
                    beachLength.Length,
                    SectionId = beachLength.Section.Id
                };

                await connection
                    .ExecuteAsync(Queries.InsertBeachLength, param, transaction);

                foreach (var history in beachLength.History)
                {
                    var historyParam = new
                    {
                        history.Id,
                        BeachLengthId = beachLength.Id,
                        history.Changes,
                        ModifiedByUserId = history.ModifiedBy.Id
                    };

                    await connection.ExecuteAsync(Queries.InsertBeachLengthHistory, historyParam, transaction);
                }

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task AddAsync(List<Domain.Entities.Reading> readings)
        {
            List<object> disableRefReadingsParams = new();
            List<InsertReadingParameters> readingParams = new();
            List<InsertReadingValueParameters> readingValueParams = new();
            List<InsertSecurityLevelsParameters> securityLevelParams = new();
            List<InsertReadingHistoryParameters> historyParams = new();
            List<Domain.Entities.Outbox> outboxParams = new();

            foreach (var reading in readings)
            {
                readingParams.Add(reading.ToDbWriteParam());

                if (reading.IsReferential.HasValue &&
                    reading.IsReferential.Value)
                {
                    disableRefReadingsParams.Add(new
                    {
                        InstrumentId = reading.Instrument.Id
                    });
                }

                foreach (var readingValue in reading.Values)
                {
                    readingValueParams.Add(
                        readingValue.ToDbWriteParam(reading));

                    securityLevelParams.AddRange(
                        readingValue.SecurityLevels
                            .ToDbWriteParams(readingValue));
                }

                historyParams.AddRange(
                    reading.History.ToDbWriteParams(reading));

                outboxParams.Add(reading.ToReadingCreatedOutboxParam());
                
                outboxParams.Add(
                    reading.ToReadingCreatedNotificationOutboxParam());
            }

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();
            
            try
            {
                if (disableRefReadingsParams.Any())
                {
                    await connection .ExecuteAsync(
                        Queries.DisableReferenceReading, 
                        disableRefReadingsParams,
                        transaction);
                }

                await BulkInsertAsync(
                    readingParams,
                    ReadingsTableName,
                    new BulkColumnMapper().WithInsertReadingQuery(),
                    connection,
                    (SqlTransaction)transaction);
                
                await BulkInsertAsync(
                    readingValueParams,
                    ReadingValuesTableName,
                    new BulkColumnMapper().WithInsertReadingValueQuery(),
                    connection,
                    (SqlTransaction)transaction);

                await BulkInsertAsync(
                    securityLevelParams,
                    ReadingSecurityLevelsTableName,
                    new BulkColumnMapper().WithInsertReadingSecurityLevelsQuery(),
                    connection,
                    (SqlTransaction)transaction);

                await BulkInsertAsync(
                    historyParams,
                    ReadingHistoryTableName,
                    new BulkColumnMapper().WithInsertReadingHistoryQuery(),
                    connection,
                    (SqlTransaction)transaction);
                
                await BulkInsertAsync(
                    outboxParams,
                    OutboxTableName,
                    new BulkColumnMapper().WithInsertOutboxQuery(),
                    connection,
                    (SqlTransaction)transaction);

                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task DeleteAsync(List<Guid> ids, Guid requesterId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                foreach (var id in ids)
                {
                    var auxData = new Dictionary<string, string>();

                    var reading = await GetAsync(id);

                    if (reading != null)
                    {
                        auxData.Add("InstrumentId", reading.Instrument.Id.ToString());

                        if (reading.Values.Any())
                        {
                            var deletedReadingDate = reading.Values.Max(x => x.Date).Date;
                            auxData.Add("ReadingDate", deletedReadingDate.ToString());
                        }
                    }

                    await connection.ExecuteAsync(OutboxQueries.Insert, new
                    {
                        Id = Guid.NewGuid(),
                        Type = typeof(NotificationCommand).AssemblyQualifiedName,
                        Data = JsonSerializer.Serialize(new NotificationCommand()
                        {
                            Id = id,
                            Theme = NotificationTheme.ReadingDeleted,
                            CreatedById = requesterId,
                            CreatedDate = DateTime.UtcNow,
                            AuxiliaryData = auxData
                        })
                    }, transaction);
                }

                await connection.ExecuteAsync(Queries.BulkDelete, new { ids }, transaction);
                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task DeleteAsync(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                var auxData = new Dictionary<string, string>();

                var reading = await GetAsync(id);

                if (reading != null)
                {
                    auxData.Add("InstrumentId", reading.Instrument.Id.ToString());

                    if (reading.Values.Any())
                    {
                        var deletedReadingDate = reading.Values.Max(x => x.Date).Date;
                        auxData.Add("ReadingDate", deletedReadingDate.ToString());
                    }
                }

                await connection.ExecuteAsync(OutboxQueries.Insert, new
                {
                    Id = Guid.NewGuid(),
                    Type = typeof(NotificationCommand).AssemblyQualifiedName,
                    Data = JsonSerializer.Serialize(new NotificationCommand()
                    {
                        Id = id,
                        Theme = NotificationTheme.ReadingDeleted,
                        CreatedDate = DateTime.UtcNow,
                        AuxiliaryData = auxData
                    })
                }, transaction);

                await connection.ExecuteAsync(Queries.Delete, new { id }, transaction);
                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<Domain.Entities.Reading> GetByReadingValueIdAsync(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var lookup = new Dictionary<Guid, Domain.Entities.Reading>();

            await connection.QueryAsync(
                sql: Queries.GetByReadingValues,
                new[]
                {
                    typeof(Domain.Entities.Reading),
                    typeof(Domain.Entities.Instrument),
                    typeof(Domain.Entities.Structure),
                    typeof(ReadingValue),
                    typeof(Measurement),
                    typeof(Domain.Entities.Nature),
                    typeof(Domain.Entities.Reading),
                    typeof(Domain.Entities.Instrument),
                    typeof(Domain.Entities.Structure),
                    typeof(ReadingValue),
                    typeof(Measurement),
                    typeof(Domain.Entities.Nature)
                },
                (records) =>
                {
                    var reading = records[0] as Domain.Entities.Reading;
                    var instrument = records[1] as Domain.Entities.Instrument;
                    var structure = records[2] as Domain.Entities.Structure;
                    var readingValue = records[3] as ReadingValue;

                    var referenceInstrument = records[7] as Domain.Entities.Instrument;
                    var referenceStructure = records[8] as Domain.Entities.Structure;
                    var referenceReadingValue = records[9] as ReadingValue;

                    if (!lookup.TryGetValue(reading.Id, out var readingEntry))
                    {
                        readingEntry = reading;

                        instrument.Structure = structure;
                        readingEntry.Instrument = instrument;

                        if (records[6] is Domain.Entities.Reading referenceReading)
                        {
                            referenceInstrument.Structure = referenceStructure;
                            referenceReading.Instrument = referenceInstrument;

                            readingEntry.ReferenceReading = referenceReading;
                        }

                        lookup.Add(readingEntry.Id, readingEntry);
                    }

                    if (records[4] is Measurement measurement)
                    {
                        readingValue.Measurement = measurement;
                    }

                    if (records[5] is Domain.Entities.Nature nature)
                    {
                        readingValue.Nature = nature;
                    }

                    if (readingEntry.Values.All(rv => rv.Id != readingValue.Id))
                    {
                        readingEntry.Values.Add(readingValue);
                    }

                    if (records[10] is Measurement refMeasurement)
                    {
                        referenceReadingValue.Measurement = refMeasurement;
                    }

                    if (records[11] is Domain.Entities.Nature refNature)
                    {
                        referenceReadingValue.Nature = refNature;
                    }

                    if (readingEntry.ReferenceReading != null && readingEntry.ReferenceReading.Values.All(rv => rv.Id != referenceReadingValue.Id))
                    {
                        readingEntry.ReferenceReading.Values.Add(referenceReadingValue);
                    }

                    return readingEntry;
                },
                splitOn: "Id,Id,Id,Id,Id,Id,Id,Id",
                param: new
                {
                    Id = id
                });

            return lookup.Values.FirstOrDefault();
        }

        public async Task<Domain.Entities.Reading> GetAsync(Guid id)
        {
            var lookup = new Dictionary<Guid, Domain.Entities.Reading>();
            var lookupReadingValues = new Dictionary<Guid, ReadingValue>();

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            await connection.QueryAsync(
                sql: Queries.GetById,
                new[]
                {
                    typeof(Domain.Entities.Reading),
                    typeof(SecurityLevels),
                    typeof(Domain.Entities.Instrument),
                    typeof(Domain.Entities.Structure),
                    typeof(ReadingValue),
                    typeof(Measurement),
                    typeof(Domain.Entities.Nature),
                    typeof(Domain.Entities.Reading),
                    typeof(Domain.Entities.Instrument),
                    typeof(Domain.Entities.Structure),
                    typeof(ReadingValue),
                    typeof(Measurement),
                    typeof(Domain.Entities.Nature)
                },
                (records) =>
                {
                    var reading = records[0] as Domain.Entities.Reading;
                    var instrument = records[2] as Domain.Entities.Instrument;
                    var structure = records[3] as Domain.Entities.Structure;
                    var readingValue = records[4] as ReadingValue;
                    var referenceInstrument = records[8] as Domain.Entities.Instrument;
                    var referenceStructure = records[9] as Domain.Entities.Structure;
                    var referenceReadingValue = records[10] as ReadingValue;

                    if (!lookup.TryGetValue(reading.Id, out var readingEntry))
                    {
                        readingEntry = reading;

                        instrument.Structure = structure;
                        readingEntry.Instrument = instrument;

                        if (records[7] is Domain.Entities.Reading referenceReading)
                        {
                            referenceInstrument.Structure = referenceStructure;
                            referenceReading.Instrument = referenceInstrument;

                            readingEntry.ReferenceReading = referenceReading;
                        }

                        lookup.Add(readingEntry.Id, readingEntry);
                    }

                    if (!lookupReadingValues.TryGetValue(readingValue.Id, out var readingValueEntry))
                    {
                        readingValueEntry = readingValue;
                        lookupReadingValues.Add(readingValueEntry.Id, readingValueEntry);
                    }

                    if (records[1] is SecurityLevels securityLevel && !readingValueEntry.SecurityLevels.Any(x => x.Id == securityLevel.Id))
                    {
                        readingValueEntry.SecurityLevels.Add(securityLevel);
                    }

                    if (records[5] is Measurement measurement)
                    {
                        readingValueEntry.Measurement = measurement;
                    }

                    if (records[6] is Domain.Entities.Nature nature)
                    {
                        readingValueEntry.Nature = nature;
                    }

                    if (!readingEntry.Values.Any(rv => rv.Id == readingValueEntry.Id))
                    {
                        readingEntry.Values.Add(readingValueEntry);
                    }

                    if (records[11] is Measurement refMeasurement)
                    {
                        referenceReadingValue.Measurement = refMeasurement;
                    }

                    if (records[12] is Domain.Entities.Nature refNature)
                    {
                        referenceReadingValue.Nature = refNature;
                    }

                    if (readingEntry.ReferenceReading != null && readingEntry.ReferenceReading.Values.All(rv => rv.Id != referenceReadingValue.Id))
                    {
                        readingEntry.ReferenceReading.Values.Add(referenceReadingValue);
                    }

                    return readingEntry;
                },
                splitOn: "Id,Id,Id,Id,Id,Id,Id,Id,Id,Id,Id,Id",
                param: new
                {
                    id
                });

            return lookup.Values.FirstOrDefault();
        }

        public async Task<List<GetBeachLengthStatsResponse>> GetBeachLengthStatsAsync(GetBeachLengthStatsRequest request)
        {
            var param = new
            {
                request.SectionIds,
                request.StartDate,
                request.EndDate,
                request.ReadingStatisticalMeasure,
                Max = ReadingStatisticalMeasure.Maximum,
                Min = ReadingStatisticalMeasure.Minimum,
                Avg = ReadingStatisticalMeasure.Average,
                Med = ReadingStatisticalMeasure.Median,
            };

            var resultList = new List<GetBeachLengthStatsResponse>();

            var sqlBuilder = new SqlBuilder()
                .Where("[beach-lengths].[section-id] IN @SectionIds");

            if (request.StartDate.HasValue && request.EndDate.HasValue)
            {
                sqlBuilder.Where("[beach-lengths].[date] BETWEEN @StartDate AND @EndDate");
            }

            var sqlTemplate = sqlBuilder.AddTemplate(Queries.GetBeachLengthStats);

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var result = await connection.QueryAsync(
                sql: sqlTemplate.RawSql,
                new[]
                {
                    typeof(Guid),
                    typeof(double)
                },
                (records) =>
                {
                    var sectionId = (Guid)records[0];
                    var value = (double)records[1];

                    return new GetBeachLengthStatsResponse()
                    {
                        SectionId = sectionId,
                        Length = value
                    };
                },
                splitOn: "Id,Value",
                param: param);

            resultList.AddRange(result);

            return resultList;
        }

        public async Task<LatestUpdateResponseItem> GetDashboardLatestAsync(Guid structureId, bool isAutomated)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();

            return await connection.QuerySingleOrDefaultAsync<LatestUpdateResponseItem>(
                sql: Queries.GetDashboardLatest,
                param: new { StructureId = structureId, IsAutomated = isAutomated });
        }

        public async Task<List<Domain.Entities.Reading>> GetReferentialsAsync(IEnumerable<Guid> ids)
        {
            var lookup = new Dictionary<Guid, Domain.Entities.Reading>();

            var template = new SqlBuilder()
                .Where("[readings].[instrument-id] IN @InstrumentIds", new { InstrumentIds = ids })
                .Where("[readings].[is-deleted] = 0")
                .Where("[readings].[is-referential] = 1")
                .AddTemplate(Queries.GetAllReferencesReadingByInstrumentId);

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.QueryAsync(
                sql: template.RawSql,
                new[]
                {
                    typeof(Domain.Entities.Reading),
                    typeof(Domain.Entities.Instrument),
                    typeof(Domain.Entities.Structure),
                    typeof(ReadingValue),
                    typeof(Measurement),
                    typeof(Domain.Entities.Nature)
                },
                (records) =>
                {
                    var reading = records[0] as Domain.Entities.Reading;
                    var instrument = records[1] as Domain.Entities.Instrument;
                    var structure = records[2] as Domain.Entities.Structure;
                    var readingValue = records[3] as Domain.Entities.ReadingValue;

                    if (!lookup.TryGetValue(reading.Id, out var readingEntry))
                    {
                        readingEntry = reading;
                        instrument.Structure = structure;
                        readingEntry.Instrument = instrument;

                        lookup.Add(readingEntry.Id, readingEntry);
                    }

                    if (records[4] is Measurement measurement)
                    {
                        readingValue.Measurement = measurement;
                    }

                    if (records[5] is Domain.Entities.Nature nature)
                    {
                        readingValue.Nature = nature;
                    }

                    readingEntry.Values.Add(readingValue);

                    return readingEntry;
                },
                splitOn: "Id,Id,Id,Id,Id,Id",
                param: template.Parameters
            );

            return lookup.Values.ToList();
        }

        public async Task<List<GetReadingStatsResponse>> GetStatsAsync(GetReadingStatsRequest request)
        {
            var @params = request.Instruments?.Select(x => new
            {
                x.InstrumentId,
                x.MeasurementId,
                request.StartDate,
                request.EndDate,
                request.ReadingStatisticalMeasure,
                Max = ReadingStatisticalMeasure.Maximum,
                Min = ReadingStatisticalMeasure.Minimum,
                Avg = ReadingStatisticalMeasure.Average,
                Med = ReadingStatisticalMeasure.Median,
                ElectricPiezometerType = InstrumentType.ElectricPiezometer
            });

            var resultList = new List<GetReadingStatsResponse>();

            var sqlBuilder = new SqlBuilder()
                .Where("[readings].[instrument-id] = @InstrumentId");

            if (request.StartDate.HasValue && request.EndDate.HasValue)
            {
                sqlBuilder.Where("[reading-values].[date] BETWEEN @StartDate AND @EndDate");
            }

            var sqlTemplate = sqlBuilder.AddTemplate(Queries.GetStats);

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            foreach (var param in @params)
            {
                if (param.MeasurementId.HasValue)
                {
                    sqlBuilder.Where("[reading-values].[measurement-id] = @MeasurementId");
                }

                var result = await connection.QueryAsync(
                sql: sqlTemplate.RawSql,
                new[]
                {
                    typeof(Guid),
                    typeof(Guid?),
                    typeof(double)
                },
                (records) =>
                {
                    var instrumentId = (Guid)records[0];
                    var measurementId = (Guid?)records[1];
                    var value = (double)records[2];

                    return new GetReadingStatsResponse()
                    {
                        InstrumentId = instrumentId,
                        MeasurementId = measurementId,
                        Quota = value
                    };
                },
                splitOn: "InstrumentId,MeasurementId,Value",
                param: param);

                resultList.AddRange(result);
            }

            return resultList;
        }

        public async Task<GetReadingByIdResponse> GetByIdAsync(Guid id)
        {
            var lookup = new Dictionary<Guid, GetReadingByIdResponse>();

            var lookupReadingValues = new Dictionary<Guid, ReadingValueResponse>();

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            await connection.QueryAsync(
                sql: Queries.GetByIdWithSections,
                new[]
                {
                    typeof(GetReadingByIdResponse),
                    typeof(Model._Shared.Instrument.Instrument),
                    typeof(ReadingValueResponse),
                    typeof(Model._Shared.Instrument.Measurement),
                    typeof(Model._Shared.SecurityLevels.SecurityLevels),
                    typeof(Model._Shared.Nature.Nature),
                    typeof(GetReadingByIdResponse),
                    typeof(Model._Shared.Instrument.Instrument),
                    typeof(ReadingValueResponse),
                    typeof(Model._Shared.Instrument.Measurement),
                    typeof(Model._Shared.Nature.Nature),
                    typeof(Model._Shared.Section.Section)
                },
                (records) =>
                {
                    var reading = records[0] as GetReadingByIdResponse;
                    var instrument = records[1] as Model._Shared.Instrument.Instrument;
                    var readingValue = records[2] as ReadingValueResponse;

                    var referenceInstrument = records[6] as Model._Shared.Instrument.Instrument;
                    var referenceReadingValue = records[8] as ReadingValueResponse;

                    if (!lookup.TryGetValue(reading.Id, out var readingEntry))
                    {
                        readingEntry = reading;

                        readingEntry.Instrument = instrument;

                        if (records[6] is GetReadingByIdResponse referenceReading)
                        {
                            referenceReading.Instrument = referenceInstrument;

                            readingEntry.ReferenceReading = referenceReading;
                        }

                        lookup.Add(readingEntry.Id, readingEntry);
                    }

                    if (!lookupReadingValues.TryGetValue(readingValue.Id.Value, out var readingValueEntry))
                    {
                        readingValueEntry = readingValue;
                        lookupReadingValues.Add(readingValueEntry.Id.Value, readingValueEntry);
                    }

                    if (records[3] is Model._Shared.Instrument.Measurement measurement)
                    {
                        readingValueEntry.Measurement = measurement;
                    }

                    if (records[4] is Model._Shared.SecurityLevels.SecurityLevels securityLevel
                        && !readingValueEntry.SecurityLevels.Exists(x => x.Id == securityLevel.Id))
                    {
                        readingValueEntry.SecurityLevels.Add(securityLevel);
                    }

                    if (records[5] is Model._Shared.Nature.Nature nature)
                    {
                        readingValueEntry.Nature = nature;
                    }

                    if (!readingEntry.Values.Exists(rv => rv.Id == readingValueEntry.Id))
                    {
                        readingEntry.Values.Add(readingValueEntry);
                    }

                    if (records[9] is Model._Shared.Instrument.Measurement refMeasurement)
                    {
                        referenceReadingValue.Measurement = refMeasurement;
                    }

                    if (records[10] is Model._Shared.Nature.Nature refNature)
                    {
                        referenceReadingValue.Nature = refNature;
                    }

                    if (readingEntry.ReferenceReading != null
                        && readingEntry.ReferenceReading.Values.TrueForAll(rv => rv.Id != referenceReadingValue.Id))
                    {
                        readingEntry.ReferenceReading.Values.Add(referenceReadingValue);
                    }

                    if (records[11] is Model._Shared.Section.Section section
                        && !readingEntry.Sections.Exists(x => x.Id == section.Id))
                    {
                        readingEntry.Sections.Add(section);
                    }

                    return readingEntry;
                },
                splitOn: "Id,Id,Id,Id,Id,Id,Id,Id,Id,Id,Id",
                param: new
                {
                    id
                });

            return lookup.Values.FirstOrDefault();
        }

        public async Task<bool> CheckIfIsUsedAsReferenceReading(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.CheckIfIsUsedAsReferenceReading, new
            {
                id
            });

            return res > 0;
        }

        public async Task<bool> BeachLengthExists(Guid sectionId, DateTime date)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.BeachLengthExists, new
            {
                sectionId,
                date
            });

            return res > 0;
        }

        public async Task<bool> BeachLengthExists(Guid sectionId, DateTime date, Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.BeachLengthExistsWithOtherId, new
            {
                sectionId,
                date,
                id
            });

            return res > 0;
        }
        
        public async Task<IEnumerable<ExistingReadingsResult>> GetExistingReadings(
            IEnumerable<ExistingReadingsQuery> parameters)
        {
            const string tempTableName = "#TempReadingExistsParams";
            
            if (parameters == null || !parameters.Any())
            {
                return Enumerable.Empty<ExistingReadingsResult>();
            }
            
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();
            
            try
            {
                await connection.ExecuteAsync(
                    Queries.CreateReadingsExistsParamsTempTable,
                    transaction: transaction);
                
                var columnMapper = new BulkColumnMapper()
                    .WithExistingReadingsQuery();
                
                await BulkInsertAsync(
                    data: parameters,
                    tableName: tempTableName,
                    columnMapper: columnMapper,
                    connection: connection,
                    transaction: (SqlTransaction) transaction);
                
                var results = await connection.QueryAsync<ExistingReadingsResult>(
                    Queries.GetExistingReadings, 
                    transaction: transaction);

                await connection.ExecuteAsync(
                    Queries.DropReadingsExistsParamsTempTable,
                    transaction: transaction);
                
                await transaction.CommitAsync();
                
                return results;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<bool> ReadingExists(DateTime date, Guid instrumentId, Guid? measurementId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.ReadingExists, new
            {
                instrumentId,
                date,
                measurementId
            });

            return res > 0;
        }

        public async Task<bool> ReadingExists(Guid readingValueId, DateTime date, Guid instrumentId, Guid? measurementId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.ReadingExistsWithOtherId, new
            {
                instrumentId,
                date,
                readingValueId,
                measurementId
            });

            return res > 0;
        }

        public async Task<List<Guid>> GetReadingValuesIdsByInstrumentIdAsync(Guid instrumentId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return (await connection.QueryAsync<Guid>(Queries.GetReadingValuesIdsByInstrumentId, new { instrumentId })).ToList();
        }

        public async Task<PaginationResponse> SearchReadingValueAsync(SearchReadingValueRequest request)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@Skip", request.Query.GetSkip());
            parameters.Add("@PageSize", request.Query.PageSize);

            var builder = new SqlBuilder()
                .Where("[readings].[is-deleted] = 0");

            if (request.Query.ClientId.HasValue)
            {
                parameters.Add("@ClientId", request.Query.ClientId);
                builder = builder
                    .Where("[client-units].[client-id] = @ClientId");
            }

            if (request.Query.ClientUnitId.HasValue)
            {
                parameters.Add("@ClientUnitId", request.Query.ClientUnitId);
                builder = builder
                    .Where("[structures].[client-unit-id] = @ClientUnitId");
            }

            if (request.Query.StructureId.HasValue)
            {
                parameters.Add("@StructureId", request.Query.StructureId);
                builder = builder
                    .Where("[structures].[id] = @StructureId");
            }

            if (request.Query.StartDate.HasValue &&
                request.Query.EndDate.HasValue)
            {
                parameters.Add("@StartDate", request.Query.StartDate);
                parameters.Add("@EndDate", request.Query.EndDate);
                builder = builder
                    .Where("[reading-values].[date] BETWEEN @StartDate AND @EndDate");
            } 
            else if (request.Query.StartDate.HasValue)
            {
                parameters.Add("@StartDate", request.Query.StartDate);
                builder = builder
                    .Where("[reading-values].[date] >= @StartDate");
            }
            else if (request.Query.EndDate.HasValue)
            {
                parameters.Add("@EndDate", request.Query.EndDate);
                builder = builder
                    .Where("[reading-values].[date] <= @EndDate");
            }

            if (request.Query.SearchIdentifier.HasValue)
            {
                parameters.Add("@SearchIdentifier", request.Query.SearchIdentifier);
                builder = builder
                    .Where("[readings].[search-identifier] = @SearchIdentifier");
            }

            if (request.Query.InstrumentType.HasValue)
            {
                parameters.Add("@InstrumentType", request.Query.InstrumentType);
                builder = builder
                    .Where("[instruments].[type] = @InstrumentType");
            }

            if (request.Body.InstrumentIds.Any())
            {
                parameters.Add("@InstrumentIds", request.Body.InstrumentIds);
                builder = builder
                    .Where("[instruments].[id] IN @InstrumentIds");
            }
            
            if (request.Body.SectionIds.Any())
            {
                parameters.Add("@SectionIds", request.Body.SectionIds);
                builder = builder
                    .Where("[section-instruments].[section-id] IN @SectionIds");
            }

            if (!request.RequestedBySuperSupport)
            {
                parameters.Add("@RequestedUserStructures", request.RequestedUserStructures);
                builder = builder
                    .Where("[instruments].[structure-id] IN @RequestedUserStructures");
            }

            var itemsQuery = builder.AddTemplate(Queries.SearchReadingValue);
            var countQuery = builder.AddTemplate(Queries.CountReadingValue);
            
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            
            var items = await connection.QueryAsync<SearchReadingValueResponse>(
                sql: itemsQuery.RawSql,
                new[]
                {
                    typeof(ReadingValue),
                    typeof(Domain.Entities.Reading),
                    typeof(Domain.Entities.Instrument),
                    typeof(Measurement),
                    typeof(bool),
                    typeof(Domain.Entities.Nature),
                    typeof(bool?)
                },
                (records) =>
                {
                    var readingValue = records[0] as ReadingValue;
                    var reading = records[1] as Domain.Entities.Reading;
                    var instrument = records[2] as Domain.Entities.Instrument;
                    var instrumentLinkedToAnySection = records[4] as bool?;
                    var isReferentialReading = records[6] as bool?;

                    return new()
                    {
                        Id = readingValue.Id,
                        IsReferentialReading = isReferentialReading,
                        RemovedFromLastPackage = readingValue.RemovedFromLastPackage,
                        ForcedCalculationInLastPackage = readingValue.ForcedCalculationInLastPackage,
                        ReadingId = reading.Id,
                        ReadingSearchIdentifier = reading.SearchIdentifier,
                        Instrument = new()
                        {
                            Id = instrument.Id,
                            Type = instrument.Type,
                            Identifier = instrument.Identifier
                        },
                        Measurement = records[3] is Measurement measurement
                        ? new()
                        {
                            Id = measurement.Id,
                            Identifier = measurement.Identifier
                        }
                        : null,
                        InstrumentLinkedToAnySection = (bool)instrumentLinkedToAnySection,
                        Date = readingValue.Date,
                        Quota = readingValue.Quota,
                        Depth = readingValue.Depth,
                        Pressure = readingValue.Pressure,
                        Dry = readingValue.Dry,
                        Pluviometry = readingValue.Pluviometry,
                        Intensity = readingValue.Intensity,
                        PositiveA = readingValue.PositiveA,
                        NegativeA = readingValue.NegativeA,
                        PositiveB = readingValue.PositiveB,
                        NegativeB = readingValue.NegativeB,
                        AverageDisplacementA = readingValue.AverageDisplacementA,
                        AverageDisplacementB = readingValue.AverageDisplacementB,
                        AccumulatedDisplacementA = readingValue.AccumulatedDisplacementA,
                        AccumulatedDisplacementB = readingValue.AccumulatedDisplacementB,
                        DeviationA = readingValue.DeviationA,
                        DeviationB = readingValue.DeviationB,
                        AAxisReading = readingValue.AAxisReading,
                        BAxisReading = readingValue.BAxisReading,
                        Datum = readingValue.Datum,
                        EastCoordinate = readingValue.EastCoordinate,
                        NorthCoordinate = readingValue.NorthCoordinate,
                        EastDisplacement = readingValue.EastDisplacement,
                        NorthDisplacement = readingValue.NorthDisplacement,
                        ZDisplacement = readingValue.ZDisplacement,
                        TotalPlanimetricDisplacement = readingValue.TotalPlanimetricDisplacement,
                        ADisplacement = readingValue.ADisplacement,
                        BDisplacement = readingValue.BDisplacement,
                        RelativeDepth = readingValue.RelativeDepth,
                        DeltaRef = readingValue.DeltaRef,
                        AbsoluteSettlement = readingValue.AbsoluteSettlement,
                        RelativeSettlement = readingValue.RelativeSettlement,
                        AAxisPga = readingValue.AAxisPga,
                        BAxisPga = readingValue.BAxisPga,
                        ZAxisPga = readingValue.ZAxisPga,
                        Nature = records[5] is Domain.Entities.Nature nature
                        ? new()
                        {
                            Id = nature.Id,
                            Description = nature.Description,
                        }
                        : null
                    };
                },
                splitOn: "Id,Id,Id,InstrumentLinkedToAnySection,Id,IsReferentialReading",
                param: parameters
                );
            
            if (!items.Any())
            {
                return new PaginationResponse()
                {
                    Data = Enumerable.Empty<SearchReadingValueResponse>(),
                };
            }

            var count = await connection.ExecuteScalarAsync<int>(
                countQuery.RawSql,
                parameters);

            return new PaginationResponse()
            {
                Data = items,
                CurrentItemsCount = items.Count(),
                TotalItemsCount = count
            };
        }

        public async Task UpdateAsync(Domain.Entities.Reading reading, bool shouldRecalculateStability)
        {
            var disableReferenceReadingParam = new
            {
                InstrumentId = reading.Instrument.Id
            };

            var updateReadingParam = new
            {
                reading.Id,
                reading.IsReferential
            };

            var updateReadingValueParams = reading.Values
                .Select(readingValue => new
                {
                    readingValue.Id,
                    readingValue.Date,
                    readingValue.Quota,
                    readingValue.Depth,
                    readingValue.Pluviometry,
                    readingValue.Intensity,
                    readingValue.Pressure,
                    readingValue.Dry,
                    readingValue.PositiveA,
                    readingValue.NegativeA,
                    readingValue.PositiveB,
                    readingValue.NegativeB,
                    readingValue.AverageDisplacementA,
                    readingValue.AverageDisplacementB,
                    readingValue.AccumulatedDisplacementA,
                    readingValue.AccumulatedDisplacementB,
                    readingValue.DeviationA,
                    readingValue.DeviationB,
                    readingValue.AAxisReading,
                    readingValue.BAxisReading,
                    readingValue.Datum,
                    readingValue.EastCoordinate,
                    readingValue.NorthCoordinate,
                    readingValue.EastDisplacement,
                    readingValue.NorthDisplacement,
                    readingValue.ZDisplacement,
                    readingValue.TotalPlanimetricDisplacement,
                    readingValue.ADisplacement,
                    readingValue.BDisplacement,
                    readingValue.RelativeDepth,
                    readingValue.DeltaRef,
                    readingValue.AbsoluteSettlement,
                    readingValue.RelativeSettlement,
                    NatureId = readingValue.Nature?.Id,
                    readingValue.AAxisPga,
                    readingValue.BAxisPga,
                    readingValue.ZAxisPga
                });

            var readingHistoryParams = reading.History
                .Select(history => new
                {
                    history.Id,
                    ReadingId = reading.Id,
                    history.Changes,
                    ModifiedByUserId = history.ModifiedBy.Id
                });

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                if (reading.IsReferential != null && (bool)reading.IsReferential)
                {
                    await connection
                        .ExecuteAsync(Queries.DisableReferenceReading, disableReferenceReadingParam, transaction);
                }

                await connection
                    .ExecuteAsync(Queries.Update, updateReadingParam, transaction);

                await connection.ExecuteAsync(Queries.InsertReadingHistory, readingHistoryParams, transaction);

                await connection
                    .ExecuteAsync(Queries.UpdateReadingValue, updateReadingValueParams, transaction);

                foreach (var readingValue in updateReadingValueParams)
                {
                    var dateChanged = await connection
                        .ExecuteScalarAsync<int>(Queries.CheckIfDateChanged, readingValue, transaction);

                    if (dateChanged == 0)
                    {
                        await connection.ExecuteAsync(OutboxQueries.Insert, new
                        {
                            Id = Guid.NewGuid(),
                            Type = typeof(ReadingValueDateChanged).AssemblyQualifiedName,
                            Data = JsonSerializer.Serialize(new
                            {
                                readingValue.Id
                            })
                        }, transaction);
                    }
                }

                await connection.ExecuteAsync(OutboxQueries.Insert, new
                {
                    Id = Guid.NewGuid(),
                    Type = typeof(ReadingUpdated).AssemblyQualifiedName,
                    Data = JsonSerializer.Serialize(new ReadingUpdated()
                    {
                        Id = reading.Id,
                        UserId = reading.History?.FirstOrDefault()?.ModifiedBy?.Id ?? Guid.Empty,
                        ShouldRecalculateStability = shouldRecalculateStability
                    })
                }, transaction);

                await connection.ExecuteAsync(OutboxQueries.Insert, new
                {
                    Id = Guid.NewGuid(),
                    Type = typeof(NotificationCommand).AssemblyQualifiedName,
                    Data = JsonSerializer.Serialize(new NotificationCommand()
                    {
                        Id = reading.Id,
                        Theme = NotificationTheme.ReadingUpdated,
                        CreatedById = reading.History.Any()
                        ? reading.History
                            .OrderByDescending(x => x.CreatedDate)
                            .FirstOrDefault()!.ModifiedBy.Id
                        : Guid.Empty,
                        CreatedDate = DateTime.Now
                    })
                }, transaction);

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task UnmarkReadingValues(List<Guid> readingValues)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                await connection.ExecuteAsync(Queries.UnmarkReadingValues, new
                {
                    readingValues
                }, transaction);

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task MarkReadingValuesAsForcedCalculation(List<Guid> readingValues)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                await connection.ExecuteAsync(Queries.MarkReadingValuesAsForcedCalculation, new
                {
                    readingValues
                }, transaction);

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<IEnumerable<ReadingHistoryResponse>> GetHistoryAsync(
            Guid sectionId,
            DateTime createdDate)
        {
            var param = new
            {
                SectionId = sectionId,
                CreatedDate = createdDate
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.QueryAsync(
                sql: Queries.GetReadingHistory,
                param: param,
                types: new[]
                {
                    typeof(ReadingHistoryResponse),
                    typeof(Model._Shared.User.User),
                    typeof(Model._Shared.Instrument.Instrument)
                },
                splitOn: "Id,Id",
                map: (records) =>
                {
                    var history = records[0] as ReadingHistoryResponse;
                    history.ModifiedBy = records[1] as Model._Shared.User.User;
                    history.Instrument = records[2] as Model._Shared.Instrument.Instrument;

                    return history;
                });
        }

        public async Task<SimpleReadingWithSections> GetByIdSimpleAsync(Guid id)
        {
            var lookup = new Dictionary<Guid, SimpleReadingWithSections>();

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            await connection.QueryAsync(
                sql: Queries.GetByIdWithSectionsSimple,
                param: new { Id = id },
                types: new[]
                {
                    typeof(SimpleReadingWithSections),
                    typeof(SimpleReadingValue),
                    typeof(Guid?)
                },
                splitOn: "Id,Id,Id",
                map: (records) =>
                {
                    var reading = records[0] as SimpleReadingWithSections;
                    var readingValue = records[1] as SimpleReadingValue;
                    var sectionId = records[2] as Guid?;

                    if (!lookup.TryGetValue(reading.Id, out var readingEntry))
                    {
                        readingEntry = reading;
                        lookup.Add(reading.Id, readingEntry);
                    }

                    if (!readingEntry.ReadingValues.Exists(value => value.Id == readingValue.Id))
                    {
                        readingEntry.ReadingValues.Add(readingValue);
                    }

                    if (!readingEntry.SectionIds.Contains(sectionId.Value))
                    {
                        readingEntry.SectionIds.Add(sectionId.Value);
                    }

                    return readingEntry;
                });

            return lookup.FirstOrDefault().Value;
        }

        public async Task<IEnumerable<Guid>> GetRelatedReadingValuesAsync(
            IEnumerable<Guid> sectionIds,
            DateTime maxDate,
            IEnumerable<InstrumentType> types)
        {
            var param = new
            {
                SectionIds = sectionIds,
                Date = maxDate,
                InstrumentTypes = types
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var ids = await connection.QueryAsync<Guid?>(Queries.GetRelatedReadingValues, param);

            return ids
                .Where(id => id.HasValue)
                .Select(id => id.Value);
        }

        public Task UpdateAsync(Domain.Entities.Reading item)
        {
            throw new NotImplementedException();
        }
    }
}
