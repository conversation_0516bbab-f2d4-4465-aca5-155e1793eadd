using Coordinate.Core;
using Coordinate.Core.Classes;
using Coordinate.Core.Enums;
using Dapper;
using Database.Repositories.Outbox;
using Domain.Entities;
using Domain.Enums;
using Domain.ValueObjects;
using Model.Core.Search.Pagination;
using Model.Instrument.GetAutomatedReadingsData.Request;
using Model.Instrument.GetAutomatedReadingsData.Response;
using Model.Instrument.GetByFiltersMaps.Request;
using Model.Instrument.GetByFiltersMaps.Response;
using Model.Instrument.GetFile.Request;
using Model.Instrument.GetSection.Response;
using Model.Instrument.List.Request;
using Model.Instrument.List.Response;
using Model.Instrument.Search.Request;
using Model.Instrument.Search.Response;
using Model.Instrument.SearchGroup.Request;
using Model.Instrument.SearchGroup.Response;
using Model.Instrument.SearchHistory.Request;
using Model.Instrument.SearchHistory.Response;
using Model.Instrument.SearchNote.Request;
using Model.Instrument.SearchNoteHistory.Request;
using Model.Instrument.SearchNoteHistory.Response;
using Model.Instrument.SearchSecurityLevelAlert.Request;
using Model.Instrument.SearchSecurityLevelAlert.Response;
using Model.Maps._Shared.DisplacementInstrumentMapData;
using Model.Maps._Shared.PercolationInstrumentMapData;
using Model.Maps._Shared.StabilityInstrumentMapData;
using Model.Maps.GetDisplacementMap.Request;
using Model.Maps.GetPercolationMap.Request;
using Model.Maps.GetStabilityMapByStructureId.Request;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Model.Dashboard.GetInstrumentMetrics.Request;
using Model.Dashboard.GetInstrumentMetrics.Response;
using NotificationCommand = Domain.Messages.Commands.Notification.CreateNotification;


namespace Database.Repositories.Instrument
{
    public class InstrumentRepository : IInstrumentRepository
    {
        private readonly string _connectionString;

        public InstrumentRepository(string connectionString)
        {
            _connectionString = connectionString;
        }

        public async Task UpdateGroupAsync(Domain.Entities.InstrumentGroup group)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                var groupParam = new
                {
                    group.Id,
                    group.Name
                };

                await connection
                   .ExecuteAsync(Queries.UpdateGroup, groupParam, transaction);

                foreach (var instrument in group.Instruments)
                {
                    var instrumentParam = new
                    {
                        InstrumentId = instrument.Id,
                        InstrumentGroupId = group.Id
                    };

                    var instrumentGroupExists = await connection.QueryFirstAsync<int>(
                        Queries.CheckIfTheInstrumentGroupExists, instrumentParam,
                        transaction);

                    if (instrumentGroupExists == 0)
                    {
                        await connection
                            .ExecuteAsync(Queries.InsertGroupInstrument, instrumentParam, transaction);
                    }
                }

                await connection.ExecuteAsync(Queries.DeleteGroupInstrument,
                    new
                    {
                        InstrumentGroupId = group.Id,
                        InstrumentIds = group.Instruments.Select(x => x.Id).ToList()
                    }, transaction);

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<bool> CheckIfIsInAnySectionAsync(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            var res = await connection
                .ExecuteScalarAsync<int>(Queries.CheckIfInstrumentIsInAnySection, new { id });

            return res > 0;
        }

        public async Task<bool> CheckIfStructureAlreadyHasLinimetricRulerAsync(Guid structureId, LinimetricRulerPosition position)
        {
            var param = new
            {
                StructureId = structureId,
                Position = position,
                Type = InstrumentType.LinimetricRuler
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            var res = await connection
                .ExecuteScalarAsync<int>(Queries.CheckIfStructureAlreadyHasLimetricRuler, param);

            return res > 0;
        }

        public async Task<bool> CheckIfStructureAlreadyHasLinimetricRulerAsync(Guid structureId, Guid instrumentId, LinimetricRulerPosition position)
        {
            var param = new
            {
                StructureId = structureId,
                InstrumentId = instrumentId,
                Position = position,
                Type = InstrumentType.LinimetricRuler
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            var res = await connection
                .ExecuteScalarAsync<int>(Queries.CheckIfStructureAlreadyHasOtherLimetricRuler, param);

            return res > 0;
        }

        public async Task DeleteGroupAsync(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                await connection.ExecuteAsync(Queries.DeleteGroup, new { Id = id }, transaction);
                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<Domain.Entities.InstrumentGroup> GetGroupAsync(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var lookup = new Dictionary<Guid, Domain.Entities.InstrumentGroup>();

            await connection.QueryAsync(
                sql: Queries.GetGroupById,
                new[]
                {
                    typeof(Domain.Entities.InstrumentGroup),
                    typeof(Domain.Entities.Instrument)
                },
                (records) =>
                {
                    var groupDb = records[0] as Domain.Entities.InstrumentGroup;
                    var instrument = records[1] as Domain.Entities.Instrument;

                    if (!lookup.TryGetValue(groupDb.Id, out var group))
                    {
                        lookup.Add(groupDb.Id, groupDb);
                        group = groupDb;
                    }

                    group.AddInstrument(instrument);

                    return group;
                },
                splitOn: "Id",
                param: new { Id = id }
                );

            return lookup.Values.FirstOrDefault();
        }

        public async Task<int> CountSecurityLevelAlertAsync(SearchInstrumentSecurityLevelAlertRequest request)
        {
            var param = new
            {
                request.InstrumentId,
                request.RequestedUserStructures,
                request.RequestedBySuperSupport
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.ExecuteScalarAsync<int>(Queries.CountSecurityLevelAlerts, param);
        }

        public async Task<int> CountGroupAsync(SearchInstrumentGroupRequest request)
        {
            var param = new
            {
                request.StructureId,
                request.Name,
                request.RequestedUserStructures,
                request.RequestedBySuperSupport
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.ExecuteScalarAsync<int>(Queries.CountGroup, param);
        }

        public async Task<List<GetInstrumentSectionResponse>> GetSectionsAsync(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return (await connection.QueryAsync<GetInstrumentSectionResponse>(Queries.GetSections, new
            {
                id
            })).ToList();
        }

        public async Task<List<Domain.Entities.Instrument>> GetByTypeAndSectionAsync(List<InstrumentType> types, Guid sectionId)
        {
            var param = new
            {
                types,
                sectionId
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var lookup = new Dictionary<Guid, Domain.Entities.Instrument>();

            var securityLevelsDictionary = new Dictionary<Guid, Domain.Entities.SecurityLevels>();

            await connection.QueryAsync(
                sql: Queries.GetByTypesAndSection,
                new[]
                {
                     typeof(Domain.Entities.Instrument),
                     typeof(SecurityLevels),
                     typeof(CoordinateSetting),
                     typeof(DecimalGeodetic),
                     typeof(Utm),
                     typeof(Domain.Entities.Client),
                     typeof(Domain.Entities.ClientUnit),
                     typeof(Domain.Entities.Structure),
                     typeof(Measurement)
                },
                (records) => BindValues(records, lookup, securityLevelsDictionary),
                splitOn: "Id,Datum,Latitude,Northing,Id,Id,Id,Id",
                param: param);

            foreach (var instrument in lookup.Values)
            {
                if (instrument.Measurements.Any())
                {
                    foreach (var measurement in instrument.Measurements)
                    {
                        var measurementSecurityLevel = securityLevelsDictionary.Values.FirstOrDefault(x => x.MeasurementId == measurement.Id);

                        if (measurementSecurityLevel != null)
                        {
                            measurement.SecurityLevels = measurementSecurityLevel;
                        }
                    }

                    continue;
                }

                var securityLevel = securityLevelsDictionary
                    .Values
                    .Where(x => x.InstrumentId == instrument.Id)
                    .ToList();

                if (securityLevel != null)
                {
                    instrument.SecurityLevels = securityLevel;
                }
            }

            return lookup.Values.ToList();
        }   

        public async Task<List<Domain.Entities.Instrument>> GetByTypeAndStructureAsync(InstrumentType type, Guid structureId)
        {
            var param = new
            {
                Type = type,
                StructureId = structureId
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var lookup = new Dictionary<Guid, Domain.Entities.Instrument>();

            var securityLevelsDictionary = new Dictionary<Guid, Domain.Entities.SecurityLevels>();

            await connection.QueryAsync(
                sql: Queries.GetByType,
                new[]
                {
                     typeof(Domain.Entities.Instrument),
                     typeof(Domain.Entities.SecurityLevels),
                     typeof(CoordinateSetting),
                     typeof(DecimalGeodetic),
                     typeof(Utm),
                     typeof(Domain.Entities.Client),
                     typeof(Domain.Entities.ClientUnit),
                     typeof(Domain.Entities.Structure),
                     typeof(Measurement)
                },
                (records) => BindValues(records, lookup, securityLevelsDictionary),
                splitOn: "Id,Datum,Latitude,Northing,Id,Id,Id,Id",
                param: param);

            foreach (var instrument in lookup.Values)
            {
                if (instrument.Measurements.Any())
                {
                    foreach (var measurement in instrument.Measurements)
                    {
                        var measurementSecurityLevel = securityLevelsDictionary.Values.FirstOrDefault(x => x.MeasurementId == measurement.Id);

                        if (measurementSecurityLevel != null)
                        {
                            measurement.SecurityLevels = measurementSecurityLevel;
                        }
                    }

                    continue;
                }

                var securityLevel = securityLevelsDictionary
                    .Values
                    .Where(x => x.InstrumentId == instrument.Id)
                    .ToList();

                if (securityLevel != null)
                {
                    instrument.SecurityLevels = securityLevel;
                }
            }

            return lookup.Values.ToList();
        }

        public async Task<List<GetInstrumentByFiltersMapsResponse>> GetByFiltersMapsAsync(GetInstrumentByFiltersMapsRequest request)
        {
            var param = new
            {
                request.StructureId,
                request.Subtype,
                request.RequestedUserStructures,
                request.RequestedBySuperSupport
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return (await connection.QueryAsync(
                sql: Queries.GetByFiltersMaps,
                new[]
                {
                    typeof(Domain.Entities.Instrument),
                    typeof(CoordinateSetting),
                    typeof(DecimalGeodetic),
                },
                (records) =>
                {
                    var instrumentDb = records[0] as Domain.Entities.Instrument;
                    var coordinateSetting = records[1] as CoordinateSetting;
                    var decimalGeodetic = records[2] as DecimalGeodetic;

                    if (coordinateSetting.Datum != Coordinate.Core.Enums.Datum.SIRGAS2000)
                    {
                        decimalGeodetic = Helper.ToDatum(coordinateSetting.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, decimalGeodetic);
                    }

                    var instrument = new GetInstrumentByFiltersMapsResponse()
                    {
                        Id = instrumentDb.Id,
                        Identifier = instrumentDb.Identifier,
                        Type = instrumentDb.Type,
                        DecimalGeodetic = decimalGeodetic,
                        Online = instrumentDb.Online
                    };

                    return instrument;
                },
                splitOn: "Id,Datum,Latitude",
                param: param
                )).ToList();
        }

        public async Task<List<Domain.Entities.Instrument>> GetByIdsAsync(
            IEnumerable<Guid> ids,
            Guid? structureId = null,
            bool? onlyActive = null)
        {
            var builder = new SqlBuilder()
                .Where("[instruments].[id] IN @Ids", new { Ids = ids });
            
            if (structureId.HasValue)
            {
                builder.Where("[instruments].[structure-id] = @StructureId", new { StructureId = structureId.Value });
            }
            
            if (onlyActive.HasValue && onlyActive.Value)
            {
                builder.Where("[instruments].[active] = 1");
            }
            
            var template = new SqlBuilder()
                .AddTemplate(Queries.GetByIdSimple);

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var result = await connection.QueryAsync<Domain.Entities.Instrument>(
                sql: template.RawSql,
                param: template.Parameters);

            return result.ToList();
        }

        public async Task<List<SearchInstrumentGroupResponse>> SearchGroupAsync(SearchInstrumentGroupRequest request)
        {
            var param = new
            {
                request.StructureId,
                request.Name,
                request.RequestedUserStructures,
                request.RequestedBySuperSupport,
                Skip = request.GetSkip(),
                request.PageSize
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var lookup = new Dictionary<Guid, SearchInstrumentGroupResponse>();

            await connection.QueryAsync(
                sql: Queries.SearchGroup,
                new[]
                {
                    typeof(Domain.Entities.InstrumentGroup),
                    typeof(Domain.Entities.Instrument)
                },
                (records) =>
                {
                    var groupDb = records[0] as Domain.Entities.InstrumentGroup;
                    var instrument = records[1] as Domain.Entities.Instrument;

                    if (!lookup.TryGetValue(groupDb.Id, out var group))
                    {
                        var groupModel = new SearchInstrumentGroupResponse()
                        {
                            Id = groupDb.Id,
                            Name = groupDb.Name,
                            Instruments = new()
                        };

                        lookup.Add(groupDb.Id, groupModel);

                        group = groupModel;
                    }

                    group.Instruments.Add(new()
                    {
                        Id = instrument.Id,
                        Identifier = instrument.Identifier,
                        Type = instrument.Type
                    });

                    return group;
                },
                splitOn: "Id",
                param: param
                );

            return lookup.Values.ToList();
        }

        public async Task<List<Guid>> GetStructureOfInstrumentsAsync(List<Guid> instrumentsIds)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return (await connection.QueryAsync<Guid>(Queries.GetStructureOfInstruments, new
            {
                InstrumentsIds = instrumentsIds
            })).ToList();
        }

        public async Task AddGroupAsync(Domain.Entities.InstrumentGroup group)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                var groupParam = new
                {
                    group.Id,
                    group.Name
                };

                await connection
                   .ExecuteAsync(Queries.InsertGroup, groupParam, transaction);

                foreach (var instrument in group.Instruments)
                {
                    var instrumentParam = new
                    {
                        InstrumentId = instrument.Id,
                        InstrumentGroupId = group.Id
                    };

                    await connection
                        .ExecuteAsync(Queries.InsertGroupInstrument, instrumentParam, transaction);
                }

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<int> CountNoteAsync(SearchInstrumentNoteRequest request)
        {
            var param = new
            {
                request.InstrumentId,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.ExecuteScalarAsync<int>(Queries.CountNote, param);
        }

        public async Task<int> CountNoteHistoryAsync(SearchInstrumentNoteHistoryRequest request)
        {
            var param = new
            {
                request.NoteId,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.ExecuteScalarAsync<int>(Queries.CountNoteHistory, param);
        }

        public async Task<int> CountHistoryAsync(SearchInstrumentHistoryRequest request)
        {
            var param = new
            {
                request.InstrumentId,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.ExecuteScalarAsync<int>(Queries.CountHistory, param);
        }

        public async Task<IEnumerable<SearchInstrumentHistoryResponse>> SearchHistoryAsync(SearchInstrumentHistoryRequest request)
        {
            var param = new
            {
                request.InstrumentId,
                Skip = request.GetSkip(),
                request.PageSize,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.QueryAsync<SearchInstrumentHistoryResponse>(
                sql: Queries.SearchHistory,
                new[]
                {
                    typeof(Domain.Entities.InstrumentHistory),
                    typeof(Domain.Entities.User)
                },
                (records) =>
                {
                    var history = records[0] as Domain.Entities.InstrumentHistory;
                    var user = records[1] as Domain.Entities.User;

                    return new()
                    {
                        Id = history.Id,
                        Changes = history.Changes,
                        DisplayInCharts = history.DisplayInCharts,
                        CreatedDate = history.CreatedDate,
                        ModifiedBy = new()
                        {
                            Id = user.Id,
                            FirstName = user.FirstName,
                            Surname = user.Surname,
                            Username = user.Username
                        }
                    };
                },
                splitOn: "Id",
                param: param
                );
        }

        public async Task<IEnumerable<SearchInstrumentNoteHistoryResponse>> SearchNoteHistoryAsync(SearchInstrumentNoteHistoryRequest request)
        {
            var param = new
            {
                request.NoteId,
                Skip = request.GetSkip(),
                request.PageSize,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.QueryAsync<SearchInstrumentNoteHistoryResponse>(
                sql: Queries.SearchNoteHistory,
                new[]
                {
                    typeof(Domain.Entities.InstrumentNoteHistory),
                    typeof(Domain.Entities.User)
                },
                (records) =>
                {
                    var history = records[0] as Domain.Entities.InstrumentNoteHistory;
                    var user = records[1] as Domain.Entities.User;

                    return new()
                    {
                        Id = history.Id,
                        Changes = history.Changes,
                        CreatedDate = history.CreatedDate,
                        ModifiedBy = new()
                        {
                            Id = user.Id,
                            FirstName = user.FirstName,
                            Surname = user.Surname,
                            Username = user.Username
                        }
                    };
                },
                splitOn: "Id",
                param: param
                );
        }

        public async Task<IEnumerable<Domain.Entities.InstrumentNote>> SearchNoteAsync(SearchInstrumentNoteRequest request)
        {
            var param = new
            {
                request.InstrumentId,
                Skip = request.GetSkip(),
                request.PageSize,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.QueryAsync(
                sql: Queries.SearchNote,
                new[]
                {
                    typeof(Domain.Entities.InstrumentNote),
                    typeof(File),
                    typeof(File),
                    typeof(Domain.Entities.User)
                },
                (records) =>
                {
                    var note = records[0] as Domain.Entities.InstrumentNote;
                    var image = records[1] as File;
                    var file = records[2] as File;
                    var user = records[3] as Domain.Entities.User;

                    note.Image = image;
                    note.File = file;
                    note.CreatedBy = user;

                    return note;
                },
                splitOn: "Name,Name,Id",
                param: param);
        }

        public async Task UpdateHistoryAsync(Domain.Entities.InstrumentHistory history)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.ExecuteAsync(Queries.UpdateHistory, new
            {
                history.Id,
                history.DisplayInCharts
            });
        }

        public async Task<Domain.Entities.InstrumentHistory> GetHistoryAsync(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var history = await connection.QueryAsync(
                sql: Queries.GetHistoryById,
                new[]
                {
                    typeof(Domain.Entities.InstrumentHistory),
                    typeof(Domain.Entities.User)
                },
                (records) =>
                {
                    var history = records[0] as Domain.Entities.InstrumentHistory;
                    var user = records[1] as Domain.Entities.User;

                    history.ModifiedBy = user;

                    return history;
                },
                splitOn: "Id",
                param: new { Id = id }
                );

            return history.FirstOrDefault();
        }

        public async Task<Domain.Entities.InstrumentNote> GetNoteAsync(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var note = await connection.QueryAsync(
                sql: Queries.GetNoteById,
                new[]
                {
                    typeof(Domain.Entities.InstrumentNote),
                    typeof(File),
                    typeof(File),
                    typeof(Domain.Entities.User)
                },
                (records) =>
                {
                    var note = records[0] as Domain.Entities.InstrumentNote;
                    var image = records[1] as File;
                    var file = records[2] as File;
                    var user = records[3] as Domain.Entities.User;

                    note.Image = image;
                    note.File = file;
                    note.CreatedBy = user;

                    return note;
                },
                splitOn: "Name,Name,Id",
                param: new { Id = id });

            return note.FirstOrDefault();
        }

        public async Task UpdateNoteAsync(Domain.Entities.InstrumentNote note)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                var param = new
                {
                    note.Id,
                    note.Description,
                    ImageUniqueName = note.Image?.UniqueName,
                    ImageName = note.Image?.Name,
                    FileUniqueName = note.File?.UniqueName,
                    FileName = note.File?.Name,
                    note.DisplayInCharts,
                    note.Type
                };

                await connection.ExecuteAsync(Queries.UpdateNote, param, transaction);

                foreach (var history in note.History)
                {
                    var historyParam = new
                    {
                        history.Id,
                        InstrumentNoteId = history.InstrumentNote.Id,
                        history.Changes,
                        ModifiedByUserId = history.ModifiedBy.Id,
                    };

                    await connection.ExecuteAsync(Queries.InsertNoteHistory, historyParam, transaction);
                }

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task AddNoteAsync(Domain.Entities.InstrumentNote note)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                var param = new
                {
                    note.Id,
                    InstrumentId = note.Instrument.Id,
                    note.Description,
                    CreatedBy = note.CreatedBy.Id,
                    ImageUniqueName = note.Image?.UniqueName,
                    ImageName = note.Image?.Name,
                    FileUniqueName = note.File?.UniqueName,
                    FileName = note.File?.Name,
                    note.DisplayInCharts,
                    note.Type
                };

                await connection.ExecuteAsync(Queries.InsertNote, param, transaction);

                foreach (var history in note.History)
                {
                    var historyParam = new
                    {
                        history.Id,
                        InstrumentNoteId = history.InstrumentNote.Id,
                        history.Changes,
                        ModifiedByUserId = history.ModifiedBy.Id,
                    };

                    await connection.ExecuteAsync(Queries.InsertNoteHistory, historyParam, transaction);
                }

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public Task UpdateAsync(Domain.Entities.Instrument instrument)
        {
            throw new NotImplementedException();
        }

        public async Task UpdateAsync(List<Domain.Entities.Instrument> instruments)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                foreach (var instrument in instruments)
                {
                    var param = new
                    {
                        instrument.Id,
                        instrument.Type,
                        instrument.Subtype,
                        instrument.Identifier,
                        instrument.AlternativeName,
                        instrument.CoordinateSetting.Systems.DecimalGeodetic.Latitude,
                        instrument.CoordinateSetting.Systems.DecimalGeodetic.Longitude,
                        instrument.CoordinateSetting.Systems.Utm.Northing,
                        instrument.CoordinateSetting.Systems.Utm.Easting,
                        instrument.CoordinateSetting.Systems.Utm.ZoneNumber,
                        instrument.CoordinateSetting.Systems.Utm.ZoneLetter,
                        instrument.CoordinateSetting.Datum,
                        instrument.CoordinateSetting.Format,
                        instrument.TopQuota,
                        instrument.Depth,
                        instrument.BaseQuota,
                        instrument.MeasurementFrequency,
                        instrument.UpperLimit,
                        instrument.LowerLimit,
                        instrument.Azimuth,
                        instrument.Automated,
                        instrument.Online,
                        instrument.GeophoneType,
                        instrument.LinimetricRulerPosition,
                        instrument.Elevation,
                        instrument.ResponsibleForInstallation,
                        instrument.InstallationDate,
                        instrument.Model,
                        instrument.DryType
                    };

                    await connection.ExecuteAsync(Queries.Update, param, transaction);

                    if (instrument.SecurityLevels != null)
                    {
                        foreach (var securityLevel in instrument.SecurityLevels)
                        {
                            var securityLevelParam = new
                            {
                                InstrumentId = instrument.Id,
                                MeasurementId = instrument.Measurements.FirstOrDefault()?.Id,
                                securityLevel.Id,
                                securityLevel.Attention,
                                securityLevel.Alert,
                                securityLevel.Emergency,
                                securityLevel.AbruptVariation,
                                securityLevel.MaximumDailyRainfall,
                                securityLevel.RainIntensity,
                                securityLevel.Axis
                            };

                            await connection
                                .ExecuteAsync(Queries.UpsertSecurityLevel, securityLevelParam, transaction);
                        }
                    }

                    foreach (var measurement in instrument.Measurements)
                    {
                        var measurementParam = new
                        {
                            measurement.Id,
                            InstrumentId = instrument.Id,
                            measurement.Identifier,
                            measurement.Active,
                            measurement.AlternativeName,
                            measurement.Quota,
                            measurement.Lithotype,
                            measurement.Limit,
                            measurement.DeltaRef,
                            measurement.IsReferential,
                            measurement.Depth,
                            measurement.Length
                        };

                        await connection
                            .ExecuteAsync(Queries.UpsertMeasurement, measurementParam, transaction);

                        if (measurement.SecurityLevels != null)
                        {
                            var securityLevelParam = new
                            {
                                InstrumentId = instrument.Id,
                                MeasurementId = measurement.Id,
                                measurement.SecurityLevels?.Id,
                                measurement.SecurityLevels?.Attention,
                                measurement.SecurityLevels?.Alert,
                                measurement.SecurityLevels?.Emergency,
                                measurement.SecurityLevels?.AbruptVariation,
                                measurement.SecurityLevels?.MaximumDailyRainfall,
                                measurement.SecurityLevels?.RainIntensity,
                                measurement.SecurityLevels?.Axis
                            };

                            await connection
                                .ExecuteAsync(Queries.UpsertSecurityLevel, securityLevelParam, transaction);
                        }
                    }

                    var securityLevelsIds = instrument.SecurityLevels?
                        .Select(x => x?.Id)
                        .Concat(instrument.Measurements.Select(x => x.SecurityLevels?.Id))
                        .Where(x => x.HasValue)
                        .ToList();

                    await connection.ExecuteAsync(Queries.DeleteSecurityLevels, new
                    {
                        InstrumentId = instrument.Id,
                        SecurityLevelsIds = securityLevelsIds
                    }, transaction);

                    foreach (var history in instrument.History)
                    {
                        var historyParam = new
                        {
                            history.Id,
                            InstrumentId = instrument.Id,
                            history.Changes,
                            ModifiedByUserId = history.ModifiedBy.Id
                        };

                        await connection.ExecuteAsync(Queries.InsertInstrumentHistory, historyParam, transaction);
                    }

                    await connection.ExecuteAsync(OutboxQueries.Insert, new
                    {
                        Id = Guid.NewGuid(),
                        Type = typeof(NotificationCommand).AssemblyQualifiedName,
                        Data = JsonSerializer.Serialize(new NotificationCommand()
                        {
                            Id = instrument.Id,
                            Theme = NotificationTheme.InstrumentUpdated,
                            CreatedById = instrument.History
                                .OrderByDescending(x => x.CreatedDate)
                                .FirstOrDefault().ModifiedBy.Id,
                            CreatedDate = DateTime.Now
                        })
                    }, transaction);
                }

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task AddSecurityLevelAlertAsync(IEnumerable<SecurityLevelAlert> alerts)
        {
            var securityLevelParam = alerts
                .Select(alert => new
                {
                    alert.Id,
                    InstrumentId = alert.Instrument.Id,
                    MeasurementId = alert.Measurement?.Id,
                    alert.SecurityLevel,
                    alert.Difference,
                    alert.ReadingValueDate,
                    alert.DisplacedAxis
                });

            var outboxParam = alerts
                .Select(alert => new
                {
                    Id = Guid.NewGuid(),
                    Type = typeof(NotificationCommand).AssemblyQualifiedName,
                    Data = JsonSerializer.Serialize(new NotificationCommand()
                    {
                        Id = alert.Instrument.Id,
                        Theme = NotificationTheme.ControlLetterNewRegistry,
                        CreatedDate = DateTime.UtcNow
                    })
                });

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                await connection.ExecuteAsync(Queries.InsertSecurityLevelAlert, securityLevelParam, transaction);
                await connection.ExecuteAsync(OutboxQueries.Insert, outboxParam, transaction);
                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task AddAsync(List<Domain.Entities.Instrument> instruments)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                foreach (var instrument in instruments)
                {
                    var param = new
                    {
                        instrument.Id,
                        instrument.Type,
                        instrument.Subtype,
                        instrument.UpperLimit,
                        instrument.LowerLimit,
                        instrument.MeasurementFrequency,
                        StructureId = instrument.Structure.Id,
                        instrument.Identifier,
                        instrument.AlternativeName,
                        instrument.CoordinateSetting.Systems.DecimalGeodetic.Latitude,
                        instrument.CoordinateSetting.Systems.DecimalGeodetic.Longitude,
                        instrument.CoordinateSetting.Systems.Utm.Northing,
                        instrument.CoordinateSetting.Systems.Utm.Easting,
                        instrument.CoordinateSetting.Systems.Utm.ZoneNumber,
                        instrument.CoordinateSetting.Systems.Utm.ZoneLetter,
                        instrument.CoordinateSetting.Datum,
                        instrument.CoordinateSetting.Format,
                        instrument.TopQuota,
                        instrument.Depth,
                        instrument.BaseQuota,
                        instrument.Azimuth,
                        instrument.Automated,
                        instrument.Online,
                        instrument.GeophoneType,
                        instrument.LinimetricRulerPosition,
                        instrument.Elevation,
                        instrument.ResponsibleForInstallation,
                        instrument.InstallationDate,
                        instrument.Model,
                        instrument.DryType
                    };

                    await connection
                        .ExecuteAsync(Queries.Insert, param, transaction);

                    if (instrument.SecurityLevels != null)
                    {
                        foreach (var securityLevel in instrument.SecurityLevels)
                        {
                            var securityLevelParam = new
                            {
                                InstrumentId = instrument.Id,
                                MeasurementId = instrument.Measurements.FirstOrDefault()?.Id,
                                securityLevel.Id,
                                securityLevel.Attention,
                                securityLevel.Alert,
                                securityLevel.Emergency,
                                securityLevel.AbruptVariation,
                                securityLevel.MaximumDailyRainfall,
                                securityLevel.RainIntensity,
                                securityLevel.Axis
                            };

                            await connection
                                .ExecuteAsync(Queries.UpsertSecurityLevel, securityLevelParam, transaction);
                        }
                    }

                    foreach (var measurement in instrument.Measurements)
                    {
                        var measurementParam = new
                        {
                            measurement.Id,
                            InstrumentId = instrument.Id,
                            measurement.Active,
                            measurement.Identifier,
                            measurement.AlternativeName,
                            measurement.Quota,
                            measurement.Lithotype,
                            measurement.Limit,
                            measurement.Depth,
                            measurement.Length,
                            measurement.DeltaRef,
                            measurement.IsReferential
                        };

                        await connection
                            .ExecuteAsync(Queries.InsertMeasurement, measurementParam, transaction);

                        if (measurement.SecurityLevels != null)
                        {
                            var securityLevelParam = new
                            {
                                InstrumentId = instrument.Id,
                                MeasurementId = measurement.Id,
                                measurement.SecurityLevels?.Id,
                                measurement.SecurityLevels?.Attention,
                                measurement.SecurityLevels?.Alert,
                                measurement.SecurityLevels?.Emergency,
                                measurement.SecurityLevels?.AbruptVariation,
                                measurement.SecurityLevels?.MaximumDailyRainfall,
                                measurement.SecurityLevels?.RainIntensity,
                                measurement.SecurityLevels?.Axis
                            };

                            await connection
                                .ExecuteAsync(Queries.UpsertSecurityLevel, securityLevelParam, transaction);
                        }
                    }

                    foreach (var history in instrument.History)
                    {
                        var historyParam = new
                        {
                            history.Id,
                            InstrumentId = instrument.Id,
                            history.Changes,
                            ModifiedByUserId = history.ModifiedBy.Id
                        };

                        await connection.ExecuteAsync(Queries.InsertInstrumentHistory, historyParam, transaction);
                    }

                    await connection.ExecuteAsync(OutboxQueries.Insert, new
                    {
                        Id = Guid.NewGuid(),
                        Type = typeof(NotificationCommand).AssemblyQualifiedName,
                        Data = JsonSerializer.Serialize(new NotificationCommand()
                        {
                            Id = instrument.Id,
                            Theme = NotificationTheme.InstrumentCreated,
                            CreatedById = instrument.History
                                .OrderByDescending(x => x.CreatedDate)
                                .FirstOrDefault().ModifiedBy.Id,
                            CreatedDate = DateTime.UtcNow
                        })
                    }, transaction);
                }

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<int> CountAsync(SearchInstrumentRequest request)
        {
            var param = new
            {
                request.Id,
                request.Identifier,
                request.Type,
                request.Subtype,
                request.Online,
                request.Automated,
                request.ClientId,
                request.ClientUnitId,
                request.StructureId,
                request.SectionId,
                request.InstallationDate,
                request.SearchIdentifier,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures
            };

            var builder = new SqlBuilder();

            if (request.SearchIdentifier.HasValue)
            {
                builder.Where("[i].[search-identifier] = @SearchIdentifier");
            }
            if (request.Type.HasValue)
            {
                builder.Where("[i].[type] = @Type");
            }
            if (request.Automated.HasValue)
            {
                builder.Where("[i].[automated] = @Automated");
            }
            if (request.Id.HasValue)
            {
                builder.Where("[i].[id] = @Id");
            }
            if (!string.IsNullOrEmpty(request.Identifier))
            {
                builder.Where("[i].[identifier] LIKE CONCAT('%', @Identifier, '%')");
            }
            if (request.InstallationDate.HasValue)
            {
                builder.Where("[i].[installation-date] = @InstallationDate");
            }
            if (request.Online.HasValue)
            {
                builder.Where("[i].[online] = @Online");
            }
            if (request.Subtype.HasValue)
            {
                builder.Where("[i].[subtype] = @Subtype");
            }
            if (request.ClientId.HasValue)
            {
                builder.Where("[client-units].[client-id] = @ClientId");
            }
            if (request.ClientUnitId.HasValue)
            {
                builder.Where("[structures].[client-unit-id] = @ClientUnitId");
            }
            if (request.StructureId.HasValue)
            {
                builder.Where("[structures].[id] = @StructureId");
            }
            if (request.SectionId.HasValue)
            {
                builder.Where("[i].[id] IN (SELECT [instrument-id] FROM [section-instruments] WHERE [section-instruments].[section-id] = @SectionId)");
            }
            if (!request.RequestedBySuperSupport)
            {
                builder.Where("[i].[structure-id] IN @RequestedUserStructures");
            }

            var query = builder.AddTemplate(Queries.Count, param);

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.ExecuteScalarAsync<int>(query.RawSql, param);
        }

        public async Task<List<SearchInstrumentSecurityLevelAlertResponse>> SearchSecurityLevelAlertAsync(SearchInstrumentSecurityLevelAlertRequest request)
        {
            var param = new
            {
                request.InstrumentId,
                Skip = request.GetSkip(),
                request.PageSize,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return (await connection.QueryAsync<SearchInstrumentSecurityLevelAlertResponse>(
                sql: Queries.SearchSecurityLevelAlerts,
                new[]
                {
                    typeof(SecurityLevelAlert),
                    typeof(Measurement)
                },
                (records) =>
                {
                    var alert = records[0] as SecurityLevelAlert;

                    return new()
                    {
                        Id = alert.Id,
                        Measurement = records[1] is Measurement measurement ? new()
                        {
                            Id = measurement.Id,
                            Identifier = measurement.Identifier
                        }
                        : null,
                        ReadingValueDate = alert.ReadingValueDate,
                        SecurityLevel = alert.SecurityLevel,
                        DisplacedAxis = alert.DisplacedAxis
                    };
                },
                splitOn: "Id",
                param: param
                )).ToList();
        }

        public async Task<PaginationResponse> SearchAsync(SearchInstrumentRequest request)
        {
            var param = new
            {
                request.Id,
                request.Identifier,
                request.Type,
                request.Subtype,
                request.Online,
                request.Automated,
                request.ClientId,
                request.ClientUnitId,
                request.StructureId,
                request.SectionId,
                request.InstallationDate,
                request.SearchIdentifier,
                Skip = request.GetSkip(),
                request.PageSize,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures,
                InstrumentsWithAbsoluteVariation = new List<InstrumentType>()
                {
                    InstrumentType.WaterLevelIndicator,
                    InstrumentType.ElectricPiezometer,
                    InstrumentType.OpenStandpipePiezometer
                },
                request.VariationPeriodDays,
                request.OrderBy
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var builder = new SqlBuilder();

            if (request.OrderBy == SortInstrumentCriteria.AbsoluteVariationAscending)
            {
                builder.OrderBy("CASE WHEN [subquery].AbsoluteVariation IS NULL THEN 1 ELSE 0 END, [subquery].AbsoluteVariation ASC");
            }
            else if (request.OrderBy == SortInstrumentCriteria.AbsoluteVariationDescending)
            {
                builder.OrderBy("[subquery].AbsoluteVariation DESC");
            }
            else if (request.OrderBy == SortInstrumentCriteria.CreationDateAscending)
            {
                builder.OrderBy("[subquery].SearchIdentifier ASC");
            }
            else if (request.OrderBy == SortInstrumentCriteria.CreationDateDescending)
            {
                builder.OrderBy("[subquery].SearchIdentifier DESC");
            }

            if (request.SearchIdentifier.HasValue)
            {
                builder.Where("[i].[search-identifier] = @SearchIdentifier");
            }
            if (request.Type.HasValue)
            {
                builder.Where("[i].[type] = @Type");
            }
            if (request.Automated.HasValue)
            {
                builder.Where("[i].[automated] = @Automated");
            }
            if (request.Id.HasValue)
            {
                builder.Where("[i].[id] = @Id");
            }
            if (!string.IsNullOrEmpty(request.Identifier))
            {
                builder.Where("[i].[identifier] LIKE CONCAT('%', @Identifier, '%')");
            }
            if (request.InstallationDate.HasValue)
            {
                builder.Where("[i].[installation-date] = @InstallationDate");
            }
            if (request.Online.HasValue)
            {
                builder.Where("[i].[online] = @Online");
            }
            if (request.Subtype.HasValue)
            {
                builder.Where("[i].[subtype] = @Subtype");
            }
            if (request.ClientId.HasValue)
            {
                builder.Where("[client-units].[client-id] = @ClientId");
            }
            if (request.ClientUnitId.HasValue)
            {
                builder.Where("[structures].[client-unit-id] = @ClientUnitId");
            }
            if (request.StructureId.HasValue)
            {
                builder.Where("[structures].[id] = @StructureId");
            }
            if (request.SectionId.HasValue)
            {
                builder.Where("[i].[id] IN (SELECT [instrument-id] FROM [section-instruments] WHERE [section-instruments].[section-id] = @SectionId)");
            }
            if (!request.RequestedBySuperSupport)
            {
                builder.Where("[i].[structure-id] IN @RequestedUserStructures");
            }

            var query = builder.AddTemplate(Queries.Search);

            var instruments = (await connection.QueryAsync<SearchInstrumentResponse>(
                sql: query.RawSql,
                new[]
                {
                    typeof(Domain.Entities.Instrument),
                    typeof(CoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(Domain.Entities.Structure),
                    typeof(Measurement),
                    typeof(double?),
                    typeof(AbsoluteVariationColors)
                },
                (records) =>
                {
                    var instrument = records[0] as Domain.Entities.Instrument;
                    var coordinateSetting = records[1] as CoordinateSetting;
                    var decimalGeodetic = records[2] as DecimalGeodetic;
                    var utm = records[3] as Utm;
                    var structure = records[4] as Domain.Entities.Structure;
                    var absoluteVariation = records[6] as double?;

                    coordinateSetting.Systems = new()
                    {
                        DecimalGeodetic = decimalGeodetic,
                        Utm = utm
                    };

                    return new()
                    {
                        InstrumentId = instrument.Id,
                        InstrumentIdentifier = instrument.Identifier,
                        InstrumentSearchIdentifier = instrument.SearchIdentifier,
                        AbsoluteVariation = absoluteVariation,
                        Measurement = records[5] is Measurement measurement
                        ? new()
                        {
                            Id = measurement.Id,
                            Identifier = measurement.Identifier
                        }
                        : null,
                        Automated = instrument.Automated,
                        Type = instrument.Type,
                        Online = instrument.Online,
                        InstallationDate = instrument.InstallationDate,
                        Structure = new()
                        {
                            Id = structure.Id,
                            Name = structure.Name,
                            ClientUnitId = structure.ClientUnitId,
                        },
                        CoordinateSetting = coordinateSetting,
                        AbsoluteVariationColorConfiguration = records[7] is AbsoluteVariationColors absoluteVariationColorConfiguration
                        ? new()
                        {
                            Id = absoluteVariationColorConfiguration.Id,
                            PositiveAbsoluteVariationColor = absoluteVariationColorConfiguration.PositiveAbsoluteVariationColor,
                            NegativeAbsoluteVariationColor = absoluteVariationColorConfiguration.NegativeAbsoluteVariationColor,
                            ConstantAbsoluteVariationColor = absoluteVariationColorConfiguration.ConstantAbsoluteVariationColor

                        }
                        : null
                    };
                },
                splitOn: "Datum,Latitude,Northing,Id,Id,AbsoluteVariation,Id",
                param: param
                )).ToList();

            query = builder.AddTemplate(Queries.Count);

            var count = await connection.ExecuteScalarAsync<int>(query.RawSql, param);

            if (request.OutputDatum != null)
            {
                var coordinatesSettings = instruments.Select(instrument => instrument.CoordinateSetting);

                foreach (var coordinateSetting in coordinatesSettings)
                {
                    var inputDatum = coordinateSetting.Datum;

                    if (inputDatum != request.OutputDatum)
                    {
                        coordinateSetting.Systems.DecimalGeodetic = Helper.ToDatum(inputDatum, (Datum)request.OutputDatum, coordinateSetting.Systems.DecimalGeodetic);
                        coordinateSetting.Systems.Utm = Helper.ToDatum(inputDatum, (Datum)request.OutputDatum, coordinateSetting.Systems.Utm);
                    }
                }
            }

            return new()
            {
                Data = instruments,
                CurrentItemsCount = instruments.Count,
                TotalItemsCount = count
            };
        }

        public async Task<IEnumerable<ListInstrumentResponse>> ListAsync(ListInstrumentRequest request)
        {
            var builder = new SqlBuilder();

            if (request.StructureId.HasValue)
            {
                builder = builder.Where(
                    "[instruments].[structure-id] = @StructureId",
                    new {  StructureId = request.StructureId.Value });
            }

            if (request.Type?.Any() ?? false)
            {
                builder = builder.Where("[instruments].[type] IN @Type",
                    new { Type = request.Type });
            }

            if (request.Subtype.HasValue)
            {
                builder = builder.Where("[instruments].[subtype] = @Subtype",
                    new { Subtype = request.Subtype });
            }

            if (!request.RequestedBySuperSupport)
            {
                builder = builder.Where(
                    "[instruments].[structure-id] IN @RequestedUserStructures",
                    new { RequestedUserStructures = request.RequestedUserStructures });
            }

            var template = builder.AddTemplate(Queries.List);
            
            var lookup = new Dictionary<Guid, ListInstrumentResponse>();
            
            await using var connection = ApplicationDatabase
                .GetConnection(_connectionString);
            
            await connection.OpenAsync();

            _ = await connection
                .QueryAsync(
                    sql: template.RawSql, 
                    types: new []
                    {
                        typeof(ListInstrumentResponse),
                        typeof(Guid?)
                    },
                    splitOn:"SectionId",
                    map: records =>
                    {
                        var instrument = records[0] as ListInstrumentResponse;
                        var sectionId = records[1] as Guid?;
                        
                        if (instrument == null)
                        {
                            return null;
                        }

                        if (!lookup.TryGetValue(
                                instrument.Id,
                                out var instrumentResponse))
                        {
                            instrumentResponse = instrument;
                            lookup.Add(instrumentResponse.Id, instrumentResponse);
                        }

                        if (sectionId.HasValue)
                        {
                            instrumentResponse.Sections.Add(sectionId.Value);
                        }

                        return instrumentResponse;
                    },
                    param: template.Parameters);

            return lookup.Values;
        }

        public async Task<bool> AlternativeNameExists(string alternativeName, Guid structureId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.AlternativeNameExists, new
            {
                AlternativeName = alternativeName,
                StructureId = structureId
            });

            return res > 0;
        }

        public async Task<bool> AlternativeNameExists(string alternativeName, Guid structureId, Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.AlternativeNameExistsWithOtherId, new
            {
                AlternativeName = alternativeName,
                StructureId = structureId,
                Id = id
            });

            return res > 0;
        }

        public async Task<bool> MeasurementIdentifierExists(string identifier, Guid structureId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.MeasurementIdentifierExists, new
            {
                Identifier = identifier,
                StructureId = structureId
            });

            return res > 0;
        }

        public async Task<bool> MeasurementIdentifierExists(string identifier, Guid structureId, Guid instrumentId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.MeasurementIdentifierExistsWithOtherId, new
            {
                Identifier = identifier,
                StructureId = structureId,
                InstrumentId = instrumentId
            });

            return res > 0;
        }

        public async Task<bool> MeasurementAlternativeNameExists(string alternativeName, Guid structureId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.MeasurementAlternativeNameExists, new
            {
                AlternativeName = alternativeName,
                StructureId = structureId
            });

            return res > 0;
        }

        public async Task<bool> MeasurementAlternativeNameExists(string alternativeName, Guid structureId, Guid instrumentId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.MeasurementAlternativeNameExistsWithOtherId, new
            {
                AlternativeName = alternativeName,
                StructureId = structureId,
                InstrumentId = instrumentId
            });

            return res > 0;
        }

        public async Task<bool> IdentifierExists(string identifier, Guid structureId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.IdentifierExists, new
            {
                Identifier = identifier,
                StructureId = structureId
            });

            return res > 0;
        }

        public async Task<bool> IdentifierExists(string identifier, Guid structureId, Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var res = await connection.ExecuteScalarAsync<int>(Queries.IdentifierExistsWithOtherId, new
            {
                Identifier = identifier,
                StructureId = structureId,
                Id = id
            });

            return res > 0;
        }

        public Task AddAsync(Domain.Entities.Instrument item)
        {
            throw new NotImplementedException();
        }

        public Task DeleteAsync(Guid id)
        {
            throw new NotImplementedException();
        }

        public async Task<Domain.Entities.Instrument> GetBySearchIdentifierAsync(int searchIdentifier)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var lookup = new Dictionary<Guid, Domain.Entities.Instrument>();

            var securityLevelsDictionary = new Dictionary<Guid, Domain.Entities.SecurityLevels>();

            await connection.QueryAsync(
                sql: Queries.GetBySearchIdentifier,
                new[]
                {
                     typeof(Domain.Entities.Instrument),
                     typeof(Domain.Entities.SecurityLevels),
                     typeof(CoordinateSetting),
                     typeof(DecimalGeodetic),
                     typeof(Utm),
                     typeof(Domain.Entities.Client),
                     typeof(Domain.Entities.ClientUnit),
                     typeof(Domain.Entities.Structure),
                     typeof(Measurement)
                },
                (records) => BindValues(records, lookup, securityLevelsDictionary),
                splitOn: "Id,Datum,Latitude,Northing,Id,Id,Id,Id",
                param: new { SearchIdentifier = searchIdentifier }
                );

            foreach (var instrument in lookup.Values)
            {
                if (instrument.Measurements.Any())
                {
                    foreach (var measurement in instrument.Measurements)
                    {
                        var measurementSecurityLevel = securityLevelsDictionary.Values.FirstOrDefault(x => x.MeasurementId == measurement.Id);

                        if (measurementSecurityLevel != null)
                        {
                            measurement.SecurityLevels = measurementSecurityLevel;
                        }
                    }

                    continue;
                }

                var securityLevel = securityLevelsDictionary
                    .Values
                    .Where(x => x.InstrumentId == instrument.Id)
                    .ToList();

                if (securityLevel != null)
                {
                    instrument.SecurityLevels = securityLevel;
                }
            }

            return lookup.Values.FirstOrDefault();
        }

        public async Task<List<Domain.Entities.Instrument>> GetByFiltersFileAsync(GetInstrumentFileRequest request)
        {
            var param = new
            {
                request.Identifier,
                request.Type,
                request.Online,
                request.Automated,
                request.ClientId,
                request.ClientUnitId,
                request.StructureId,
                request.SectionId,
                request.InstallationDate,
                request.SearchIdentifier,
                request.RequestedBySuperSupport,
                request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var lookup = new Dictionary<Guid, Domain.Entities.Instrument>();

            var securityLevelsDictionary = new Dictionary<Guid, Domain.Entities.SecurityLevels>();

            await connection.QueryAsync(
                sql: Queries.GetByFiltersFile,
                new[]
                {
                     typeof(Domain.Entities.Instrument),
                     typeof(Domain.Entities.SecurityLevels),
                     typeof(CoordinateSetting),
                     typeof(DecimalGeodetic),
                     typeof(Utm),
                     typeof(Domain.Entities.Client),
                     typeof(Domain.Entities.ClientUnit),
                     typeof(Domain.Entities.Structure),
                     typeof(Measurement)
                },
                (records) => BindValues(records, lookup, securityLevelsDictionary),
                splitOn: "Id,Datum,Latitude,Northing,Id,Id,Id,Id",
                param: param
                );

            foreach (var instrument in lookup.Values)
            {
                if (instrument.Measurements.Any())
                {
                    foreach (var measurement in instrument.Measurements)
                    {
                        var measurementSecurityLevel = securityLevelsDictionary.Values.FirstOrDefault(x => x.MeasurementId == measurement.Id);

                        if (measurementSecurityLevel != null)
                        {
                            measurement.SecurityLevels = measurementSecurityLevel;
                        }
                    }

                    continue;
                }

                var securityLevel = securityLevelsDictionary
                    .Values
                    .Where(x => x.InstrumentId == instrument.Id)
                    .ToList();

                if (securityLevel != null)
                {
                    instrument.SecurityLevels = securityLevel;
                }
            }

            return lookup.Values.ToList();
        }
        
        public async Task<Domain.Entities.Instrument> GetAsync(Guid id)
        {
            var lookup = new Dictionary<Guid, Domain.Entities.Instrument>();
            var securityLevelsLookup = new Dictionary<Guid, Domain.Entities.SecurityLevels>();
            
            var template = new SqlBuilder()
                .Where("[instruments].[id] = @Id", new { Id = id })
                .AddTemplate(Queries.GetById);

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            await connection.QueryAsync(
                sql: template.RawSql,
                new[]
                {
                     typeof(Domain.Entities.Instrument),
                     typeof(SecurityLevels),
                     typeof(CoordinateSetting),
                     typeof(DecimalGeodetic),
                     typeof(Utm),
                     typeof(Domain.Entities.Client),
                     typeof(Domain.Entities.ClientUnit),
                     typeof(Domain.Entities.Structure),
                     typeof(Measurement)
                },
                (records) => BindValues(records, lookup, securityLevelsLookup),
                splitOn: "Id,Datum,Latitude,Northing,Id,Id,Id,Id",
                param: template.Parameters);

            foreach (var instrument in lookup.Values)
            {
                if (instrument.Measurements.Any())
                {
                    foreach (var measurement in instrument.Measurements)
                    {
                        var measurementSecurityLevel = securityLevelsLookup.Values.FirstOrDefault(x => x.MeasurementId == measurement.Id);

                        if (measurementSecurityLevel != null)
                        {
                            measurement.SecurityLevels = measurementSecurityLevel;
                        }
                    }

                    continue;
                }

                var securityLevel = securityLevelsLookup
                    .Values
                    .Where(x => x.InstrumentId == instrument.Id)
                    .ToList();

                if (securityLevel != null)
                {
                    instrument.SecurityLevels = securityLevel;
                }
            }

            return lookup.Values.FirstOrDefault();
        }

        public async Task<IEnumerable<Domain.Entities.Instrument>> GetMultipleAsync(
            IEnumerable<Guid> ids)
        {
            var lookup = new Dictionary<Guid, Domain.Entities.Instrument>();
            var securityLevelsLookup = new Dictionary<Guid, SecurityLevels>();

            var template = new SqlBuilder()
                .Where("[instruments].[id] IN @Ids", new {Ids = ids})
                .AddTemplate(Queries.GetById);

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            await connection.QueryAsync(
                sql: template.RawSql,
                new[]
                {
                     typeof(Domain.Entities.Instrument),
                     typeof(Domain.Entities.SecurityLevels),
                     typeof(CoordinateSetting),
                     typeof(DecimalGeodetic),
                     typeof(Utm),
                     typeof(Domain.Entities.Client),
                     typeof(Domain.Entities.ClientUnit),
                     typeof(Domain.Entities.Structure),
                     typeof(Measurement)
                },
                (records) => BindValues(records, lookup, securityLevelsLookup),
                splitOn: "Id,Datum,Latitude,Northing,Id,Id,Id,Id",
                param: template.Parameters);

            foreach (var instrument in lookup.Values)
            {
                if (instrument.Measurements.Any())
                {
                    foreach (var measurement in instrument.Measurements)
                    {
                        var measurementSecurityLevel = securityLevelsLookup.Values.FirstOrDefault(x => x.MeasurementId == measurement.Id);

                        if (measurementSecurityLevel != null)
                        {
                            measurement.SecurityLevels = measurementSecurityLevel;
                        }
                    }

                    continue;
                }

                var securityLevel = securityLevelsLookup
                    .Values
                    .Where(x => x.InstrumentId == instrument.Id)
                    .ToList();

                if (securityLevel != null)
                {
                    instrument.SecurityLevels = securityLevel;
                }
            }

            return lookup.Values;
        }

        private static Domain.Entities.Instrument BindValues(object[] records, Dictionary<Guid, Domain.Entities.Instrument> lookup, Dictionary<Guid, Domain.Entities.SecurityLevels> securityLevelsDictionary)
        {
            var instrument = records[0] as Domain.Entities.Instrument;
            var coordinateSetting = records[2] as CoordinateSetting;
            var decimalGeodetic = records[3] as DecimalGeodetic;
            var utm = records[4] as Utm;
            var client = records[5] as Domain.Entities.Client;
            var clientUnit = records[6] as Domain.Entities.ClientUnit;
            var structure = records[7] as Domain.Entities.Structure;

            if (!lookup.TryGetValue(instrument.Id, out var instrumentEntry))
            {
                instrumentEntry = instrument;
                lookup.Add(instrumentEntry.Id, instrumentEntry);
            }

            if (records[1] is Domain.Entities.SecurityLevels securityLevels && !securityLevelsDictionary.TryGetValue(securityLevels.Id, out var _))
            {
                securityLevelsDictionary.Add(securityLevels.Id, securityLevels);
            }

            instrument.Client = client;
            instrument.ClientUnit = clientUnit;
            instrument.Structure = structure;

            coordinateSetting.Systems = new()
            {
                DecimalGeodetic = decimalGeodetic,
                Utm = utm
            };

            instrument.CoordinateSetting = coordinateSetting;

            if (records[8] is Measurement measurement)
            {
                instrumentEntry.AddMeasurement(measurement);
            }

            return instrumentEntry;
        }

        public async Task<List<PercolationInstrumentMapData>> GetPercolationInstrumentsFromSameStructure(GetPercolationMapRequest request)
        {
            var param = new
            {
                request.StructureId,
                request.VariationPeriodDays,
                request.RequestedUserStructures,
                request.RequestedBySuperSupport,
                Subtype = InstrumentSubtype.Percolation,
                InstrumentsWithAbsoluteVariation = new List<InstrumentType>()
                {
                    InstrumentType.WaterLevelIndicator,
                    InstrumentType.ElectricPiezometer,
                    InstrumentType.OpenStandpipePiezometer
                }
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return (await connection.QueryAsync(
                sql: Queries.GetPercolationInstruments,
                new[]
                {
                    typeof(Domain.Entities.Instrument),
                    typeof(CoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(double?),
                    typeof(Guid)
                },
                (records) =>
                {
                    var instrumentDb = records[0] as Domain.Entities.Instrument;
                    var coordinateSetting = records[1] as CoordinateSetting;
                    var decimalGeodetic = records[2] as DecimalGeodetic;
                    var absoluteVariation = records[3] as double?;
                    var structureId = records[4] as Guid?;


                    if (coordinateSetting.Datum != Coordinate.Core.Enums.Datum.SIRGAS2000)
                    {
                        decimalGeodetic = Helper.ToDatum(coordinateSetting.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, decimalGeodetic);
                    }

                    var instrument = new PercolationInstrumentMapData()
                    {
                        Id = instrumentDb.Id,
                        Identifier = instrumentDb.Identifier,
                        Type = instrumentDb.Type,
                        DecimalGeodetic = decimalGeodetic,
                        Online = instrumentDb.Online,
                        AbsoluteVariation = absoluteVariation,
                        StructureId = structureId
                    };

                    return instrument;
                },
                splitOn: "Id,Datum,Latitude,AbsoluteVariation,StructureId",
                param: param
                )).ToList();
        }

        public async Task<List<DisplacementInstrumentMapData>> GetDisplacementInstrumentsFromSameStructure(GetDisplacementMapRequest request)
        {
            var lookup = new Dictionary<Guid, DisplacementInstrumentMapData>();

            var param = new
            {
                request.StructureId,
                request.StartDate,
                request.EndDate,
                request.RequestedUserStructures,
                request.RequestedBySuperSupport,
                Subtype = InstrumentSubtype.Displacement,
                InstrumentsWithAbsoluteVariation = new List<InstrumentType>()
                {
                    InstrumentType.SurfaceLandmark,
                    InstrumentType.Prism
                }
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            await connection.QueryAsync(
                sql: Queries.GetDisplacementInstruments,
                new[]
                {
                    typeof(Domain.Entities.Instrument),
                    typeof(CoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(double?),
                    typeof(double?),
                    typeof(double?),
                    typeof(Guid),
                    typeof(InstrumentGroup),
                },
                (records) =>
                {
                    var instrumentDb = records[0] as Domain.Entities.Instrument;
                    var coordinateSetting = records[1] as CoordinateSetting;
                    var decimalGeodetic = records[2] as DecimalGeodetic;
                    var utm = records[3] as Utm;
                    var zDisplacementVariation = records[4] as double?;
                    var nDisplacementVariation = records[5] as double?;
                    var eDisplacementVariation = records[6] as double?;
                    var structureId = records[7] as Guid?;
                    var calculatedUTMCoordinate = new Utm();
                    var calculatedDecimalCoordinate = new DecimalGeodetic();

                    if (coordinateSetting.Datum != Coordinate.Core.Enums.Datum.SIRGAS2000)
                    {
                        decimalGeodetic = Helper.ToDatum(coordinateSetting.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, decimalGeodetic);
                    }

                    coordinateSetting.Systems = new()
                    {
                        DecimalGeodetic = decimalGeodetic,
                        Utm = utm
                    };

                    if (nDisplacementVariation != null && eDisplacementVariation != null && nDisplacementVariation != 0 && eDisplacementVariation != 0)
                    {
                        calculatedUTMCoordinate = new Utm()
                        {
                            Northing = (double)(utm.Northing + ((nDisplacementVariation / 1000) * request.Overkill)),
                            Easting = (double)(utm.Easting + ((eDisplacementVariation / 1000) * request.Overkill)),
                            ZoneLetter = utm.ZoneLetter,
                            ZoneNumber = utm.ZoneNumber
                        };

                        calculatedDecimalCoordinate = Helper.ConvertUTMToDecimalGeodetic(calculatedUTMCoordinate, coordinateSetting.Datum);

                    }


                    if (!lookup.TryGetValue(instrumentDb.Id, out var instrument))
                    {
                        var newIntrument = new DisplacementInstrumentMapData()
                        {
                            Id = instrumentDb.Id,
                            Identifier = instrumentDb.Identifier,
                            Type = instrumentDb.Type,
                            CoordinateSetting = coordinateSetting,
                            Online = instrumentDb.Online,
                            ZDisplacementVariation = zDisplacementVariation,
                            NDisplacementVariation = nDisplacementVariation,
                            EDisplacementVariation = eDisplacementVariation,
                            CalculatedUtmCoordinate = calculatedUTMCoordinate,
                            CalculatedDecimalCoordinate = calculatedDecimalCoordinate,
                            StructureId = structureId,
                            InstrumentGroup = new List<Model._Shared.Instrument.InstrumentGroup>()
                        };

                        lookup.Add(instrumentDb.Id, newIntrument);
                        instrument = newIntrument;
                    }

                    if (records[8] is InstrumentGroup group)
                    {
                        instrument.InstrumentGroup.Add(
                            new()
                            {
                                Id = group.Id,
                                Name = group.Name
                            });
                    }

                    return instrument;

                },
                splitOn: "Datum,Latitude,Northing,ZDisplacementVariation,NDisplacementVariation,EDisplacementVariation, StructureId,Id",
                param: param
                );

            return lookup.Values.ToList();
        }

        public async Task<List<StabilityInstrumentMapData>> GetStabilityMapInstrumentsData(GetStabilityMapByStructureIdRequest request)
        {
            var param = new
            {
                request.StructureId,
                request.RequestedUserStructures,
                request.RequestedBySuperSupport,
                Type = new List<InstrumentType>()
                {
                    InstrumentType.WaterLevelIndicator,
                    InstrumentType.ElectricPiezometer,
                    InstrumentType.OpenStandpipePiezometer
                },
                Subtype = new List<InstrumentSubtype>()
                {
                    InstrumentSubtype.Percolation
                }

            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return (await connection.QueryAsync(
                sql: Queries.GetStabilityMapInstrumentsData,
                new[]
                {
                    typeof(Domain.Entities.Instrument),
                    typeof(CoordinateSetting),
                    typeof(DecimalGeodetic),
                },
                (records) =>
                {
                    var instrumentDb = records[0] as Domain.Entities.Instrument;
                    var coordinateSetting = records[1] as CoordinateSetting;
                    var decimalGeodetic = records[2] as DecimalGeodetic;

                    if (coordinateSetting.Datum != Coordinate.Core.Enums.Datum.SIRGAS2000)
                    {
                        decimalGeodetic = Helper.ToDatum(coordinateSetting.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, decimalGeodetic);
                    }

                    var instrument = new StabilityInstrumentMapData()
                    {
                        Id = instrumentDb.Id,
                        Identifier = instrumentDb.Identifier,
                        Type = instrumentDb.Type,
                        DecimalGeodetic = decimalGeodetic,
                        Online = instrumentDb.Online
                    };

                    return instrument;
                },
                splitOn: "Id,Datum,Latitude",
                param: param
                )).ToList();
        }

        public async Task<IEnumerable<InstrumentSimpleResponse>> GetInstrumentsDataByIdentifiersAsync(
            GetAutomatedReadingsDataRequest request)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();

            return await connection.QueryAsync<InstrumentSimpleResponse>(
                sql: Queries.GetInstrumentsDataByIdentifiers,
                param: request);
        }

        public async Task<IEnumerable<GetAutomatedReadingsDataResponse>> GetMeasurementsByInstrumentsIdsAsync(
            IEnumerable<InstrumentSimpleResponse> instruments)
        {
            await using var connection = ApplicationDatabase
                .GetConnection(_connectionString);
            await connection.OpenAsync();

            var lookup = new Dictionary<Guid, GetAutomatedReadingsDataResponse>();

            _ = await connection.QueryAsync(
                sql: Queries.GetMeasurementsByInstrumentsIds,
                param: new { instrumentsIds = instruments.Select(i => i.Id) },
                types: new[]
                {
                    typeof(MeasurementSimpleResponse)
                },
                splitOn: "Id",
                map: (records) =>
                {
                    var measurement = records[0] as MeasurementSimpleResponse;

                    if (!lookup.TryGetValue(measurement.InstrumentId, out var result))
                    {
                        var data = new GetAutomatedReadingsDataResponse(
                                instruments.First(i => i.Id == measurement.InstrumentId),
                                measurement);

                        lookup.Add(measurement.InstrumentId, data);

                        result = data;
                    }

                    if (!result.Data.Any(x => x.Id == measurement.Id))
                    {
                        result.Data.Add(measurement);
                    }   

                    return result;
                });

            return lookup.Values;
        }

        public async Task<Guid> GetStructureIdAsync(Guid id)
        {
            await using var connection = ApplicationDatabase
                .GetConnection(_connectionString);
            await connection.OpenAsync();

            return await connection.QueryFirstOrDefaultAsync<Guid>(Queries.GetStructureId, new { Id = id});
        }

        public async Task<IEnumerable<InstrumentMetricResponseItem>> GetGashboardMetricsAsync(
            GetDashboardInstrumentMetricsRequest request)
        {
            await using var connection = ApplicationDatabase
                .GetConnection(_connectionString);
            await connection.OpenAsync();

            return await connection.QueryAsync<InstrumentMetricResponseItem>(
                Queries.GetGashboardMetrics, 
                new { request.StructureId}) ?? Array.Empty<InstrumentMetricResponseItem>();
        }

        public async Task<List<Domain.Entities.Instrument>> GetByIdentifiersAsync(
            IEnumerable<string> identifiers,
            Guid structureId)
        {
            var lookup = new Dictionary<Guid, Domain.Entities.Instrument>();
            var securityLevelsLookup = new Dictionary<Guid, SecurityLevels>();

            var template = new SqlBuilder()
                .Where("[instruments].[structure-id] = @StructureId", new { StructureId = structureId })
                .Where("[instruments].[identifier] IN @Identifiers", new { Identifiers = identifiers })
                .AddTemplate(Queries.GetByIdentifier);

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            await connection.QueryAsync(
                sql: template.RawSql,
                new[]
                {
                     typeof(Domain.Entities.Instrument),
                     typeof(SecurityLevels),
                     typeof(CoordinateSetting),
                     typeof(DecimalGeodetic),
                     typeof(Utm),
                     typeof(Domain.Entities.Client),
                     typeof(Domain.Entities.ClientUnit),
                     typeof(Domain.Entities.Structure),
                     typeof(Measurement)
                },
                (records) => BindValues(records, lookup, securityLevelsLookup),
                splitOn: "Id,Datum,Latitude,Northing,Id,Id,Id,Id",
                param: template.Parameters);

            foreach (var instrument in lookup.Values)
            {
                if (instrument.Measurements.Any())
                {
                    foreach (var measurement in instrument.Measurements)
                    {
                        var measurementSecurityLevel = securityLevelsLookup.Values.FirstOrDefault(x => x.MeasurementId == measurement.Id);

                        if (measurementSecurityLevel != null)
                        {
                            measurement.SecurityLevels = measurementSecurityLevel;
                        }
                    }

                    continue;
                }

                var securityLevel = securityLevelsLookup
                    .Values
                    .Where(x => x.InstrumentId == instrument.Id)
                    .ToList();

                if (securityLevel != null)
                {
                    instrument.SecurityLevels = securityLevel;
                }
            }

            return lookup.Values.ToList();
        }

        public async Task<List<Domain.Entities.Instrument>> GetAsync(
            IEnumerable<Guid> ids)
        {
            var lookup = new Dictionary<Guid, Domain.Entities.Instrument>();
            var securityLevelsDictionary = new Dictionary<Guid, Domain.Entities.SecurityLevels>();

            var template = new SqlBuilder()
                .Where("[instruments].[id] IN @Ids", new { Ids = ids })
                .AddTemplate(Queries.GetById);

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.QueryAsync(
                sql: template.RawSql,
                new[]
                {
                     typeof(Domain.Entities.Instrument),
                     typeof(Domain.Entities.SecurityLevels),
                     typeof(CoordinateSetting),
                     typeof(DecimalGeodetic),
                     typeof(Utm),
                     typeof(Domain.Entities.Client),
                     typeof(Domain.Entities.ClientUnit),
                     typeof(Domain.Entities.Structure),
                     typeof(Measurement)
                },
                (records) => BindValues(records, lookup, securityLevelsDictionary),
                splitOn: "Id,Datum,Latitude,Northing,Id,Id,Id,Id",
                param: template.Parameters);

            foreach (var instrument in lookup.Values)
            {
                if (instrument.Measurements.Any())
                {
                    foreach (var measurement in instrument.Measurements)
                    {
                        var measurementSecurityLevel = securityLevelsDictionary.Values.FirstOrDefault(x => x.MeasurementId == measurement.Id);

                        if (measurementSecurityLevel != null)
                        {
                            measurement.SecurityLevels = measurementSecurityLevel;
                        }
                    }

                    continue;
                }

                var securityLevel = securityLevelsDictionary
                    .Values
                    .Where(x => x.InstrumentId == instrument.Id)
                    .ToList();

                if (securityLevel != null)
                {
                    instrument.SecurityLevels = securityLevel;
                }
            }

            return lookup.Values.ToList();
        }
    }
}
