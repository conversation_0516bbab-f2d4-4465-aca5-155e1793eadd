namespace Database.Repositories.Instrument
{
    public static class Queries
    {
        public const string Insert =
            @"
            INSERT INTO [instruments] 
            (
                id, 
                [structure-id], 
                type, 
                subtype, 
                identifier, 
                [alternative-name],
                latitude, 
                longitude, 
                northing, 
                easting, 
                [zone-number], 
                [zone-letter],
                datum, 
                [coordinate-format], 
                [top-quota], 
                [depth], 
                [upper-limit],
                [lower-limit],
                [measurement-frequency],
                [base-quota], 
                azimuth, 
                automated, 
                [geophone-type], 
                [linimetric-ruler-position], 
                elevation,
                [responsible-for-installation], 
                [installation-date], 
                model, 
                online,
                [dry-type]
            )
            VALUES 
            (
                @Id,
                @StructureId,
                @Type,
                @Subtype, 
                @Identifier, 
                @AlternativeName, 
                @Latitude,
                @Longitude, 
                @Northing,
                @Easting,
                @ZoneNumber, 
                @ZoneLetter, 
                @Datum,
                @Format,
                @TopQuota,
                @Depth,
                @UpperLimit,
                @LowerLimit,
                @MeasurementFrequency,
                @BaseQuota,
                @Azimuth, 
                @Automated,
                @GeophoneType,
                @LinimetricRulerPosition,
                @Elevation, 
                @ResponsibleForInstallation, 
                @InstallationDate,
                @Model,
                @Online,
                @DryType
            )";

        public const string GetByFiltersMaps =
            @"
                 SELECT 
                    [instruments].[id] AS Id,
                    [instruments].[identifier] AS Identifier,
                    [instruments].[type] AS Type,
                    [instruments].[online] AS Online,
                    [instruments].[datum] AS [Datum], 
                    [instruments].[coordinate-format] AS [Format], 
                    [instruments].[latitude] AS [Latitude], 
                    [instruments].[longitude] AS [Longitude]
                FROM [instruments]
                WHERE (@StructureId IS NULL OR [instruments].[structure-id] = @StructureId)
                AND (@Subtype IS NULL OR [instruments].[subtype] = @Subtype)
                AND (@RequestedBySuperSupport = 1 OR [instruments].[structure-id] IN @RequestedUserStructures)
	            ORDER BY [instruments].identifier ASC
            ";

        public const string CheckIfInstrumentIsInAnySection =
            @"
                SELECT TOP(1) 1 FROM [section-instruments] WITH (updlock,serializable)
                WHERE [instrument-id] = @Id
            ";

        public const string CheckIfTheInstrumentGroupExists =
            @"
                SELECT COUNT(*)
                FROM [instrument-groups-instruments]
                WHERE [instrument-id] = @InstrumentId
                AND [instrument-group-id] = @InstrumentGroupId
            ";

        public const string DeleteGroup =
            @"
                DELETE [instrument-groups]
                WHERE id = @Id
            ";

        public const string DeleteGroupInstrument =
            @"
                DELETE FROM [instrument-groups-instruments]
                WHERE [instrument-id] NOT IN @InstrumentIds
                AND [instrument-group-id] = @InstrumentGroupId
            ";

        public const string UpdateGroup =
            @"
                UPDATE [instrument-groups]
                SET [name] = @Name
                WHERE [id] = @Id
            ";

        public const string InsertGroup =
            @"
            INSERT INTO [instrument-groups] 
            (
                id, 
                name
            )
            VALUES 
            (
                @Id,
                @Name
            )
            ";

        public const string InsertGroupInstrument =
            @"
                INSERT INTO [instrument-groups-instruments] 
            (
                [instrument-group-id], 
                [instrument-id]
            )
            VALUES 
            (
                @InstrumentGroupId,
                @InstrumentId
            )
            ";

        public const string GetGroupById =
            @"
                SELECT 
                    [instrument-groups].id AS Id, 
                    [instrument-groups].name AS Name,
                    [instruments].id AS Id,
                    [instruments].[identifier] AS Identifier
                FROM [instrument-groups]
                JOIN [instrument-groups-instruments] ON [instrument-groups-instruments].[instrument-group-id] = [instrument-groups].id
                JOIN [instruments] ON [instruments].id = [instrument-groups-instruments].[instrument-id]
                WHERE [instrument-groups].id = @Id
            ";

        public const string SearchGroup =
            @"
                SELECT 
                    [ig].id AS Id, 
                    [ig].name AS Name,
                    [instruments].id AS Id,
                    [instruments].[identifier] AS Identifier,
                    [instruments].[type] AS Type
                FROM (
	                SELECT 
		                [id], 
		                [name],
                        [created-date]
	                FROM [instrument-groups]
	                WHERE (@Name IS NULL OR [instrument-groups].name LIKE CONCAT('%', @Name, '%'))
	               	ORDER BY [instrument-groups].[created-date] DESC
	                OFFSET @Skip ROWS
	                FETCH NEXT @PageSize ROWS ONLY
                ) AS [ig]
                JOIN [instrument-groups-instruments] ON [instrument-groups-instruments].[instrument-group-id] = [ig].id
                JOIN [instruments] ON [instruments].id = [instrument-groups-instruments].[instrument-id]
                WHERE (@StructureId IS NULL OR [instruments].[structure-id] = @StructureId)
                AND (@RequestedBySuperSupport = 1 OR [instruments].[structure-id] IN @RequestedUserStructures)
	            ORDER BY [ig].[created-date] DESC
            ";

        public const string CountGroup =
            @"
                SELECT COUNT(DISTINCT [instrument-groups].id)
                FROM [instrument-groups]
                JOIN [instrument-groups-instruments] ON [instrument-groups-instruments].[instrument-group-id] = [instrument-groups].id
                JOIN [instruments] ON [instruments].id = [instrument-groups-instruments].[instrument-id]
                WHERE (@Name IS NULL OR [instrument-groups].name LIKE CONCAT('%', @Name, '%'))
                AND (@StructureId IS NULL OR [instruments].[structure-id] = @StructureId)
                AND (@RequestedBySuperSupport = 1 OR [instruments].[structure-id] IN @RequestedUserStructures)
            ";

        public const string GetBySearchIdentifier =
            @"
                SELECT
                [instruments].id AS Id,
                [instruments].[search-identifier] AS SearchIdentifier,
                [instruments].[structure-id] AS StructureId,
                [instruments].type AS Type,
                [instruments].subtype AS Subtype,
                [instruments].identifier AS Identifier,
                [instruments].[dry-type] AS DryType,
                [instruments].[measurement-frequency] AS MeasurementFrequency,
                [instruments].[alternative-name] AS AlternativeName,
                [instruments].[top-quota] AS TopQuota,
                [instruments].[depth] AS Depth,
                [instruments].[upper-limit] AS UpperLimit,
                [instruments].[lower-limit] AS LowerLimit,
                [instruments].[base-quota] AS BaseQuota,
                [instruments].azimuth AS Azimuth,
                [instruments].automated AS Automated,
                [instruments].[geophone-type] AS GeophoneType,
                [instruments].[linimetric-ruler-position] AS LinimetricRulerPosition,
                [instruments].elevation AS Elevation,
                [instruments].[responsible-for-installation] AS ResponsibleForInstallation,           
                [instruments].[installation-date] AS InstallationDate,             
                [instruments].model AS Model,           
                [instruments].online AS Online,       
                [security-levels].[id] AS Id,
                [security-levels].[measurement-id] AS MeasurementId,
                [security-levels].[instrument-id] AS InstrumentId,
                [security-levels].[attention] AS Attention,
                [security-levels].[alert] AS Alert,
                [security-levels].[emergency] AS Emergency,
                [security-levels].[abrupt-variation] AS AbruptVariation,
                [security-levels].[rain-intensity] AS RainIntensity,
                [security-levels].[maximum-daily-rainfall] AS MaximumDailyRainfall,
                [security-levels].[axis] AS Axis,
                [instruments].datum AS Datum,
                [instruments].[coordinate-format] AS Format,
                [instruments].latitude AS Latitude,
                [instruments].longitude AS Longitude,
                [instruments].northing AS Northing,
                [instruments].easting AS Easting,
                [instruments].[zone-number] AS ZoneNumber,            
                [instruments].[zone-letter] AS ZoneLetter,
                [clients].[id] AS Id,
                [clients].[name] AS Name,
                [clients].[active] AS Active,
                [client-units].[id] AS Id,
                [client-units].[name] AS Name,
                [client-units].[active] AS Active,
                [client-units].[client-id] AS ClientId,
                [structures].[id] AS Id,
                [structures].[name] AS Name,
                [structures].[client-unit-id] AS [ClientUnitId],
                [measurements].[id] AS Id,
                [measurements].[identifier] AS Identifier,
                [measurements].[alternative-name] AS AlternativeName,
                [measurements].[active] AS Active,
                [measurements].[quota] AS Quota,
                [measurements].[lithotype] AS Lithotype,
                [measurements].[limit] AS Limit,
                [measurements].[depth] AS Depth,
                [measurements].[length] AS Length,
                [measurements].[delta-ref] AS DeltaRef,
                [measurements].[is-referential] AS IsReferential
                FROM [instruments]
                JOIN structures ON structures.id = instruments.[structure-id]
                JOIN [client-units] ON [client-units].id = [structures].[client-unit-id]
                JOIN [clients] ON [clients].id = [client-units].[client-id]
                LEFT JOIN [measurements] ON [measurements].[instrument-id] = instruments.id
                LEFT JOIN [security-levels] ON [security-levels].[measurement-id] = [measurements].[id] OR [security-levels].[instrument-id] = [instruments].[id]
                WHERE [instruments].[search-identifier] = @SearchIdentifier
            ";

        public const string GetByFiltersFile =
          @"
                SELECT
                [instruments].id AS Id,
                [instruments].[search-identifier] AS SearchIdentifier,
                [instruments].[structure-id] AS StructureId,
                [instruments].type AS Type,
                [instruments].subtype AS Subtype,
                [instruments].[dry-type] AS DryType,
                [instruments].[measurement-frequency] AS MeasurementFrequency,
                [instruments].identifier AS Identifier,
                [instruments].[alternative-name] AS AlternativeName,
                [instruments].[top-quota] AS TopQuota,
                [instruments].[depth] AS Depth,
                [instruments].[base-quota] AS BaseQuota,
                [instruments].azimuth AS Azimuth,
                [instruments].[upper-limit] AS UpperLimit,
                [instruments].[lower-limit] AS LowerLimit,
                [instruments].automated AS Automated,
                [instruments].[geophone-type] AS GeophoneType,
                [instruments].[linimetric-ruler-position] AS LinimetricRulerPosition,
                [instruments].elevation AS Elevation,
                [instruments].[responsible-for-installation] AS ResponsibleForInstallation,           
                [instruments].[installation-date] AS InstallationDate,             
                [instruments].model AS Model,           
                [instruments].online AS Online,    
                [security-levels].[id] AS Id,
                [security-levels].[measurement-id] AS MeasurementId,
                [security-levels].[instrument-id] AS InstrumentId,
                [security-levels].[attention] AS Attention,
                [security-levels].[alert] AS Alert,
                [security-levels].[emergency] AS Emergency,
                [security-levels].[abrupt-variation] AS AbruptVariation,
                [security-levels].[rain-intensity] AS RainIntensity,
                [security-levels].[maximum-daily-rainfall] AS MaximumDailyRainfall,
                [security-levels].[axis] AS Axis,
                [instruments].datum AS Datum,
                [instruments].[coordinate-format] AS Format,
                [instruments].latitude AS Latitude,
                [instruments].longitude AS Longitude,
                [instruments].northing AS Northing,
                [instruments].easting AS Easting,
                [instruments].[zone-number] AS ZoneNumber,            
                [instruments].[zone-letter] AS ZoneLetter,
                [clients].[id] AS Id,
                [clients].[name] AS Name,
                [clients].[active] AS Active,
                [client-units].[id] AS Id,
                [client-units].[name] AS Name,
                [client-units].[active] AS Active,
                [client-units].[client-id] AS ClientId,
                [structures].[id] AS Id,
                [structures].[name] AS Name,
                [structures].[client-unit-id] AS [ClientUnitId],
                [structures].[search-identifier] AS [SearchIdentifier],
                [measurements].[id] AS Id,
                [measurements].[identifier] AS Identifier,
                [measurements].[alternative-name] AS AlternativeName,
                [measurements].[active] AS Active,
                [measurements].[quota] AS Quota,
                [measurements].[lithotype] AS Lithotype,
                [measurements].[limit] AS Limit,
                [measurements].[depth] AS Depth,
                [measurements].[length] AS Length,
                [measurements].[delta-ref] AS DeltaRef,
                [measurements].[is-referential] AS IsReferential
                FROM [instruments]
                JOIN structures ON structures.id = instruments.[structure-id]
                JOIN [client-units] ON [client-units].id = [structures].[client-unit-id]
                JOIN [clients] ON [clients].id = [client-units].[client-id]
                LEFT JOIN [measurements] ON [measurements].[instrument-id] = [instruments].[id]
                LEFT JOIN [security-levels] ON [security-levels].[measurement-id] = [measurements].[id] OR [security-levels].[instrument-id] = [instruments].[id]
                LEFT JOIN [section-instruments] ON [section-instruments].[instrument-id] = [instruments].[id]
                WHERE (@SearchIdentifier IS NULL OR [instruments].[search-identifier] = @SearchIdentifier)
                AND ([instruments].[type] = @Type)
                AND (@SectionId IS NULL OR [section-instruments].[section-id] = @SectionId)
                AND (@Automated IS NULL OR [instruments].[automated] = @Automated)
                AND (@Identifier IS NULL OR [instruments].[identifier] LIKE CONCAT('%', @Identifier, '%'))
                AND (@InstallationDate IS NULL OR [instruments].[installation-date] = @InstallationDate)
                AND (@Online IS NULL OR [instruments].[online] = @Online)
                AND (@ClientId IS NULL OR [client-units].[client-id] = @ClientId)
                AND (@ClientUnitId IS NULL OR [structures].[client-unit-id] = @ClientUnitId)
                AND (@StructureId IS NULL OR [structures].[id] = @StructureId)
                AND (@RequestedBySuperSupport = 1 OR [instruments].[structure-id] IN @RequestedUserStructures)
                ORDER BY [instruments].[search-identifier] DESC
            ";

        public const string GetByIdentifier =
           @"   
                SELECT
                [instruments].id AS Id,
                [instruments].[search-identifier] AS SearchIdentifier,
                [instruments].[structure-id] AS StructureId,
                [instruments].type AS Type,
                [instruments].subtype AS Subtype,
                [instruments].[dry-type] AS DryType,
                [instruments].identifier AS Identifier,
                [instruments].[alternative-name] AS AlternativeName,
                [instruments].[top-quota] AS TopQuota,
                [instruments].[depth] AS Depth,
                [instruments].[upper-limit] AS UpperLimit,
                [instruments].[lower-limit] AS LowerLimit,
                [instruments].[measurement-frequency] AS MeasurementFrequency,
                [instruments].[base-quota] AS BaseQuota,
                [instruments].azimuth AS Azimuth,
                [instruments].automated AS Automated,
                [instruments].[geophone-type] AS GeophoneType,
                [instruments].[linimetric-ruler-position] AS LinimetricRulerPosition,
                [instruments].elevation AS Elevation,
                [instruments].[responsible-for-installation] AS ResponsibleForInstallation,           
                [instruments].[installation-date] AS InstallationDate,             
                [instruments].model AS Model,           
                [instruments].online AS Online,           
                [security-levels].[id] AS Id,
                [security-levels].[measurement-id] AS MeasurementId,
                [security-levels].[instrument-id] AS InstrumentId,
                [security-levels].[attention] AS Attention,
                [security-levels].[alert] AS Alert,
                [security-levels].[emergency] AS Emergency,
                [security-levels].[abrupt-variation] AS AbruptVariation,
                [security-levels].[rain-intensity] AS RainIntensity,
                [security-levels].[maximum-daily-rainfall] AS MaximumDailyRainfall,
                [security-levels].[axis] AS Axis,
                [instruments].datum AS Datum,
                [instruments].[coordinate-format] AS Format,
                [instruments].latitude AS Latitude,
                [instruments].longitude AS Longitude,
                [instruments].northing AS Northing,
                [instruments].easting AS Easting,
                [instruments].[zone-number] AS ZoneNumber,            
                [instruments].[zone-letter] AS ZoneLetter,
                [clients].[id] AS Id,
                [clients].[name] AS Name,
                [clients].[active] AS Active,
                [client-units].[id] AS Id,
                [client-units].[name] AS Name,
                [client-units].[active] AS Active,
                [client-units].[client-id] AS ClientId,
                [structures].[id] AS Id,
                [structures].[name] AS Name,
                [structures].[client-unit-id] AS [ClientUnitId],
                [measurements].[id] AS Id,
                [measurements].[identifier] AS Identifier,
                [measurements].[alternative-name] AS AlternativeName,
                [measurements].[active] AS Active,
                [measurements].[quota] AS Quota,
                [measurements].[lithotype] AS Lithotype,
                [measurements].[limit] AS Limit,
                [measurements].[depth] AS Depth,
                [measurements].[length] AS Length,
                [measurements].[delta-ref] AS DeltaRef,
                [measurements].[is-referential] AS IsReferential
                FROM [instruments]
                JOIN structures ON structures.id = instruments.[structure-id]
                JOIN [client-units] ON [client-units].id = [structures].[client-unit-id]
                JOIN [clients] ON [clients].id = [client-units].[client-id]
                LEFT JOIN [measurements] ON [measurements].[instrument-id] = [instruments].[id]
                LEFT JOIN [security-levels] ON [security-levels].[measurement-id] = [measurements].[id] OR [security-levels].[instrument-id] = [instruments].[id]
                /**where**/
            ";

        public const string GetByTypesAndSection =
            @"
                SELECT
                    [instruments].id AS Id,
                    [instruments].[search-identifier] AS SearchIdentifier,
                    [instruments].[structure-id] AS StructureId,
                    [instruments].type AS Type,
                    [instruments].subtype AS Subtype,
                    [instruments].[dry-type] AS DryType,
                    [instruments].identifier AS Identifier,
                    [instruments].[alternative-name] AS AlternativeName,
                    [instruments].[top-quota] AS TopQuota,
                    [instruments].[depth] AS Depth,
                    [instruments].[upper-limit] AS UpperLimit,
                    [instruments].[lower-limit] AS LowerLimit,
                    [instruments].[measurement-frequency] AS MeasurementFrequency,
                    [instruments].[base-quota] AS BaseQuota,
                    [instruments].azimuth AS Azimuth,
                    [instruments].automated AS Automated,
                    [instruments].[geophone-type] AS GeophoneType,
                    [instruments].[linimetric-ruler-position] AS LinimetricRulerPosition,
                    [instruments].elevation AS Elevation,
                    [instruments].[responsible-for-installation] AS ResponsibleForInstallation,           
                    [instruments].[installation-date] AS InstallationDate,             
                    [instruments].model AS Model,           
                    [instruments].online AS Online,           
                    [security-levels].[id] AS Id,
                    [security-levels].[measurement-id] AS MeasurementId,
                    [security-levels].[instrument-id] AS InstrumentId,
                    [security-levels].[attention] AS Attention,
                    [security-levels].[alert] AS Alert,
                    [security-levels].[emergency] AS Emergency,
                    [security-levels].[abrupt-variation] AS AbruptVariation,
                    [security-levels].[rain-intensity] AS RainIntensity,
                    [security-levels].[maximum-daily-rainfall] AS MaximumDailyRainfall,
                    [security-levels].[axis] AS Axis,
                    [instruments].datum AS Datum,
                    [instruments].[coordinate-format] AS Format,
                    [instruments].latitude AS Latitude,
                    [instruments].longitude AS Longitude,
                    [instruments].northing AS Northing,
                    [instruments].easting AS Easting,
                    [instruments].[zone-number] AS ZoneNumber,            
                    [instruments].[zone-letter] AS ZoneLetter,
                    [clients].[id] AS Id,
                    [clients].[name] AS Name,
                    [clients].[active] AS Active,
                    [client-units].[id] AS Id,
                    [client-units].[name] AS Name,
                    [client-units].[active] AS Active,
                    [client-units].[client-id] AS ClientId,
                    [structures].[id] AS Id,
                    [structures].[name] AS Name,
                    [structures].[client-unit-id] AS [ClientUnitId],
                    [measurements].[id] AS Id,
                    [measurements].[identifier] AS Identifier,
                    [measurements].[alternative-name] AS AlternativeName,
                    [measurements].[active] AS Active,
                    [measurements].[quota] AS Quota,
                    [measurements].[lithotype] AS Lithotype,
                    [measurements].[limit] AS Limit,
                    [measurements].[depth] AS Depth,
                    [measurements].[length] AS Length,
                    [measurements].[delta-ref] AS DeltaRef,
                    [measurements].[is-referential] AS IsReferential,
                    [measurements].[active] AS Active
                FROM [instruments]
                JOIN structures ON structures.id = instruments.[structure-id]
                JOIN [client-units] ON [client-units].id = [structures].[client-unit-id]
                JOIN [clients] ON [clients].id = [client-units].[client-id]
                JOIN [section-instruments] ON [section-instruments].[instrument-id] = [instruments].[id] AND [section-instruments].[section-id] = @SectionId
                LEFT JOIN [measurements] ON [measurements].[instrument-id] = [instruments].[id]
                LEFT JOIN [security-levels] ON [security-levels].[measurement-id] = [measurements].[id] OR [security-levels].[instrument-id] = [instruments].[id]
                WHERE [instruments].[type] IN @Types
            ";

        public const string GetByType =
            @"
                SELECT
                    [instruments].id AS Id,
                    [instruments].[search-identifier] AS SearchIdentifier,
                    [instruments].[structure-id] AS StructureId,
                    [instruments].type AS Type,
                    [instruments].subtype AS Subtype,
                    [instruments].[dry-type] AS DryType,
                    [instruments].identifier AS Identifier,
                    [instruments].[alternative-name] AS AlternativeName,
                    [instruments].[top-quota] AS TopQuota,
                    [instruments].[depth] AS Depth,
                    [instruments].[upper-limit] AS UpperLimit,
                    [instruments].[lower-limit] AS LowerLimit,
                    [instruments].[measurement-frequency] AS MeasurementFrequency,
                    [instruments].[base-quota] AS BaseQuota,
                    [instruments].azimuth AS Azimuth,
                    [instruments].automated AS Automated,
                    [instruments].[geophone-type] AS GeophoneType,
                    [instruments].[linimetric-ruler-position] AS LinimetricRulerPosition,
                    [instruments].elevation AS Elevation,
                    [instruments].[responsible-for-installation] AS ResponsibleForInstallation,           
                    [instruments].[installation-date] AS InstallationDate,             
                    [instruments].model AS Model,           
                    [instruments].online AS Online,           
                    [security-levels].[id] AS Id,
                    [security-levels].[measurement-id] AS MeasurementId,
                    [security-levels].[instrument-id] AS InstrumentId,
                    [security-levels].[attention] AS Attention,
                    [security-levels].[alert] AS Alert,
                    [security-levels].[emergency] AS Emergency,
                    [security-levels].[abrupt-variation] AS AbruptVariation,
                    [security-levels].[rain-intensity] AS RainIntensity,
                    [security-levels].[maximum-daily-rainfall] AS MaximumDailyRainfall,
                    [security-levels].[axis] AS Axis,
                    [instruments].datum AS Datum,
                    [instruments].[coordinate-format] AS Format,
                    [instruments].latitude AS Latitude,
                    [instruments].longitude AS Longitude,
                    [instruments].northing AS Northing,
                    [instruments].easting AS Easting,
                    [instruments].[zone-number] AS ZoneNumber,            
                    [instruments].[zone-letter] AS ZoneLetter,
                    [clients].[id] AS Id,
                    [clients].[name] AS Name,
                    [clients].[active] AS Active,
                    [client-units].[id] AS Id,
                    [client-units].[name] AS Name,
                    [client-units].[active] AS Active,
                    [client-units].[client-id] AS ClientId,
                    [structures].[id] AS Id,
                    [structures].[name] AS Name,
                    [structures].[client-unit-id] AS [ClientUnitId],
                    [measurements].[id] AS Id,
                    [measurements].[identifier] AS Identifier,
                    [measurements].[alternative-name] AS AlternativeName,
                    [measurements].[active] AS Active,
                    [measurements].[quota] AS Quota,
                    [measurements].[lithotype] AS Lithotype,
                    [measurements].[limit] AS Limit,
                    [measurements].[depth] AS Depth,
                    [measurements].[length] AS Length,
                    [measurements].[delta-ref] AS DeltaRef,
                    [measurements].[is-referential] AS IsReferential,
                    [measurements].[active] AS Active
                FROM [instruments]
                JOIN structures ON structures.id = instruments.[structure-id]
                JOIN [client-units] ON [client-units].id = [structures].[client-unit-id]
                JOIN [clients] ON [clients].id = [client-units].[client-id]
                LEFT JOIN [measurements] ON [measurements].[instrument-id] = [instruments].[id]
                LEFT JOIN [security-levels] ON [security-levels].[measurement-id] = [measurements].[id] OR [security-levels].[instrument-id] = [instruments].[id]
                WHERE [instruments].[type] = @Type AND [instruments].[structure-id] = @StructureId
            ";

        public const string GetById =
            @"
                SELECT
                [instruments].id AS Id,
                [instruments].[search-identifier] AS SearchIdentifier,
                [instruments].[structure-id] AS StructureId,
                [instruments].type AS Type,
                [instruments].subtype AS Subtype,
                [instruments].[dry-type] AS DryType,
                [instruments].identifier AS Identifier,
                [instruments].[alternative-name] AS AlternativeName,
                [instruments].[top-quota] AS TopQuota,
                [instruments].[depth] AS Depth,
                [instruments].[upper-limit] AS UpperLimit,
                [instruments].[lower-limit] AS LowerLimit,
                [instruments].[measurement-frequency] AS MeasurementFrequency,
                [instruments].[base-quota] AS BaseQuota,
                [instruments].azimuth AS Azimuth,
                [instruments].automated AS Automated,
                [instruments].[geophone-type] AS GeophoneType,
                [instruments].[linimetric-ruler-position] AS LinimetricRulerPosition,
                [instruments].elevation AS Elevation,
                [instruments].[responsible-for-installation] AS ResponsibleForInstallation,           
                [instruments].[installation-date] AS InstallationDate,             
                [instruments].model AS Model,           
                [instruments].online AS Online,           
                [security-levels].[id] AS Id,
                [security-levels].[measurement-id] AS MeasurementId,
                [security-levels].[instrument-id] AS InstrumentId,
                [security-levels].[attention] AS Attention,
                [security-levels].[alert] AS Alert,
                [security-levels].[emergency] AS Emergency,
                [security-levels].[abrupt-variation] AS AbruptVariation,
                [security-levels].[rain-intensity] AS RainIntensity,
                [security-levels].[maximum-daily-rainfall] AS MaximumDailyRainfall,
                [security-levels].[axis] AS Axis,
                [instruments].datum AS Datum,
                [instruments].[coordinate-format] AS Format,
                [instruments].latitude AS Latitude,
                [instruments].longitude AS Longitude,
                [instruments].northing AS Northing,
                [instruments].easting AS Easting,
                [instruments].[zone-number] AS ZoneNumber,            
                [instruments].[zone-letter] AS ZoneLetter,
                [clients].[id] AS Id,
                [clients].[name] AS Name,
                [clients].[active] AS Active,
                [client-units].[id] AS Id,
                [client-units].[name] AS Name,
                [client-units].[active] AS Active,
                [client-units].[client-id] AS ClientId,
                [structures].[id] AS Id,
                [structures].[name] AS Name,
                [structures].[client-unit-id] AS [ClientUnitId],
                [measurements].[id] AS Id,
                [measurements].[identifier] AS Identifier,
                [measurements].[alternative-name] AS AlternativeName,
                [measurements].[active] AS Active,
                [measurements].[quota] AS Quota,
                [measurements].[lithotype] AS Lithotype,
                [measurements].[limit] AS Limit,
                [measurements].[depth] AS Depth,
                [measurements].[length] AS Length,
                [measurements].[delta-ref] AS DeltaRef,
                [measurements].[is-referential] AS IsReferential,
                [measurements].[active] AS Active
                FROM [instruments]
                JOIN structures ON structures.id = instruments.[structure-id]
                JOIN [client-units] ON [client-units].id = [structures].[client-unit-id]
                JOIN [clients] ON [clients].id = [client-units].[client-id]
                LEFT JOIN [measurements] ON [measurements].[instrument-id] = [instruments].[id]
                LEFT JOIN [security-levels] ON [security-levels].[measurement-id] = [measurements].[id] OR [security-levels].[instrument-id] = [instruments].[id]
                /**where**/
            ";
        
        public const string GetByIdSimple =
            @"
                SELECT
                [instruments].id AS Id,
                [instruments].[search-identifier] AS SearchIdentifier,
                [instruments].[structure-id] AS StructureId,
                [instruments].type AS Type,
                [instruments].subtype AS Subtype,
                [instruments].[dry-type] AS DryType,
                [instruments].identifier AS Identifier,
                [instruments].[alternative-name] AS AlternativeName,
                [instruments].[top-quota] AS TopQuota,
                [instruments].[depth] AS Depth,
                [instruments].[upper-limit] AS UpperLimit,
                [instruments].[lower-limit] AS LowerLimit,
                [instruments].[measurement-frequency] AS MeasurementFrequency,
                [instruments].[base-quota] AS BaseQuota,
                [instruments].azimuth AS Azimuth,
                [instruments].automated AS Automated,
                [instruments].[geophone-type] AS GeophoneType,
                [instruments].[linimetric-ruler-position] AS LinimetricRulerPosition,
                [instruments].elevation AS Elevation,
                [instruments].[responsible-for-installation] AS ResponsibleForInstallation,           
                [instruments].[installation-date] AS InstallationDate,             
                [instruments].model AS Model,           
                [instruments].online AS Online,          
                [instruments].datum AS Datum,
                [instruments].[coordinate-format] AS Format,
                [instruments].latitude AS Latitude,
                [instruments].longitude AS Longitude,
                [instruments].northing AS Northing,
                [instruments].easting AS Easting,
                [instruments].[zone-number] AS ZoneNumber,            
                [instruments].[zone-letter] AS ZoneLetter                
                FROM [instruments]
                /**where**/
            ";

        public const string Count =
            @"
                SELECT
                COUNT([i].[id])
                FROM [instruments] [i]
                LEFT JOIN [measurements] ON [i].[id] = [measurements].[instrument-id]
                INNER JOIN [structures] ON [structures].[id] = [i].[structure-id]
                INNER JOIN [client-units] ON [client-units].[id] = [structures].[client-unit-id]
                /**where**/
            ";

        public const string Search =
            @"
               WITH ReadingQuotas AS
              (SELECT r.[instrument-id],
                      rv.[measurement-id],  
                      rv.quota,
                      rv.date,
                      rv.dry,
                      rv.[measurement-quota],
                      r.[instrument-base-quota],
                      ROW_NUMBER() OVER (PARTITION BY r.[instrument-id],COALESCE(rv.[measurement-id], r.[instrument-id]) ORDER BY rv.date DESC) AS LatestRank,
                      ROW_NUMBER() OVER (PARTITION BY r.[instrument-id], COALESCE(rv.[measurement-id], r.[instrument-id]) ORDER BY rv.date ASC) AS OldestRank
               FROM [readings] r
               INNER JOIN [reading-values] rv ON r.id = rv.[reading-id]
               INNER JOIN [instruments] ON instruments.id = r.[instrument-id]
               WHERE rv.date >= DATEADD(DAY, -@VariationPeriodDays, (SELECT MAX(date) FROM [reading-values] INNER JOIN readings ON readings.id = [reading-values].[reading-id] WHERE [instrument-id] = r.[instrument-id]))  
               AND [instruments].[type] IN @InstrumentsWithAbsoluteVariation),
                 CombinedQuotas AS
              (SELECT [instrument-id],
                      [measurement-id],
                      MAX(CASE
                              WHEN LatestRank = 1 THEN CASE WHEN DRY = 1 THEN COALESCE([measurement-quota],[instrument-base-quota]) ELSE QUOTA END
                          END) AS RecentQuota,
                      MAX(CASE
                              WHEN OldestRank = 1 THEN CASE WHEN DRY = 1 THEN COALESCE([measurement-quota],[instrument-base-quota]) ELSE QUOTA END
                          END) AS OldestQuota
               FROM ReadingQuotas
               GROUP BY [instrument-id],
                        [measurement-id]),
                 FilteredInstruments AS
              (SELECT [i].[id],
                      [i].[search-identifier],
                      [i].[type],
                      [i].[automated],
                      [i].[identifier],
                      [i].[installation-date],
                      [i].[online],
                      [i].[datum],
                      [i].[coordinate-format],
                      [i].[latitude],
                      [i].[longitude],
                      [i].[northing],
                      [i].[easting],
                      [i].[zone-number],
                      [i].[zone-letter],
                      [structures].[id] AS StructureId,
                      [structures].[name],
                      [structures].[client-unit-id],
                      [client-units].[client-id],
                      [absolute-variation-color-configurations].id AS AbsolutVariationColorConfigurationId,
		              [absolute-variation-color-configurations].[positive-absolute-variation-color],
		              [absolute-variation-color-configurations].[negative-absolute-variation-color],
		              [absolute-variation-color-configurations].[constant-absolute-variation-color]
               FROM [instruments] [i]
               INNER JOIN [structures] ON [structures].[id] = [i].[structure-id]
               INNER JOIN [client-units] ON [client-units].[id] = [structures].[client-unit-id]
               LEFT JOIN [absolute-variation-color-configurations] ON [absolute-variation-color-configurations].[structure-id] = [structures].[id]
               /**where**/ )
            SELECT 
                   Id,
                   SearchIdentifier,
                   Type,
                   Automated,
                   Identifier,
                   InstallationDate,
                   Online,
                   Datum,
                   Format,
                   Latitude,
                   Longitude,
                   Northing,
                   Easting,
                   ZoneNumber,
                   ZoneLetter,
                   StructureId AS Id,
                   Name,
                   [ClientUnitId],
                   MeasurementId AS Id,
                   MeasurementIdentifier AS Identifier,
                   AbsoluteVariation,
                   AbsolutVariationColorConfigurationId AS Id,
            	   PositiveAbsoluteVariationColor,
            	   NegativeAbsoluteVariationColor,
            	   ConstantAbsoluteVariationColor
            FROM (SELECT fi.[id] AS Id,
                   fi.[search-identifier] AS SearchIdentifier,
                   fi.[type] AS Type,
                   fi.[automated] AS Automated,
                   fi.[identifier] AS Identifier,
                   fi.[installation-date] AS InstallationDate,
                   fi.[online] AS Online,
                   fi.[datum] AS Datum,
                   fi.[coordinate-format] AS Format,
                   fi.[latitude] AS Latitude,
                   fi.[longitude] AS Longitude,
                   fi.[northing] AS Northing,
                   fi.[easting] AS Easting,
                   fi.[zone-number] AS ZoneNumber,
                   fi.[zone-letter] AS ZoneLetter,
                   fi.StructureId AS StructureId,
                   fi.[name] AS Name,
                   fi.[client-unit-id] AS [ClientUnitId],
                   m.[id] AS MeasurementId,
                   m.[identifier] AS MeasurementIdentifier,
                   (cq.RecentQuota - cq.OldestQuota) AS AbsoluteVariation,
                   fi.AbsolutVariationColorConfigurationId AS AbsolutVariationColorConfigurationId,
                   fi.[positive-absolute-variation-color] AS PositiveAbsoluteVariationColor,
                   fi.[negative-absolute-variation-color] AS NegativeAbsoluteVariationColor,
                   fi.[constant-absolute-variation-color] AS ConstantAbsoluteVariationColor
            FROM FilteredInstruments fi
            LEFT JOIN [measurements] m ON fi.[id] = m.[instrument-id]
                 AND m.[id] IS NOT NULL
            LEFT JOIN CombinedQuotas cq ON fi.[id] = cq.[instrument-id]
                 AND (m.[id] = cq.[measurement-id] OR cq.[measurement-id] IS NULL)
            ) AS [subquery]
            /**orderby**/ OFFSET @Skip ROWS FETCH NEXT @PageSize ROWS ONLY
            ";

        public const string CountNote =
            @"
                SELECT count(*)
                FROM [instrument-notes]
                JOIN [users] ON [users].[id] = [instrument-notes].[created-by-user-id]
                JOIN [instruments] ON [instruments].[id] = [instrument-notes].[instrument-id]
                WHERE (@RequestedBySuperSupport = 1 OR [instruments].[structure-id] IN @RequestedUserStructures)
                AND ([instrument-notes].[instrument-id] = @InstrumentId)
            ";

        public const string SearchNote =
            @"
                SELECT 
                [instrument-notes].[id] AS Id, 
                [instrument-notes].[description] AS Description, 
                [instrument-notes].[type] AS Type, 
                [display-in-charts] AS DisplayInCharts, 
                [instrument-notes].[created-date] AS CreatedDate,
                [image-name] AS Name, 
                [image-unique-name] AS UniqueName,
                [file-name] AS Name, 
                [file-unique-name] UniqueName, 
                [users].[id] AS Id,
                [users].[username] AS Username,
                [users].[first-name] AS Firstname,
                [users].[surname] AS Surname
                FROM [instrument-notes]
                JOIN [users] ON [users].[id] = [instrument-notes].[created-by-user-id]
                JOIN [instruments] ON [instruments].[id] = [instrument-notes].[instrument-id]
                WHERE (@RequestedBySuperSupport = 1 OR [instruments].[structure-id] IN @RequestedUserStructures)
                AND ([instrument-notes].[instrument-id] = @InstrumentId)       
                ORDER BY [instrument-notes].[created-date] DESC
                OFFSET @Skip ROWS
                FETCH NEXT @PageSize ROWS ONLY
            ";

        public const string GetNoteById =
            @"
                SELECT 
                [instrument-notes].[id] AS Id, 
                [instrument-notes].[description] AS Description, 
                [instrument-notes].[type] AS Type, 
                [display-in-charts] AS DisplayInCharts, 
                [instrument-notes].[created-date] AS CreatedDate,
                [image-name] AS Name, 
                [image-unique-name] AS UniqueName,
                [file-name] AS Name, 
                [file-unique-name] UniqueName, 
                [users].[id] AS Id,
                [users].[username] AS Username
                FROM [instrument-notes]
                JOIN [users] ON [users].[id] = [instrument-notes].[created-by-user-id]
                WHERE [instrument-notes].[id] = @Id
            ";

        public const string InsertNote =
            @"
            INSERT INTO [instrument-notes] 
            (
                [id], 
                [instrument-id], 
                [description], 
                [created-by-user-id], 
                [image-unique-name],
                [image-name],
                [file-unique-name],
                [file-name],
                [display-in-charts],
                [type]
            )
            VALUES 
            (
                @Id, 
                @InstrumentId, 
                @Description, 
                @CreatedBy, 
                @ImageUniqueName,
                @ImageName,
                @FileUniqueName,
                @FileName,
                @DisplayInCharts,
                @Type
            )
            ";

        public const string UpdateNote =
            @"
            UPDATE [instrument-notes] 
            SET 
                [description] = @Description,
                [image-unique-name] = @ImageUniqueName,
                [image-name] = @ImageName,
                [file-unique-name] = @FileUniqueName,
                [file-name] = @FileName,
                [display-in-charts] = @DisplayInCharts,
                [type] = @Type
            WHERE [id] = @Id
            ";

        public const string SearchHistory =
            @"
                SELECT 
                [instruments-history].[id] AS [Id], 
                [instruments-history].[display-in-charts] AS [DisplayInCharts], 
                [changes] AS [Changes], 
                [instruments-history].[created-date] AS [CreatedDate],
                [users].Id AS [Id],
                [users].[username] AS [Username],
                [users].[first-name] AS [Firstname],
                [users].[surname] AS [Surname]
                FROM [instruments-history]
                JOIN [users] ON [users].[id] = [instruments-history].[modified-by-user-id]
                JOIN [instruments] ON [instruments].[id] = [instruments-history].[instrument-id]
                WHERE [instruments-history].[instrument-id] = @InstrumentId
                AND (@RequestedBySuperSupport = 1 OR [instruments].[structure-id] IN @RequestedUserStructures)
                ORDER BY [instruments-history].[created-date] DESC
                OFFSET @Skip ROWS
                FETCH NEXT @PageSize ROWS ONLY
            ";

        public const string GetHistoryById =
            @"
            SELECT 
                [instruments-history].[id] AS [Id], 
                [instruments-history].[display-in-charts] AS [DisplayInCharts], 
                [changes] AS [Changes], 
                [instruments-history].[created-date] AS [CreatedDate],
                [users].Id AS [Id],
                [users].[username] AS [Username],
                [users].[first-name] AS [Firstname],
                [users].[surname] AS [Surname]
                FROM [instruments-history]
                JOIN [users] ON [users].[id] = [instruments-history].[modified-by-user-id]
                JOIN [instruments] ON [instruments].[id] = [instruments-history].[instrument-id]
                WHERE [instruments-history].[id] = @Id
            ";

        public const string UpdateHistory =
            @"UPDATE [instruments-history] 
            SET [display-in-charts] = @DisplayInCharts 
            WHERE [id] = @Id";

        public const string CountHistory =
            @"
                SELECT COUNT(*)
                FROM [instruments-history]
                JOIN [users] ON [users].[id] = [instruments-history].[modified-by-user-id]
                JOIN [instruments] ON [instruments].[id] = [instruments-history].[instrument-id]
                WHERE [instruments-history].[instrument-id] = @InstrumentId
                AND (@RequestedBySuperSupport = 1 OR [instruments].[structure-id] IN @RequestedUserStructures)
            ";

        public const string SearchNoteHistory =
            @"
                SELECT 
                [instrument-notes-history].[id] AS [Id], 
                [changes] AS [Changes], 
                [instrument-notes-history].[created-date] AS [CreatedDate],
                [users].Id AS [Id],
                [users].[username] AS [Username],
                [users].[first-name] AS [Firstname],
                [users].[surname] AS [Surname]
                FROM [instrument-notes-history]
                JOIN [users] ON [users].[id] = [instrument-notes-history].[modified-by-user-id]
                JOIN [instrument-notes] ON [instrument-notes].[id] = [instrument-notes-history].[instrument-note-id]
                JOIN [instruments] ON [instruments].[id] = [instrument-notes].[instrument-id]
                WHERE [instrument-notes-history].[instrument-note-id] = @NoteId
                AND (@RequestedBySuperSupport = 1 OR [instruments].[structure-id] IN @RequestedUserStructures)
                ORDER BY [instrument-notes-history].[created-date] DESC
                OFFSET @Skip ROWS
                FETCH NEXT @PageSize ROWS ONLY
            ";

        public const string CountNoteHistory =
            @"
                SELECT COUNT(*)
                FROM [instrument-notes-history]
                JOIN [users] ON [users].[id] = [instrument-notes-history].[modified-by-user-id]
                JOIN [instrument-notes] ON [instrument-notes].[id] = [instrument-notes-history].[instrument-note-id]
                JOIN [instruments] ON [instruments].[id] = [instrument-notes].[instrument-id]
                WHERE [instrument-notes-history].[instrument-note-id] = @NoteId
                AND (@RequestedBySuperSupport = 1 OR [instruments].[structure-id] IN @RequestedUserStructures)
            ";

        public const string InsertNoteHistory =
            @"
                INSERT INTO [instrument-notes-history] 
                (
                    [id], 
                    [instrument-note-id], 
                    [modified-by-user-id], 
                    [changes]
                )
                VALUES 
                (
                    @Id, 
                    @InstrumentNoteId, 
                    @ModifiedByUserId,
                    @Changes
                )
            ";

        public const string InsertInstrumentHistory =
        @"
                INSERT INTO [instruments-history] 
                (
                    [id], 
                    [instrument-id], 
                    [modified-by-user-id], 
                    [changes]
                )
                VALUES 
                (
                    @Id, 
                    @InstrumentId, 
                    @ModifiedByUserId,
                    @Changes
                )
            ";

        public const string SearchInstrumentHistory =
        @"
                SELECT 
                [instruments-history].[id] AS [Id], 
                [changes] AS [Changes], 
                [display-in-charts] AS [DisplayInCharts], 
                [instruments-history].[created-date] AS [CreatedDate],
                [users].Id AS [Id],
                [users].[username] AS [Username],
                [users].[first-name] AS [Firstname],
                [users].[surname] AS [Surname]
                FROM [instruments-history]
                JOIN [users] ON [users].[id] = [instrument-notes-history].[modified-by-user-id]
                JOIN [instruments] ON [instruments].[id] = [instruments-history].[instrument-id]
                WHERE [instruments-history].[instrument-id] = @InstrumentId
                AND (@RequestedBySuperSupport = 1 OR [instruments].[structure-id] IN @RequestedUserStructures)
                ORDER BY [instruments-history].[created-date] DESC
                OFFSET @Skip ROWS
                FETCH NEXT @PageSize ROWS ONLY
            ";

        public const string CountInstrumentHistory =
            @"
                SELECT COUNT(*)
                FROM [instruments-history]
                JOIN [users] ON [users].[id] = [instrument-notes-history].[modified-by-user-id]
                JOIN [instruments] ON [instruments].[id] = [instruments-history].[instrument-id]
                WHERE [instruments-history].[instrument-id] = @InstrumentId
                AND (@RequestedBySuperSupport = 1 OR [instruments].[structure-id] IN @RequestedUserStructures)
            ";

        public const string InsertMeasurement =
            @"
            INSERT INTO [measurements] 
            (
                id, 
                [instrument-id],
                identifier,
                [alternative-name], 
                quota, 
                lithotype, 
                limit, 
                depth, 
                length, 
                [delta-ref], 
                [is-referential],
                [active]
            )
            VALUES  
            (
                @Id, 
                @InstrumentId, 
                @Identifier,
                @AlternativeName,
                @Quota, 
                @Lithotype,
                @Limit, 
                @Depth, 
                @Length, 
                @DeltaRef,
                @IsReferential,
                @Active
            )
            ";

        public const string Update =
            @"
            UPDATE dbo.instruments
            SET type = @Type,
	            subtype = @Subtype,
	            identifier = @Identifier,
	            [alternative-name] = @AlternativeName,
	            latitude = @Latitude,
	            longitude = @Longitude,
	            northing = @Northing,
	            easting = @Easting,
	            [zone-number] = @ZoneNumber,
	            [zone-letter] = @ZoneLetter,
	            datum = @Datum,
	            [coordinate-format] = @Format,
	            [top-quota] = @TopQuota,
	            [depth] = @Depth,
                [upper-limit] = @UpperLimit,
                [lower-limit] = @LowerLimit,
                [measurement-frequency] = @MeasurementFrequency,
	            [base-quota] = @BaseQuota,
	            azimuth = @Azimuth,
	            automated = @Automated,
	            [geophone-type] = @GeophoneType,
	            [linimetric-ruler-position] = @LinimetricRulerPosition,
	            elevation = @Elevation,
	            [responsible-for-installation] = @ResponsibleForInstallation,
	            [installation-date] = @InstallationDate,
	            model = @Model,
	            online = @Online,
                [dry-type] = @DryType
                WHERE [id] = @Id
            ";

        public const string UpsertMeasurement =
            @"
                UPDATE [measurements]
                SET [instrument-id] = @InstrumentId,
	                [identifier] = @Identifier,
	                [alternative-name] = @AlternativeName,
	                [active] = @Active,
	                [quota] = @Quota,
	                [lithotype] = @Lithotype,
	                [limit] = @Limit,
	                [depth] = @Depth,
	                [length] = @Length,
	                [delta-ref] = @DeltaRef,
	                [is-referential] = @IsReferential
                WHERE [id] = @Id
                IF @@ROWCOUNT = 0
                    INSERT INTO [measurements] 
                    (
                        [id], 
                        [instrument-id],
                        [identifier],
                        [alternative-name], 
                        [quota], 
                        [lithotype], 
                        [limit], 
                        [depth], 
                        [length], 
                        [delta-ref], 
                        [is-referential],
                        [active]
                    )
                    VALUES  
                    (
                        @Id, 
                        @InstrumentId, 
                        @Identifier,
                        @AlternativeName,
                        @Quota, 
                        @Lithotype,
                        @Limit, 
                        @Depth, 
                        @Length, 
                        @DeltaRef,
                        @IsReferential,
                        @Active
                    )
            ";

        public const string List =
            @"
                SELECT 
                    [instruments].id AS Id,
                    [instruments].identifier AS Identifier,
                    [instruments].[type] AS Type,
                    [section-instruments].[section-id] AS SectionId
                FROM [instruments]
                    LEFT JOIN [section-instruments] ON [instruments].[id] = [section-instruments].[instrument-id]
                /**where**/
                ORDER BY [instruments].[identifier]
            ";

        public const string MeasurementIdentifierExists =
            @"
            SELECT TOP(1) 1
            FROM [measurements] 
            JOIN [instruments] ON [measurements].[instrument-id] = [instruments].id
            WHERE [measurements].identifier = @Identifier 
            AND [instruments].[structure-id] = @StructureId
            ";

        public const string MeasurementIdentifierExistsWithOtherId =
            @"
            SELECT TOP(1) 1
            FROM [measurements] 
            JOIN [instruments] ON [measurements].[instrument-id] = [instruments].id
            WHERE [measurements].identifier = @Identifier 
            AND [instruments].[structure-id] = @StructureId 
            AND [measurements].[instrument-id] <> @InstrumentId
            ";

        public const string MeasurementAlternativeNameExists =
            @"
            SELECT TOP(1) 1
            FROM [measurements] 
            JOIN [instruments] ON [measurements].[instrument-id] = [instruments].id
            WHERE [measurements].[alternative-name] = @AlternativeName AND [instruments].[structure-id] = @StructureId
            ";

        public const string MeasurementAlternativeNameExistsWithOtherId =
            @"
            SELECT TOP(1) 1
            FROM [measurements] 
            JOIN [instruments] ON [measurements].[instrument-id] = [instruments].id
            WHERE [measurements].[alternative-name] = @AlternativeName 
            AND [instruments].[structure-id] = @StructureId 
            AND [measurements].[instrument-id] <> @InstrumentId
            ";

        public const string IdentifierExists =
            @"
                SELECT TOP(1) 1 FROM [instruments]
                WHERE [identifier] = @Identifier 
                AND [structure-id] = @StructureId
            ";

        public const string IdentifierExistsWithOtherId =
            @"
                SELECT TOP(1) 1 FROM [instruments]
                WHERE [identifier] = @Identifier 
                AND [structure-id] = @StructureId
                AND [id] != @Id
            ";

        public const string AlternativeNameExists =
            @"
                SELECT TOP(1) 1 FROM [instruments]
                WHERE [alternative-name] = @AlternativeName 
                AND [structure-id] = @StructureId
            ";

        public const string AlternativeNameExistsWithOtherId =
            @"
                SELECT TOP(1) 1 FROM [instruments]  
                WHERE [alternative-name] = @AlternativeName 
                AND [structure-id] = @StructureId
                AND [id] != @Id
            ";

        public const string GetStructureOfInstruments =
            @"
                SELECT [structure-id] FROM [instruments]
                WHERE [id] IN @InstrumentsIds
            ";

        public const string CheckIfStructureAlreadyHasLimetricRuler =
            @"
                SELECT TOP(1) 1 FROM [instruments]
                WHERE [structure-id] = @StructureId
                AND [type] = @Type
                AND [linimetric-ruler-position] = @Position
            ";

        public const string CheckIfStructureAlreadyHasOtherLimetricRuler =
            @"
                SELECT TOP(1) 1 FROM [instruments]
                WHERE [structure-id] = @StructureId
                AND [type] = @Type
                AND [linimetric-ruler-position] = @Position
                AND [id] != @InstrumentId
            ";

        public const string GetSections =
            @"
                SELECT 
                [sections].[id] AS [Id],
                [sections].[name] AS [Name],
                    CASE WHEN (
                        SELECT TOP(1) [drawing-unique-name]
                        FROM [section-reviews]
                        WHERE [section-reviews].[section-id] = [sections].[id]
                        ORDER BY [created-date] DESC
                    ) IS NOT NULL THEN 1 ELSE 0 END AS [LatestReviewHaveFile]
                FROM [sections]
                LEFT JOIN [section-instruments] ON [section-instruments].[section-id] = [sections].[id]
                WHERE [section-instruments].[instrument-id] = @Id
                GROUP BY [sections].[id], [sections].[name]
            ";

        public const string InsertSecurityLevelAlert =
            @"
                INSERT INTO [security-level-alerts]
                (
                    [id],
                    [instrument-id],
                    [reading-value-date],
                    [security-level],
                    [difference],
                    [measurement-id],
                    [displaced-axis]
                )
                VALUES
                (
                    @Id,
                    @InstrumentId,
                    @ReadingValueDate,
                    @SecurityLevel,
                    @Difference,
                    @MeasurementId,
                    @DisplacedAxis
                )
            ";

        public const string SearchSecurityLevelAlerts =
            @"
                SELECT 
                [security-level-alerts].[id] AS [Id],
                [security-level-alerts].[reading-value-date] AS [ReadingValueDate],
                [security-level-alerts].[security-level] AS [SecurityLevel],
                [security-level-alerts].[difference] AS [Difference],
                [security-level-alerts].[displaced-axis] AS [DisplacedAxis],
                [measurements].[id] AS [Id],
                [measurements].[identifier] AS [Identifier]
                FROM [security-level-alerts]
                INNER JOIN [instruments] ON [security-level-alerts].[instrument-id] = [instruments].[id]
                LEFT JOIN [measurements] ON [security-level-alerts].[measurement-id] = [measurements].[id]
                WHERE ([security-level-alerts].[instrument-id] = @InstrumentId)
                AND (@RequestedBySuperSupport = 1 OR [instruments].[structure-id] IN @RequestedUserStructures)
                ORDER BY [security-level-alerts].[reading-value-date] DESC
                OFFSET @Skip ROWS
                FETCH NEXT @PageSize ROWS ONLY
            ";

        public const string CountSecurityLevelAlerts =
            @"
                SELECT COUNT(*)
                FROM [security-level-alerts]
                INNER JOIN [instruments] ON [security-level-alerts].[instrument-id] = [instruments].[id]
                WHERE ([security-level-alerts].[instrument-id] = @InstrumentId)
                AND (@RequestedBySuperSupport = 1 OR [instruments].[structure-id] IN @RequestedUserStructures)
            ";

        public const string UpsertSecurityLevel =
            @"
                UPDATE [security-levels]
                SET [attention] = @Attention,
                    [alert] = @Alert,
                    [emergency] = @Emergency,
                    [abrupt-variation] = @AbruptVariation,
                    [axis] = @Axis,
                    [maximum-daily-rainfall] = @MaximumDailyRainfall,
                    [rain-intensity] = @RainIntensity
                WHERE [id] = @Id
                IF @@ROWCOUNT = 0
                    INSERT INTO [security-levels]
                    (
                        [id],
                        [instrument-id],
                        [measurement-id],
                        [attention],
                        [alert],
                        [emergency],
                        [abrupt-variation],
                        [axis],
                        [maximum-daily-rainfall],
                        [rain-intensity] 
                    )
                    VALUES
                    (
                        @Id,
                        @InstrumentId,
                        @MeasurementId,
                        @Attention,
                        @Alert,
                        @Emergency,
                        @AbruptVariation,
                        @Axis,
                        @MaximumDailyRainfall,
                        @RainIntensity
                    )
            ";

        public const string DeleteSecurityLevels =
            @"
                DELETE FROM [security-levels]
                WHERE [id] NOT IN @SecurityLevelsIds
                AND [instrument-id] = @InstrumentId
            ";

        public const string GetPercolationInstruments =
            @"
                WITH ReadingQuotas AS (
                	SELECT 
                		r.[instrument-id], 
                		rv.[measurement-id], 
                		rv.quota, 
                		rv.date, 
                		rv.dry, 
                		rv.[measurement-quota], 
                		r.[instrument-base-quota], 
                		ROW_NUMBER() OVER (PARTITION BY r.[instrument-id],COALESCE(rv.[measurement-id], r.[instrument-id]) ORDER BY rv.date DESC) AS LatestRank, 
                		ROW_NUMBER() OVER (PARTITION BY r.[instrument-id],COALESCE(rv.[measurement-id], r.[instrument-id]) ORDER BY rv.date ASC ) AS OldestRank 
                    FROM 
                		[readings] r 
                		INNER JOIN [reading-values] rv ON r.id = rv.[reading-id] 
                		INNER JOIN [instruments] ON instruments.id = r.[instrument-id] 
                    WHERE 
                		rv.date >= DATEADD(DAY,-@VariationPeriodDays,(
                		SELECT 
                		       MAX(date) 
                		     FROM 
                		       [reading-values] 
                		       INNER JOIN readings ON readings.id = [reading-values].[reading-id] 
                		     WHERE 
                		       [instrument-id] = r.[instrument-id]
                		   )
                		 ) 
                		AND [instruments].[type] IN @InstrumentsWithAbsoluteVariation
                ), 
                CombinedQuotas AS (
                    SELECT 
                      [instrument-id], 
                      [measurement-id],	
                      MAX(
                        CASE WHEN LatestRank = 1 THEN CASE WHEN DRY = 1 THEN COALESCE(
                          [measurement-quota], [instrument-base-quota]
                        ) ELSE QUOTA END END
                      ) AS RecentQuota, 
                      MAX(
                        CASE WHEN OldestRank = 1 THEN CASE WHEN DRY = 1 THEN COALESCE(
                          [measurement-quota], [instrument-base-quota]
                        ) ELSE QUOTA END END
                      ) AS OldestQuota 
                    FROM 
                      ReadingQuotas 
                    GROUP BY 
                      [instrument-id], 
                      [measurement-id]
                ),
                FilteredInstruments AS (
                	SELECT 
                		[i].[id],
                		[i].[identifier],
                		[i].[type],
                		[i].[online] ,
                		[i].[datum], 
                		[i].[coordinate-format], 
                		[i].[latitude], 
                		[i].[longitude],
                  		[s].id AS [structure-id]
                    FROM [instruments] i
                  		INNER JOIN [structures] s ON [s].[id] = [i].[structure-id]
                		INNER JOIN [client-units] ON [client-units].[id] = [s].[client-unit-id]
                  	WHERE
                      [i].[structure-id] = @StructureId AND [i].[subtype] = @Subtype
                )  
                SELECT  
                	Id,
                	Identifier,
                	Type,
                	Online,
                	Datum, 
                	Format, 
                	Latitude, 
                	Longitude,		
                  	AbsoluteVariation,
                  	StructureId
                FROM 
                  	(SELECT  
                		fi.[id] AS Id,
                		fi.[identifier] AS Identifier,
                		fi.[type] AS TYPE,
                		fi.[online] AS ONLINE,
                		fi.[datum] AS Datum,
                		fi.[coordinate-format] AS [Format],
                		fi.[latitude] AS Latitude,
                		fi.[longitude] AS Longitude,					
                		(cq.RecentQuota - cq.OldestQuota) AS AbsoluteVariation,
                  		fi.[structure-id]  AS StructureId
                  	FROM FilteredInstruments fi 
                        LEFT JOIN [measurements] m ON fi.[id] = m.[instrument-id] 
                        LEFT JOIN CombinedQuotas cq ON m.[id] = cq.[measurement-id] 
                        AND fi.[id] = cq.[instrument-id]
                  	WHERE m.[id] IS NOT NULL 
                    UNION ALL 
                	SELECT 
                		fi.[id] AS Id,
                		fi.[identifier] AS Identifier,
                		fi.[type] AS TYPE,
                		fi.[online] AS ONLINE,
                		fi.[datum] AS Datum,
                		fi.[coordinate-format] AS [Format],
                		fi.[latitude] AS Latitude,
                		fi.[longitude] AS Longitude,					
                		(cq.RecentQuota - cq.OldestQuota) AS AbsoluteVariation,
                  		fi.[structure-id] AS StructureId
                     FROM FilteredInstruments fi
                		 LEFT JOIN CombinedQuotas cq ON fi.[id] = cq.[instrument-id]
                			 AND cq.[measurement-id] IS NULL
                     WHERE NOT EXISTS
                         (SELECT 1
                          FROM [measurements] m
                          WHERE fi.[id] = m.[instrument-id] )
                ) AS [subquery]
            ";

        public const string GetDisplacementInstruments =
            @"
                WITH ReadingDisplacements AS (
                    SELECT 
                        r.[instrument-id],
                        rv.[measurement-id], 
                        rv.[z-displacement],
                        rv.[east-displacement],
                        rv.[north-displacement],
                        rv.date,
                        ROW_NUMBER() OVER (PARTITION BY r.[instrument-id],COALESCE(rv.[measurement-id], r.[instrument-id]) ORDER BY rv.date DESC) AS LatestRank, 
                        ROW_NUMBER() OVER (PARTITION BY r.[instrument-id],COALESCE(rv.[measurement-id], r.[instrument-id]) ORDER BY rv.date ASC ) AS OldestRank 
                    FROM 
                        [readings] r 
                        INNER JOIN [reading-values] rv ON r.id = rv.[reading-id] 
                        INNER JOIN [instruments] ON instruments.id = r.[instrument-id] 
                    WHERE 
                       rv.date BETWEEN @StartDate AND @EndDate
                       AND [instruments].[type] IN @InstrumentsWithAbsoluteVariation
                ),
                CombinedDisplacements AS (	
                    SELECT 
                        [instrument-id], 
                        [measurement-id], 
                        MAX(
                          CASE WHEN LatestRank = 1 THEN [z-displacement] END
                        ) AS RecentZDisplacement, 
                        MAX(
                          CASE WHEN OldestRank = 1 THEN [z-displacement] END
                        ) AS OldestZDisplacement,
                        MAX(
                          CASE WHEN LatestRank = 1 THEN [north-displacement] END
                        ) AS RecentNDisplacement, 
                        MAX(
                          CASE WHEN OldestRank = 1 THEN [north-displacement] END
                        ) AS OldestNDisplacement,
                        MAX(
                          CASE WHEN LatestRank = 1 THEN [east-displacement] END
                        ) AS RecentEDisplacement, 
                        MAX(
                          CASE WHEN OldestRank = 1 THEN [east-displacement] END
                        ) AS OldestEDisplacement 
                    FROM 
                        ReadingDisplacements 
                    GROUP BY 
                        [instrument-id], 
                        [measurement-id]
                ),
                FilteredInstruments AS (
                    SELECT 
                        [i].[id],
                        [i].[identifier],
                        [i].[type],
                        [i].[online] ,
                        [i].[datum], 
                        [i].[coordinate-format], 
                        [i].[latitude], 
                        [i].[longitude],
                        [i].[northing], 
                        [i].[easting],
                        [i].[zone-number],            
                        [i].[zone-letter], 
                        [s].id AS [structure-id],
                        ig.id AS [instrument-group-id],
                        ig.name AS [instrument-group-name]                            
                    FROM [instruments] i
                       	INNER JOIN [structures] s ON [s].[id] = [i].[structure-id]
                  		INNER JOIN [client-units] ON [client-units].[id] = [s].[client-unit-id]
                        LEFT JOIN [instrument-groups-instruments] igi ON igi.[instrument-id] = i.id
                        LEFT JOIN [instrument-groups] ig ON ig.id = igi.[instrument-group-id]                       	
                    WHERE
                       	 [i].[structure-id] = @StructureId AND [i].[subtype] = @Subtype
                )
                SELECT  
                	Id,
                	Identifier,
                	Type,
                	Online,
                	Datum, 
                	Format, 
                	Latitude, 
                	Longitude,
                	Northing,
                	Easting,
                	ZoneNumber,
                	ZoneLetter,
                	ZDisplacementVariation,
                	NDisplacementVariation,
                	EDisplacementVariation,
                	StructureId,
                	InstrumentGroupID AS Id,
                	InstrumentGroupName AS Name
                FROM (
                    SELECT  fi.[id] AS Id,
                		fi.[identifier] AS Identifier,
                		fi.[type] AS TYPE,
                		fi.[online] AS ONLINE,
                		fi.[datum] AS Datum,
                		fi.[coordinate-format] AS [Format],
                		fi.[latitude] AS Latitude,
                		fi.[longitude] AS Longitude,
                		fi.[northing] AS Northing,
                		fi.[easting] AS Easting,
                		fi.[zone-number] AS ZoneNumber,            
                		fi.[zone-letter] AS ZoneLetter,
                		(cq.RecentZDisplacement - cq.OldestZDisplacement) AS ZDisplacementVariation,
                		(cq.RecentNDisplacement - cq.OldestNDisplacement) AS NDisplacementVariation,
                		(cq.RecentEDisplacement - cq.OldestEDisplacement) AS EDisplacementVariation,
                		fi.[structure-id]  AS StructureId,
                		fi.[instrument-group-id] AS InstrumentGroupID,
                		fi.[instrument-group-name] AS InstrumentGroupName
                    FROM FilteredInstruments fi 
                		LEFT JOIN [measurements] m ON fi.[id] = m.[instrument-id] 
                        INNER JOIN CombinedDisplacements cq ON m.[id] = cq.[measurement-id] 
                			AND fi.[id] = cq.[instrument-id]
                    WHERE m.[id] IS NOT NULL 
                	UNION ALL 
                	SELECT fi.[id] AS Id,
                		fi.[identifier] AS Identifier,
                		fi.[type] AS TYPE,
                		fi.[online] AS ONLINE,
                		fi.[datum] AS Datum,
                		fi.[coordinate-format] AS [Format],
                		fi.[latitude] AS Latitude,
                		fi.[longitude] AS Longitude,	
                		fi.[northing] AS Northing,
                		fi.[easting] AS Easting,
                		fi.[zone-number] AS ZoneNumber,            
                		fi.[zone-letter] AS ZoneLetter,				
                		(cq.RecentZDisplacement - cq.OldestZDisplacement) AS ZDisplacementVariation,
                		(cq.RecentNDisplacement - cq.OldestNDisplacement) AS NDisplacementVariation,
                		(cq.RecentEDisplacement - cq.OldestEDisplacement) AS EDisplacementVariation,
                		fi.[structure-id] AS StructureId,
                		fi.[instrument-group-id] AS InstrumentGroupID,
                		fi.[instrument-group-name] AS InstrumentGroupName
                   	FROM FilteredInstruments fi
                   		LEFT JOIN CombinedDisplacements cq ON fi.[id] = cq.[instrument-id]
                   			AND cq.[measurement-id] IS NULL
                	WHERE NOT EXISTS
                        (SELECT 1
                         FROM [measurements] m
                         WHERE fi.[id] = m.[instrument-id] ) 
                ) AS [subquery]";

        public const string GetStabilityMapInstrumentsData =
           @"
                 SELECT 
                    [instruments].[id] AS Id,
                    [instruments].[identifier] AS Identifier,
                    [instruments].[type] AS Type,
                    [instruments].[online] AS Online,
                    [instruments].[datum] AS [Datum], 
                    [instruments].[coordinate-format] AS [Format], 
                    [instruments].[latitude] AS [Latitude], 
                    [instruments].[longitude] AS [Longitude]
                FROM [instruments]
                WHERE [structure-id] = @StructureId
                AND [instruments].[type] IN @Type
                AND [instruments].[subtype] IN @Subtype
                AND (@RequestedBySuperSupport = 1 OR [instruments].[structure-id] IN @RequestedUserStructures)
	            ORDER BY [instruments].identifier ASC
            ";

        public const string GetWithSections = @"
            SELECT 
            	[instruments].[id],
            	[instruments].[identifier],
            	[section-instruments].[section-id]
            FROM [instruments] 
            	JOIN [section-instruments] ON [instruments].[id] = [section-instruments].[instrument-id]
            WHERE [instruments].[id] = @Id;";

        public const string GetInstrumentsDataByIdentifiers = @"
            SELECT
                [instruments].[id] AS 'Id',
                [instruments].[identifier] AS 'Identifier',
                [instruments].[alternative-name] AS 'AlternativeName',
                [instruments].[type] AS 'Type',
                [instruments].[top-quota] AS 'TopQuota',
                [instruments].[base-quota] AS 'BaseQuota',
                [instruments].[azimuth] AS 'Azimuth',
                [client-units].[name] AS 'ClientUnitName',
                [structures].[name] AS 'StructureName'
            FROM [instruments]
            LEFT JOIN [structures] ON [structures].[id] = [instruments].[structure-id]
            LEFT JOIN [client-units] ON [client-units].[id] = [structures].[client-unit-id]
            WHERE
                (@StructureId IS NULL OR [structure-id] = @StructureId)
                AND (@StructureName IS NULL OR [structures].[name] = @StructureName)
                AND (@ClientUnitName IS NULL OR [client-units].[name] = @ClientUnitName)
                AND (@ClientId IS NULL OR [client-units].[client-id] = @ClientId)
                AND (([instruments].[identifier] IN @InstrumentIdentifiers OR [instruments].[alternative-name] IN @InstrumentIdentifiers) OR [instruments].[type] IN @InstrumentTypes)";

        public const string GetMeasurementsByInstrumentsIds = @"
            SELECT
                [id] AS 'Id',
                [identifier] AS 'Identifier',
                [instrument-id] AS 'InstrumentId'
            FROM [measurements]
            WHERE
                [instrument-id] IN @instrumentsIds
                AND [active] = 1";

        public const string GetStructureId = @"
            SELECT [structure-id] 
            FROM [instruments] 
            WHERE [id] = @Id;
        ";

        public const string GetGashboardMetrics = @"
            SELECT instruments.type AS Type, COUNT(id) AS Count
            FROM instruments
            WHERE instruments.[structure-id] = @StructureId AND instruments.online = 1
            GROUP BY type
            ORDER BY type";
    }
}
