using Coordinate.Core;
using Coordinate.Core.Classes;
using Coordinate.Core.Enums;
using Dapper;
using Database.Repositories.Outbox;
using Domain.Entities;
using Domain.Enums;
using Domain.Messages.Events.Structure;
using Domain.ValueObjects;
using MassTransit;
using Model.Dashboard.GetSectionsMap.Request;
using Model.Dashboard.GetSectionsMap.Response;
using Model.Dashboard.GetStructureMap.Request;
using Model.Dashboard.GetStructureMap.Response;
using Model.Structure.GetByFiltersMaps.Request;
using Model.Structure.GetSimulationData.Request;
using Model.Structure.GetSimulationData.Response;
using Model.Structure.GetStabilityAnalysisDate.Request;
using Model.Structure.GetStabilityAnalysisDate.Response;
using Model.Structure.List.Request;
using Model.Structure.List.Response;
using Model.Structure.Patch.Request;
using Model.Structure.Search.Request;
using Model.Structure.Search.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Model.Structure.SearchHistory.Request;
using Model.Structure.SearchHistory.Response;
using AbsoluteVariationColors = Domain.Entities.AbsoluteVariationColors;
using NotificationCommand = Domain.Messages.Commands.Notification.CreateNotification;

namespace Database.Repositories.Structure
{
    public class StructureRepository : IStructureRepository
    {
        private readonly string _connectionString;

        public StructureRepository(string connectionString)
        {
            _connectionString = connectionString;
        }

        public async Task<GetDashboardStructureMapResponse> GetDashboardMapAsync(GetDashboardStructureMapRequest request)
        {
            var param = new
            {
                request.StructureId
            };

            var lookup = new Dictionary<Guid, GetDashboardStructureMapResponse>();

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.QueryAsync(
                sql: Queries.GetDashboardMap,
                new[]
                {
                    typeof(GetDashboardStructureMapResponse),
                    typeof(DecimalGeodetic),
                    typeof(GetDashboardStructureMapSection),
                    typeof(DecimalGeodetic),
                    typeof(DecimalGeodetic),
                    typeof(DecimalGeodetic),
                    typeof(Model.Section._Shared.MapLineSetting.MapLineSetting),
                    typeof(GetDashboardStructureMapInstrument),
                    typeof(DecimalGeodetic)
                },
                (records) =>
                {
                    var map = records[0] as GetDashboardStructureMapResponse;
                    var mapSection = records[2] as GetDashboardStructureMapSection;

                    if (!lookup.TryGetValue(map.StructureId, out var dashboardMapResponse))
                    {
                        dashboardMapResponse = map;

                        var structureCoordinate = records[1] as DecimalGeodetic;

                        if (dashboardMapResponse.Datum != Datum.SIRGAS2000)
                        {
                            structureCoordinate = Helper.ToDatum(dashboardMapResponse.Datum, Datum.SIRGAS2000, structureCoordinate);
                        }

                        dashboardMapResponse.StructureCoordinate = structureCoordinate;

                        lookup.Add(dashboardMapResponse.StructureId, dashboardMapResponse);
                    }

                    if (dashboardMapResponse != null &&
                        mapSection != null &&
                        dashboardMapResponse.Sections.All(section => section.Id != mapSection.Id))
                    {
                        var upstreamCoordinate = records[3] as DecimalGeodetic;
                        var downstreamCoordinate = records[4] as DecimalGeodetic;
                        var midpointCoordinate = records[5] as DecimalGeodetic;

                        if (mapSection.Datum != Datum.SIRGAS2000)
                        {
                            upstreamCoordinate = Helper.ToDatum(mapSection.Datum, Datum.SIRGAS2000, upstreamCoordinate);
                            downstreamCoordinate = Helper.ToDatum(mapSection.Datum, Datum.SIRGAS2000, downstreamCoordinate);
                            midpointCoordinate = midpointCoordinate != null
                                ? Helper.ToDatum(mapSection.Datum, Datum.SIRGAS2000, midpointCoordinate)
                                : null;
                        }

                        mapSection.UpstreamCoordinate = upstreamCoordinate;
                        mapSection.DownstreamCoordinate = downstreamCoordinate;
                        mapSection.MidpointCoordinate = midpointCoordinate;
                        mapSection.MapLineSetting = records[6] as Model.Section._Shared.MapLineSetting.MapLineSetting;

                        dashboardMapResponse.Sections.Add(mapSection);
                    }
                    else if (dashboardMapResponse.Sections.Any(x => x.Id == mapSection.Id))
                    {
                        mapSection = dashboardMapResponse.Sections.First(x => x.Id == mapSection.Id);
                    }

                    if (records[7] is GetDashboardStructureMapInstrument instrument &&
                        !dashboardMapResponse.Instruments.Any(x => x.Id == instrument.Id))
                    {
                        var instrumentCoordinate = records[8] as DecimalGeodetic;

                        if (instrument.Datum != Datum.SIRGAS2000)
                        {
                            instrumentCoordinate = Helper.ToDatum(instrument.Datum, Datum.SIRGAS2000, instrumentCoordinate);
                        }

                        instrument.Coordinate = instrumentCoordinate;

                        dashboardMapResponse.Instruments.Add(instrument);
                    }

                    return map;
                },
                splitOn: "Latitude,Id,Latitude,Latitude,Latitude,Color,Id,Latitude",
                param: param);

            return lookup.Values.FirstOrDefault();
        }

        public async Task<IEnumerable<SearchStructureHistoryResponse>> SearchHistoryAsync(
            SearchStructureHistoryRequest request)
        {
            var param = new {
                StructureId = request.Id,
                Skip = request.GetSkip(),
                PageSize = request.PageSize,
            };
            
            using var connection = ApplicationDatabase.GetConnection(_connectionString);
            
            return await connection.QueryAsync<SearchStructureHistoryResponse>(
                sql: Queries.SearchHistory,
                new[]
                {
                    typeof(SearchStructureHistoryResponse),
                    typeof(Domain.Entities.User)
                },
                (records) =>
                {
                    var history = records[0] as SearchStructureHistoryResponse;
                    var user = records[1] as Domain.Entities.User;

                    return history with 
                    { 
                        ModifiedBy = new()
                        {
                            Id = user.Id,
                            FirstName = user.FirstName,
                            Surname = user.Surname,
                            Username = user.Username
                        } 
                    };
                },
                splitOn: "Id",
                param: param
            );
        }

        public async Task<int> CountHistoryAsync(
            SearchStructureHistoryRequest request)
        {
            var param = new
            {
                StructureId = request.Id
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.ExecuteScalarAsync<int>(Queries.CountHistory, param);
        }
        
        public async Task<GetStructureSimulationDataResponse> GetSimulationDataAsync(GetStructureSimulationDataRequest request)
        {
            var param = new
            {
                request.StructureId,
                LinimetricRulerType = InstrumentType.LinimetricRuler,
                InstrumentTypes = new [] 
                {
                    InstrumentType.OpenStandpipePiezometer,
                    InstrumentType.ElectricPiezometer,
                    InstrumentType.WaterLevelIndicator
                },
            };

            var lookup = new Dictionary<Guid, GetStructureSimulationDataResponse>();

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.QueryAsync(
                sql: Queries.GetWithReading,
                new[]
                {
                    typeof(GetStructureSimulationDataStructure),
                    typeof(CircularParameters),
                    typeof(NonCircularParameters),
                    typeof(CalculationMethod),
                    typeof(CalculationMethod),
                    typeof(Domain.Entities.Section),
                    typeof(bool),
                    typeof(double?),
                    typeof(Domain.Entities.Instrument),
                    typeof(Measurement),
                    typeof(ReadingValue),
                    typeof(SectionReview),
                    typeof(File),
                    typeof(Domain.Entities.ConstructionStage),
                    typeof(File),
                },
                (records) =>
                {
                    var structure = records[0] as GetStructureSimulationDataStructure;
                    var circularParameter = records[1] as CircularParameters;
                    var nonCircularParameter = records[2] as NonCircularParameters;
                    var circularCalculationMethod = (CalculationMethod?)records[3];
                    var nonCircularCalculationMethod = (CalculationMethod?)records[4];
                    var section = records[5] as Domain.Entities.Section;
                    var lastReviewHasDxf = (int?)records[6] == 1;
                    var beachLength = (double?)records[7];
                    var instrument = records[8] as Domain.Entities.Instrument;
                    var measurement = records[9] as Measurement;
                    var readingValue = records[10] as ReadingValue;
                    var sectionReview = records[11] as SectionReview;
                    var sectionReviewFile = records[12] as File;
                    var constructionStage = records[13] as Domain.Entities.ConstructionStage;
                    var constructionStageFile = records[14] as File;

                    if (!lookup.TryGetValue(structure.Id, out var result))
                    {
                        lookup.Add(structure.Id, new()
                        {
                            Structure = structure with { Slide2Configuration = new()
                            {
                                CircularParameters = circularParameter != null
                                    ? new()
                                    {
                                        CalculationMethods = circularParameter.CalculationMethods,
                                        CirclesPerDivision = circularParameter.CirclesPerDivision,
                                        CircularSearchMethod = circularParameter.CircularSearchMethod,
                                        DivisionsAlongSlope = circularParameter.DivisionsAlongSlope,
                                        DivisionsNextIteration = circularParameter.DivisionsNextIteration,
                                        NumberOfIterations = circularParameter.NumberOfIterations,
                                        NumberOfSurfaces = circularParameter.NumberOfSurfaces,
                                        RadiusIncrement = circularParameter.RadiusIncrement
                                    }
                                    : null,
                                NonCircularParameters = nonCircularParameter != null
                                    ? new()
                                    {
                                        CalculationMethods = nonCircularParameter.CalculationMethods,
                                        DivisionsAlongSlope = nonCircularParameter.DivisionsAlongSlope,
                                        DivisionsNextIteration = nonCircularParameter.DivisionsNextIteration,
                                        NumberOfIterations = nonCircularParameter.NumberOfIterations,
                                        NumberOfSurfaces = nonCircularParameter.NumberOfSurfaces,
                                        InitialNumberOfIterations = nonCircularParameter.InitialNumberOfIterations,
                                        InitialNumberOfSurfaceVertices = nonCircularParameter.InitialNumberOfSurfaceVertices,
                                        MaximumIterations = nonCircularParameter.MaximumIterations,
                                        MaximumNumberOfSteps = nonCircularParameter.MaximumNumberOfSteps,
                                        NonCircularSearchMethod = nonCircularParameter.NonCircularSearchMethod,
                                        NumberOfFactorsSafetyComparedBeforeStopping = nonCircularParameter.NumberOfFactorsSafetyComparedBeforeStopping,
                                        NumberOfNests = nonCircularParameter.NumberOfNests,
                                        NumberOfParticles = nonCircularParameter.NumberOfParticles,
                                        NumberOfVerticesAlongSurface = nonCircularParameter.NumberOfVerticesAlongSurface,
                                        SurfacesPerDivision = nonCircularParameter.SurfacesPerDivision,
                                        ToleranceForStoppingCriterion = nonCircularParameter.ToleranceForStoppingCriterion
                                    }
                                    : null
                            }, LinimetricRulers = new() },
                            Sections = new()
                        });

                        result = lookup[structure.Id];
                    }

                    if (circularCalculationMethod != null && !circularParameter.CalculationMethods.Contains((CalculationMethod)circularCalculationMethod))
                    {
                        circularParameter.CalculationMethods.Add((CalculationMethod)circularCalculationMethod);
                    }

                    if (nonCircularCalculationMethod != null && !nonCircularParameter.CalculationMethods.Contains((CalculationMethod)nonCircularCalculationMethod))
                    {
                        nonCircularParameter.CalculationMethods.Add((CalculationMethod)nonCircularCalculationMethod);
                    }

                    if (instrument != null
                        && instrument.Type == InstrumentType.LinimetricRuler
                        && !result.Structure.LinimetricRulers.Any(x => x.InstrumentId == instrument.Id))
                    {
                        result.Structure.LinimetricRulers.Add(new()
                        {
                            InstrumentId = instrument.Id,
                            InstrumentIdentifier = instrument.Identifier,
                            Online = instrument.Online,
                            Quota = readingValue?.Quota,
                            Type = instrument.Type,
                            Position = (LinimetricRulerPosition)instrument.LinimetricRulerPosition
                        });
                    }

                    var sectionResult = result.Sections.FirstOrDefault(x => x.Id == section.Id);

                    if (sectionResult == null && section != null)
                    {
                        sectionResult = new()
                        {
                            Id = section.Id,
                            Name = section.Name,
                            StructureId = section.StructureId,
                            BeachLength = beachLength,
                            MinimumUndrainedDepth = section.MinimumUndrainedDepth,
                            MinimumDrainedDepth = section.MinimumDrainedDepth,
                            MinimumPseudoStaticDepth = section.MinimumPseudoStaticDepth,
                            LastReviewHasDxf = lastReviewHasDxf,
                            Reviews = new(),
                            Instruments = new(),
                        };

                        result.Sections.Add(sectionResult);
                    }

                    if (instrument != null
                        && instrument.Type != InstrumentType.LinimetricRuler
                        && !sectionResult.Instruments.Any(x => x.InstrumentId == instrument.Id && x.MeasurementId == measurement?.Id))
                    {
                        sectionResult.Instruments.Add(new()
                        {
                            InstrumentId = instrument.Id,
                            MeasurementId = measurement?.Id,
                            InstrumentIdentifier = instrument.Identifier,
                            MeasurementIdentifier = measurement?.Identifier,
                            Online = instrument.Online,
                            Type = instrument.Type,
                            Dry = readingValue?.Dry,
                            Quota = readingValue?.Quota,
                            InstrumentBaseQuota = instrument.BaseQuota ?? measurement?.Quota,
                            InstrumentTopQuota = (decimal)instrument.TopQuota
                        });

                        sectionResult.HasInstruments = true;
                    }

                    if (sectionReview != null
                        && !sectionResult.Reviews.Any(x => x.Id == sectionReview.Id))
                    {
                        sectionResult.Reviews.Add(new()
                        {
                            Id = sectionReview.Id,
                            StartDate = sectionReview.StartDate,
                            Index = sectionReview.Index,
                            HasDxf = sectionReviewFile != null,
                            IsUnderConstruction = sectionReview.IsUnderConstruction,
                            DxfHasWaterline = sectionReview.DxfHasWaterline,
                        });
                    }

                    if (constructionStage != null)
                    {
                        var review = sectionResult.Reviews.FirstOrDefault(x => x.Id == constructionStage.SectionReviewId);

                        if (review != null && !review.ConstructionStages.Any(x => x.Id == constructionStage.Id))    
                        {
                            review.ConstructionStages.Add(new()
                            {
                                Id = constructionStage.Id,
                                HasDxf = constructionStageFile != null,
                                IsCurrentStage = constructionStage.IsCurrentStage,
                                Stage = constructionStage.Stage,
                                DxfHasWaterline = sectionReview.DxfHasWaterline
                            });

                        }
                    }

                    return result;
                },
                splitOn: "CircularSearchMethod,NonCircularSearchMethod,CalculationMethods,CalculationMethods,Id,LastReviewHasDxf,Length,Id,Id,Id,Id,Name,Id,Name",
                param: param
            );

            return lookup.Values.FirstOrDefault();
        }

        public async Task BindAspectAsync(Guid structureId, AspectStructure aspect)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            
            var param = new
            {
                aspect.Id,
                StructureId = structureId,
                AspectId = aspect.Aspect.Id,
                aspect.Index
            };

            await connection
                .ExecuteAsync(Queries.InsertAspectStructure, param);
        }

        public async Task<int> GetLastAspectIndex(Guid structureId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();

            return await connection.QuerySingleOrDefaultAsync<int>(
                Queries.GetLastAspectIndexByStructure, 
                new { StructureId = structureId });
        }
        
        public async Task<MapConfiguration> GetStructureMapConfiguration(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.QueryFirstAsync<MapConfiguration>(
                sql: Queries.GetMapConfiguration,
                param: new { Id = id });
        }

        public async Task UpdateActiveAsync(Domain.Entities.Structure structure)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                await connection.ExecuteAsync(Queries.UpdateOnlyActive, new
                {
                    structure.Id,
                    structure.Active
                }, transaction);

                if (!structure.Active)
                {
                    await connection.ExecuteAsync(OutboxQueries.Insert, new
                    {
                        Id = Guid.NewGuid(),
                        Type = typeof(NotificationCommand).AssemblyQualifiedName,
                        Data = JsonSerializer.Serialize(new NotificationCommand()
                        {
                            Id = structure.Id,
                            Theme = NotificationTheme.StructureDeleted,
                            CreatedById = structure.History.Any()
                                ? structure.History
                                    .OrderByDescending(x => x.CreatedDate)
                                    .FirstOrDefault()!.ModifiedBy.Id
                                : Guid.Empty,
                            CreatedDate = DateTime.UtcNow
                        })
                    }, transaction);
                }

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<IEnumerable<ListStructureResponse>> ListAsync(ListStructureRequest request)
        {
            var param = new
            {
                request.OnlyWithTailingsBeach,
                TailingsBeachEnum = (int)Activity.TailingsBeach,
                request.ClientUnitId,
                request.RequestedBySuperSupport,
                StructureIds = request.RequestedUserStructures,
                request.Active
            };

            var builder = new SqlBuilder();

            if (request.ClientUnitId.HasValue)
            {
                builder.Where("[client-unit-id] = @ClientUnitId");
            }

            if (!request.RequestedBySuperSupport)
            {
                builder.Where("[structures].[id] IN @StructureIds");
            }

            if (request.OnlyWithTailingsBeach == true)
            {
                builder.Where(@"[structures].[id] IN (
                    SELECT [S].[id] FROM [structures] [S] 
                    JOIN [structure-types] ON [structure-types].id = structures.[structure-type-id] 
                    JOIN [structure-type-activities] ON [structure-type-activities].[structure-type-id] = [structure-types].[id] 
                    WHERE [structure-type-activities].[activity] = @TailingsBeachEnum)");
            }

            if (request.Active.HasValue)
            {
                builder.Where("[structures].[active] = @Active");
            }

            var query = builder.AddTemplate(Queries.List);

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.QueryAsync<ListStructureResponse>(
                sql: query.RawSql,
                new[]
                {
                    typeof(Domain.Entities.Structure),
                    typeof(CoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm)
                },
                (records) =>
                {
                    var structure = records[0] as Domain.Entities.Structure;
                    var coordinateSetting = records[1] as CoordinateSetting;
                    var decimalCoordinate = records[2] as DecimalGeodetic;
                    var utmCoordinate = records[3] as Utm;

                    coordinateSetting.Systems = new()
                    {
                        DecimalGeodetic = decimalCoordinate,
                        Utm = utmCoordinate
                    };

                    return new()
                    {
                        Id = structure.Id,
                        CoordinateSetting = coordinateSetting,
                        Name = structure.Name,
                        Active = structure.Active
                    };
                },
                splitOn: "Datum,Latitude,Northing",
                param: param);
        }

        public async Task<IEnumerable<Domain.Entities.Structure>> GetByFiltersMapsAsync(GetStructureByFiltersMapsRequest request)
        {
            var param = new
            {
                request.RequestedUserStructures,
                request.RequestedBySuperSupport,
                request.ClientId,
                HasStructureIds = request.StructureIds != null && request.StructureIds.Any(),
                request.StructureIds
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.QueryAsync(
                sql: Queries.GetByFiltersMaps,
                new[]
                {
                    typeof(Domain.Entities.Structure),
                    typeof(CoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm)
                },
                (records) =>
                {
                    var structureDb = records[0] as Domain.Entities.Structure;
                    var coordinateSetting = records[1] as CoordinateSetting;
                    var decimalCoordinate = records[2] as DecimalGeodetic;
                    var utmCoordinate = records[3] as Utm;

                    coordinateSetting.Systems = new()
                    {
                        DecimalGeodetic = decimalCoordinate,
                        Utm = utmCoordinate
                    };

                    structureDb.CoordinateSetting = coordinateSetting;

                    return structureDb;
                },
                splitOn: "Datum,Latitude,Northing",
                param: param);
        }

        public async Task<IEnumerable<SearchStructureResponse>> SearchAsync(SearchStructureRequest request)
        {
            var param = new
            {
                request.Name,
                request.Id,
                request.Status,
                request.ClientId,
                request.Active,
                request.ClientUnitId,
                request.SearchIdentifier,
                Skip = request.GetSkip(),
                request.PageSize,
                request.RequestedBySuperSupport,
                StructureIds = request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.QueryAsync<SearchStructureResponse>(
                sql: Queries.Search,
                new[]
                {
                    typeof(Domain.Entities.Structure),
                    typeof(Domain.Entities.ClientUnit)
                },
                (records) =>
                {
                    var structure = records[0] as Domain.Entities.Structure;
                    var clientUnit = records[1] as Domain.Entities.ClientUnit;

                    return new()
                    {
                        Id = structure.Id,
                        Status = structure.Status,
                        ClientUnit = new()
                        {
                            Id = clientUnit.Id,
                            Name = clientUnit.Name,
                            Active = clientUnit.Active,
                            ClientId = clientUnit.ClientId
                        },
                        Name = structure.Name,
                        Active = structure.Active,
                        SearchIdentifier = structure.SearchIdentifier
                    };
                },
                param);
        }

        public async Task<int> CountAsync(SearchStructureRequest request)
        {
            var param = new
            {
                request.Name,
                request.Id,
                request.Status,
                request.ClientId,
                request.ClientUnitId,
                request.Active,
                request.SearchIdentifier,
                request.RequestedBySuperSupport,
                StructureIds = request.RequestedUserStructures
            };

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            return await connection.ExecuteScalarAsync<int>(Queries.Count, param);
        }

        public async Task AddAsync(Domain.Entities.Structure structure)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                var param = new
                {
                    structure.Id,
                    structure.Name,
                    StructureTypeId = structure.StructureType.Id,
                    structure.Protocol,
                    structure.Status,
                    structure.CoordinateSetting.Systems.DecimalGeodetic.Latitude,
                    structure.CoordinateSetting.Systems.DecimalGeodetic.Longitude,
                    structure.CoordinateSetting.Systems.Utm.Northing,
                    structure.CoordinateSetting.Systems.Utm.Easting,
                    structure.CoordinateSetting.Systems.Utm.ZoneNumber,
                    structure.CoordinateSetting.Systems.Utm.ZoneLetter,
                    structure.CoordinateSetting.Datum,
                    CoordinateFormat = structure.CoordinateSetting.Format,
                    ClientUnitId = structure.ClientUnit.Id,
                    structure.ShouldEvaluateDrainedCondition,
                    structure.ShouldEvaluateUndrainedCondition,
                    structure.ShouldEvaluatePseudoStaticCondition,
                    MapGeneralZoom = structure.MapConfiguration.GeneralZoom,
                    MapInstrumentsZoom = structure.MapConfiguration.InstrumentsZoom,
                    structure.HasAutoUpdate,
                    IntervalFetchData = structure.FrequencyToFetchData.Interval,
                    IntervalGeneratePackage = structure.FrequencyToGeneratePackages.Interval,
                    SeismicCoefficientHorizontal = structure.SeismicCoefficient.Horizontal,
                    SeismicCoefficientVertical = structure.SeismicCoefficient.Vertical,
                    structure.Gravity,
                    CityId = structure.City?.Id,
                    structure.Purpose,
                    structure.ConstructionStages,
                    CrestLength = structure.CrestDimension?.Length,
                    CrestWidth = structure.CrestDimension?.Width,
                    structure.TotalHeight,
                    structure.DownstreamSlope,
                    structure.UpstreamSlope,
                    structure.Classification,
                    structure.SectionType,
                    structure.FoundationType,
                    structure.RaisingMethod,
                    structure.ExpectedElevations,
                    structure.ElevationsMade,
                    structure.ReservoirDesignVolume,
                    structure.CurrentReservoirVolume,
                    structure.InternalDrainage,
                    structure.SuperficialDrainage,
                    structure.BasinAreaInSquareKilometers,
                    structure.ProjectPrecipitation,
                    structure.FullOfProject,
                    structure.MaximumInfluentFlow,
                    structure.ProjectFlow,
                    structure.NormalMaximumWaterLevel,
                    structure.MaximumWaterLevelMaximorum,
                    structure.FreeboardNormalMaximumWaterLevel,
                    structure.FreeboardMaximumWaterLevelMaximorum,
                    structure.Spillway,
                    structure.Slide2Configuration.CircularParameters?.CircularSearchMethod,
                    CircularDivisionsAlongSlope = structure.Slide2Configuration.CircularParameters?.DivisionsAlongSlope,
                    structure.Slide2Configuration.CircularParameters?.CirclesPerDivision,
                    CircularNumberOfIterations = structure.Slide2Configuration.CircularParameters?.NumberOfIterations,
                    CircularDivisionsNextIteration = structure.Slide2Configuration.CircularParameters?.DivisionsNextIteration,
                    structure.Slide2Configuration.CircularParameters?.RadiusIncrement,
                    CircularNumberOfSurfaces = structure.Slide2Configuration.CircularParameters?.NumberOfSurfaces,
                    structure.Slide2Configuration.NonCircularParameters?.NonCircularSearchMethod,
                    NonCircularDivisionsAlongSlope = structure.Slide2Configuration.NonCircularParameters?.DivisionsAlongSlope,
                    structure.Slide2Configuration.NonCircularParameters?.SurfacesPerDivision,
                    NonCircularNumberOfIterations = structure.Slide2Configuration.NonCircularParameters?.NumberOfIterations,
                    NonCircularDivisionsNextIteration = structure.Slide2Configuration.NonCircularParameters?.DivisionsNextIteration,
                    structure.Slide2Configuration.NonCircularParameters?.NumberOfVerticesAlongSurface,
                    NonCircularNumberOfSurfaces = structure.Slide2Configuration.NonCircularParameters?.NumberOfSurfaces,
                    structure.Slide2Configuration.NonCircularParameters?.NumberOfNests,
                    structure.Slide2Configuration.NonCircularParameters?.MaximumIterations,
                    structure.Slide2Configuration.NonCircularParameters?.InitialNumberOfSurfaceVertices,
                    structure.Slide2Configuration.NonCircularParameters?.InitialNumberOfIterations,
                    structure.Slide2Configuration.NonCircularParameters?.MaximumNumberOfSteps,
                    NumberOfFactorsSafetyCompared = structure.Slide2Configuration.NonCircularParameters?.NumberOfFactorsSafetyComparedBeforeStopping,
                    structure.Slide2Configuration.NonCircularParameters?.ToleranceForStoppingCriterion,
                    structure.Slide2Configuration.NonCircularParameters?.NumberOfParticles,
                    structure.ConstructionYear,
                    structure.StartOfOperationDate,
                    structure.EndOfOperationDate,
                    structure.DesigningCompany,
                    structure.CurrentStatus,
                    structure.CrestQuota,
                    structure.SpillwaySillQuota,
                    structure.InterceptedWatercourse
                };

                await connection
                   .ExecuteAsync(Queries.Insert, param, transaction);

                if (structure.Slide2Configuration
                    .CircularParameters?.CalculationMethods != null)
                {
                    foreach (var m in structure.Slide2Configuration.CircularParameters.CalculationMethods)
                    {
                        await connection
                            .ExecuteAsync(Queries.InsertCircularCalculationMethod, new
                            {
                                StructureId = structure.Id,
                                CalculationMethod = m
                            }, transaction);
                    }
                }

                if (structure.Slide2Configuration
                    .NonCircularParameters?.CalculationMethods != null)
                {
                    foreach (var m in structure.Slide2Configuration.NonCircularParameters.CalculationMethods)
                    {
                        await connection
                            .ExecuteAsync(Queries.InsertNonCircularCalculationMethod, new
                            {
                                StructureId = structure.Id,
                                CalculationMethod = m
                            }, transaction);
                    }
                }

                foreach (var aspect in structure.Aspects)
                {
                    var aspectParam = new
                    {
                        aspect.Id,
                        StructureId = structure.Id,
                        AspectId = aspect.Aspect.Id,
                        aspect.Index
                    };

                    await connection
                        .ExecuteAsync(Queries.InsertAspectStructure, aspectParam, transaction);
                }

                foreach (var responsible in structure.Responsibles)
                {
                    var responsibleParam = new
                    {
                        StructureId = structure.Id,
                        ResponsibleId = responsible.Id
                    };

                    await connection
                        .ExecuteAsync(
                            Queries.InsertResponsibleStructures, responsibleParam, transaction);
                }

                foreach (var nature in structure.Natures)
                {
                    var natureParam = new
                    {
                        nature.Id,
                        StructureId = structure.Id,
                        nature.Description
                    };

                    await connection
                        .ExecuteAsync(
                            Queries.InsertNature, natureParam, transaction);
                }

                if (structure.EnvironmentalDamagePotential != null)
                {
                    var environmentalDamagePotentialParam = new
                    {
                        structure.EnvironmentalDamagePotential.Id,
                        StructureId = structure.Id,
                        structure.EnvironmentalDamagePotential.TotalReservoirVolume,
                        structure.EnvironmentalDamagePotential.PopulationDownstream,
                        structure.EnvironmentalDamagePotential.EnvironmentalImpact,
                        structure.EnvironmentalDamagePotential.SocioeconomicImpact,
                        structure.EnvironmentalDamagePotential.EnvironmentalDamagePotentialTotal
                    };

                    await connection
                        .ExecuteAsync(
                            Queries.InsertEnvironmentalDamagePotential, environmentalDamagePotentialParam, transaction);
                }

                foreach (var layer in structure.Layers)
                {
                    var layerParam = new
                    {
                        layer.Id,
                        StructureId = structure.Id,
                        FileName = layer.File.Name,
                        FileUniqueName = layer.File.UniqueName,
                        LayerType = layer.Type,
                    };

                    await connection
                        .ExecuteAsync(Queries.InsertLayersStructure, layerParam, transaction);
                }

                var historyParam = new
                {
                    Id = structure.History[0].Id,
                    StructureId = structure.Id,
                    ModifiedByUserId = structure.History[0].ModifiedBy.Id,
                    Changes = structure.History[0].Changes,
                    CreatedDate = structure.History[0].CreatedDate
                };
                
                await connection
                    .ExecuteAsync(Queries.InsertHistory, historyParam, transaction);
                
                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public Task DeleteAsync(Guid id)
        {
            throw new NotImplementedException();
        }

        public async Task<Domain.Entities.Structure> GetBySearchIdentifierAsync(int searchIdentifier)
        {
            var template = new SqlBuilder()
                .Where(
                    "[structures].[search-identifier] = @SearchIdentifier", new { SearchIdentifier = searchIdentifier })
                .AddTemplate(Queries.GetBySearchIdentifier);
                
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            
            return await connection
                .QueryFirstOrDefaultAsync<Domain.Entities.Structure>(
                    template.RawSql, 
                    template.Parameters);
        }

        public async Task<IEnumerable<Domain.Entities.Structure>> GetBySearchIdentifiersAsync(
            IEnumerable<int> searchIdentifiers)
        {
            var template = new SqlBuilder()
                .Where(
                    "[structures].[search-identifier] IN @SearchIdentifiers", new { SearchIdentifiers = searchIdentifiers } )
                .AddTemplate(Queries.GetBySearchIdentifier);
                
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            
            return await connection
                .QueryAsync<Domain.Entities.Structure>(
                    template.RawSql, 
                    template.Parameters);
        }

        public async Task<Domain.Entities.Structure> GetBasicAsync(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var lookup = new Dictionary<Guid, Domain.Entities.Structure>();

            return (await connection.QueryAsync(
                sql: Queries.GetBasic,
                new[]
                {
                    typeof(Domain.Entities.Structure),
                    typeof(CoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(Domain.Entities.ClientUnit),
                    typeof(Domain.Entities.Client),
                    typeof(Domain.Entities.StructureType),
                    typeof(StructureTypeActivity),
                },
                (records) =>
                {
                    var structureDb = records[0] as Domain.Entities.Structure;
                    var coordinateSetting = records[1] as CoordinateSetting;
                    var decimalCoordinate = records[2] as DecimalGeodetic;
                    var utmCoordinate = records[3] as Utm;
                    var clientUnit = records[4] as Domain.Entities.ClientUnit;
                    var client = records[5] as Domain.Entities.Client;
                    var structureType = records[6] as Domain.Entities.StructureType;

                    if (!lookup.TryGetValue(structureDb.Id, out var structure))
                    {
                        lookup.Add(structureDb.Id, structureDb);
                        structure = structureDb;
                        coordinateSetting.Systems = new()
                        {
                            DecimalGeodetic = decimalCoordinate,
                            Utm = utmCoordinate
                        };

                        structure.Client = client;
                        structure.ClientUnit = clientUnit;
                        structure.CoordinateSetting = coordinateSetting;
                        structure.StructureType = structureType;
                    }

                    if (records[7] is StructureTypeActivity structureTypeActivity)
                    {
                        structure.StructureType.AddActivity(structureTypeActivity);
                    }

                    return structure;
                },
                splitOn: "Datum,Latitude,Northing,Id,Id,Id,Id",
                param: new { Id = id }
                )).FirstOrDefault();
        }

        public async Task<Domain.Entities.Structure> GetStructureStabilityAnalysisData(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var lookup = new Dictionary<Guid, Domain.Entities.Structure>();

            await connection.QueryAsync(
                sql: Queries.GetStabilityAnalysisData,
                new[]
                {
                    typeof(Domain.Entities.Structure),
                    typeof(Orientation),
                    typeof(Domain.Entities.CircularParameters),
                    typeof(Domain.Entities.NonCircularParameters),
                    typeof(CalculationMethod),
                    typeof(CalculationMethod)
                },
                (records) =>
                {
                    var structureDb = records[0] as Domain.Entities.Structure;
                    var seismicCoefficient = records[1] as Orientation;
                    var circularParameter = records[2] as Domain.Entities.CircularParameters;
                    var nonCircularParameter = records[3] as Domain.Entities.NonCircularParameters;
                    var circularCalculationMethod = (CalculationMethod?)records[4];
                    var nonCircularCalculationMethod = (CalculationMethod?)records[5];

                    if (!lookup.TryGetValue(structureDb.Id, out var structure))
                    {
                        lookup.Add(structureDb.Id, structureDb);
                        structure = structureDb;
                        structure.Slide2Configuration = new()
                        {
                            CircularParameters = circularParameter,
                            NonCircularParameters = nonCircularParameter
                        };
                    }

                    structure.SeismicCoefficient = seismicCoefficient;

                    if (circularCalculationMethod != null)
                    {
                        structure.AddCircularCalculationMethod((CalculationMethod)circularCalculationMethod);
                    }

                    if (nonCircularCalculationMethod != null)
                    {
                        structure.AddNonCircularCalculationMethod((CalculationMethod)nonCircularCalculationMethod);
                    }

                    return structure;
                },
                splitOn: "Horizontal,CircularSearchMethod,NonCircularSearchMethod,CalculationMethods,CalculationMethods",
                param: new { Id = id }
                );

            return lookup.Values.FirstOrDefault();
        }

        public async Task<Domain.Entities.Structure> GetAsync(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var lookup = new Dictionary<Guid, Domain.Entities.Structure>();

            await connection.QueryAsync(
                sql: Queries.GetById,
                new[]
                {
                    typeof(Domain.Entities.Structure),
                    typeof(CoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(Domain.Entities.StructureType),
                    typeof(StructureTypeActivity),
                    typeof(Domain.Entities.ClientUnit),
                    typeof(MapConfiguration),
                    typeof(AutomationFrequency),
                    typeof(AutomationFrequency),
                    typeof(Orientation),
                    typeof(Domain.Entities.City),
                    typeof(Domain.Entities.State),
                    typeof(Domain.Entities.Country),
                    typeof(Dimension),
                    typeof(Domain.Entities.Responsible),
                    typeof(Email),
                    typeof(Domain.Entities.Position),
                    typeof(Domain.Entities.Role),
                    typeof(Domain.Entities.CircularParameters),
                    typeof(Domain.Entities.NonCircularParameters),
                    typeof(CalculationMethod),
                    typeof(CalculationMethod),
                    typeof(AspectStructure),
                    typeof(Domain.Entities.Aspect),
                    typeof(Domain.Entities.Area),
                    typeof(Domain.Entities.Section),
                    typeof(SectionReview),
                    typeof(Domain.Entities.Client),
                    typeof(EnvironmentalDamagePotential),
                    typeof(Layer),
                    typeof(File)
                },
                (records) =>
                {
                    var structureDb = records[0] as Domain.Entities.Structure;
                    var coordinateSetting = records[1] as CoordinateSetting;
                    var decimalCoordinate = records[2] as DecimalGeodetic;
                    var utmCoordinate = records[3] as Utm;

                    var structureType = records[4] as Domain.Entities.StructureType;
                    var clientUnit = records[6] as Domain.Entities.ClientUnit;
                    var mapConfig = records[7] as MapConfiguration;

                    var frequencyToFetchData = records[8] as AutomationFrequency;
                    var frequencyToGeneratePackages = records[9] as AutomationFrequency;
                    var seismicCoefficient = records[10] as Orientation;
                    var city = records[11] as Domain.Entities.City;
                    var state = records[12] as Domain.Entities.State;
                    var country = records[13] as Domain.Entities.Country;
                    var crestDimension = records[14] as Dimension;

                    var email = records[16] as Email;
                    var position = records[17] as Domain.Entities.Position;
                    var role = records[18] as Domain.Entities.Role;

                    var circularParameter = records[19] as Domain.Entities.CircularParameters;
                    var nonCircularParameter = records[20] as Domain.Entities.NonCircularParameters;
                    var circularCalculationMethod = (CalculationMethod?)records[21];
                    var nonCircularCalculationMethod = (CalculationMethod?)records[22];

                    var aspect = records[24] as Domain.Entities.Aspect;
                    var area = records[25] as Domain.Entities.Area;

                    var sectionReview = records[27] as SectionReview;

                    var client = records[28] as Domain.Entities.Client;

                    if (!lookup.TryGetValue(structureDb.Id, out var structure))
                    {
                        lookup.Add(structureDb.Id, structureDb);
                        structure = structureDb;
                        structure.StructureType = structureType;
                        structure.Slide2Configuration = new()
                        {
                            CircularParameters = circularParameter,
                            NonCircularParameters = nonCircularParameter
                        };
                    }

                    coordinateSetting.Systems = new()
                    {
                        DecimalGeodetic = decimalCoordinate,
                        Utm = utmCoordinate
                    };

                    structure.City = city;
                    structure.State = state;
                    structure.Country = country;
                    structure.CrestDimension = crestDimension;
                    structure.SeismicCoefficient = seismicCoefficient;
                    structure.FrequencyToFetchData = frequencyToFetchData;
                    structure.FrequencyToGeneratePackages = frequencyToGeneratePackages;
                    structure.MapConfiguration = mapConfig;
                    structure.ClientUnit = clientUnit;
                    structure.Client = client;
                    structure.CoordinateSetting = coordinateSetting;

                    if (records[5] is StructureTypeActivity structureTypeActivity)
                    {
                        structure.StructureType.AddActivity(structureTypeActivity);
                    }

                    if (records[15] is Domain.Entities.Responsible responsible)
                    {
                        responsible.EmailAddress = email;
                        responsible.Position = position;
                        responsible.Role = role;

                        structure.AddResponsible(responsible);
                    }

                    if (circularCalculationMethod != null)
                    {
                        structure.AddCircularCalculationMethod((CalculationMethod)circularCalculationMethod);
                    }

                    if (nonCircularCalculationMethod != null)
                    {
                        structure.AddNonCircularCalculationMethod((CalculationMethod)nonCircularCalculationMethod);
                    }

                    if (records[23] is AspectStructure aspectStructure)
                    {
                        aspect.Area = area;
                        aspectStructure.Aspect = aspect;
                        structure.AddAspect(aspectStructure);
                    }

                    if (records[26] is Domain.Entities.Section section)
                    {
                        section.AddSectionReview(sectionReview);
                        structure.AddSection(section);
                    }

                    if (records[29] is EnvironmentalDamagePotential environmentalDamagePotential)
                    {
                        structure.EnvironmentalDamagePotential = environmentalDamagePotential;
                    }

                    if (records[30] is Layer layer)
                    {
                        var file = records[31] as File;
                        layer.File = file;
                        structure.AddLayer(layer);
                    }

                    return structure;
                },
                splitOn: "Datum,Latitude,Northing,Id,Id,Id,GeneralZoom,Interval,Interval,Horizontal,Id,Id,Id,Length,Id,Value,Id,Id,CircularSearchMethod,NonCircularSearchMethod,CalculationMethods,CalculationMethods,Id,Id,Id,Id,Id,Id,Id,Id,Name",
                param: new { Id = id }
                );

            return lookup.Values.FirstOrDefault();
        }

        public async Task UpdateAsync(Domain.Entities.Structure structure)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                var param = new
                {
                    structure.Id,
                    structure.Name,
                    StructureTypeId = structure.StructureType.Id,
                    structure.Protocol,
                    structure.Status,
                    structure.CoordinateSetting.Systems.DecimalGeodetic.Latitude,
                    structure.CoordinateSetting.Systems.DecimalGeodetic.Longitude,
                    structure.CoordinateSetting.Systems.Utm.Northing,
                    structure.CoordinateSetting.Systems.Utm.Easting,
                    structure.CoordinateSetting.Systems.Utm.ZoneNumber,
                    structure.CoordinateSetting.Systems.Utm.ZoneLetter,
                    structure.CoordinateSetting.Datum,
                    CoordinateFormat = structure.CoordinateSetting.Format,
                    ClientUnitId = structure.ClientUnit.Id,
                    structure.ShouldEvaluateDrainedCondition,
                    structure.ShouldEvaluateUndrainedCondition,
                    structure.ShouldEvaluatePseudoStaticCondition,
                    MapGeneralZoom = structure.MapConfiguration.GeneralZoom,
                    MapInstrumentsZoom = structure.MapConfiguration.InstrumentsZoom,
                    structure.HasAutoUpdate,
                    IntervalFetchData = structure.FrequencyToFetchData.Interval,
                    IntervalGeneratePackage = structure.FrequencyToGeneratePackages.Interval,
                    SeismicCoefficientHorizontal = structure.SeismicCoefficient.Horizontal,
                    SeismicCoefficientVertical = structure.SeismicCoefficient.Vertical,
                    structure.Gravity,
                    CityId = structure.City?.Id,
                    structure.Purpose,
                    structure.ConstructionStages,
                    CrestLength = structure.CrestDimension?.Length,
                    CrestWidth = structure.CrestDimension?.Width,
                    structure.TotalHeight,
                    structure.DownstreamSlope,
                    structure.UpstreamSlope,
                    structure.Classification,
                    structure.SectionType,
                    structure.FoundationType,
                    structure.RaisingMethod,
                    structure.ExpectedElevations,
                    structure.ElevationsMade,
                    structure.ReservoirDesignVolume,
                    structure.CurrentReservoirVolume,
                    structure.InternalDrainage,
                    structure.SuperficialDrainage,
                    structure.BasinAreaInSquareKilometers,
                    structure.ProjectPrecipitation,
                    structure.FullOfProject,
                    structure.MaximumInfluentFlow,
                    structure.ProjectFlow,
                    structure.NormalMaximumWaterLevel,
                    structure.MaximumWaterLevelMaximorum,
                    structure.FreeboardNormalMaximumWaterLevel,
                    structure.FreeboardMaximumWaterLevelMaximorum,
                    structure.Spillway,
                    structure.Slide2Configuration.CircularParameters?.CircularSearchMethod,
                    CircularDivisionsAlongSlope = structure.Slide2Configuration.CircularParameters?.DivisionsAlongSlope,
                    structure.Slide2Configuration.CircularParameters?.CirclesPerDivision,
                    CircularNumberOfIterations = structure.Slide2Configuration.CircularParameters?.NumberOfIterations,
                    CircularDivisionsNextIteration = structure.Slide2Configuration.CircularParameters?.DivisionsNextIteration,
                    structure.Slide2Configuration.CircularParameters?.RadiusIncrement,
                    CircularNumberOfSurfaces = structure.Slide2Configuration.CircularParameters?.NumberOfSurfaces,
                    structure.Slide2Configuration.NonCircularParameters?.NonCircularSearchMethod,
                    NonCircularDivisionsAlongSlope = structure.Slide2Configuration.NonCircularParameters?.DivisionsAlongSlope,
                    structure.Slide2Configuration.NonCircularParameters?.SurfacesPerDivision,
                    NonCircularNumberOfIterations = structure.Slide2Configuration.NonCircularParameters?.NumberOfIterations,
                    NonCircularDivisionsNextIteration = structure.Slide2Configuration.NonCircularParameters?.DivisionsNextIteration,
                    structure.Slide2Configuration.NonCircularParameters?.NumberOfVerticesAlongSurface,
                    NonCircularNumberOfSurfaces = structure.Slide2Configuration.NonCircularParameters?.NumberOfSurfaces,
                    structure.Slide2Configuration.NonCircularParameters?.NumberOfNests,
                    structure.Slide2Configuration.NonCircularParameters?.MaximumIterations,
                    structure.Slide2Configuration.NonCircularParameters?.InitialNumberOfSurfaceVertices,
                    structure.Slide2Configuration.NonCircularParameters?.InitialNumberOfIterations,
                    structure.Slide2Configuration.NonCircularParameters?.MaximumNumberOfSteps,
                    NumberOfFactorsSafetyCompared = structure.Slide2Configuration.NonCircularParameters?.NumberOfFactorsSafetyComparedBeforeStopping,
                    structure.Slide2Configuration.NonCircularParameters?.ToleranceForStoppingCriterion,
                    structure.Slide2Configuration.NonCircularParameters?.NumberOfParticles,
                    structure.ConstructionYear,
                    structure.StartOfOperationDate,
                    structure.EndOfOperationDate,
                    structure.DesigningCompany,
                    structure.CurrentStatus,
                    structure.CrestQuota,
                    structure.SpillwaySillQuota,
                    structure.InterceptedWatercourse
                };

                await connection
                   .ExecuteAsync(Queries.Update, param, transaction);

                if (structure.Slide2Configuration.CircularParameters?.CalculationMethods != null)
                {
                    foreach (var m in structure.Slide2Configuration.CircularParameters.CalculationMethods)
                    {
                        var methodParam = new
                        {
                            StructureId = structure.Id,
                            CalculationMethod = m
                        };

                        var calculationMethodExists = await connection.QueryFirstAsync<int>(
                            Queries.CheckIfTheCircularMethodExists, methodParam,
                            transaction);

                        if (calculationMethodExists == 0)
                        {
                            await connection
                                .ExecuteAsync(Queries.InsertCircularCalculationMethod, methodParam, transaction);
                        }
                    }
                }

                await connection.ExecuteAsync(
                    Queries.DeleteCircularCalculationMethod,
                    new
                    {
                        StructureId = structure.Id,
                        structure.Slide2Configuration.CircularParameters?.CalculationMethods
                    }, transaction);

                if (structure.Slide2Configuration.NonCircularParameters?.CalculationMethods != null)
                {
                    foreach (var m in structure.Slide2Configuration.NonCircularParameters.CalculationMethods)
                    {
                        var methodParam = new
                        {
                            StructureId = structure.Id,
                            CalculationMethod = m
                        };

                        var calculationMethodExists = await connection.QueryFirstAsync<int>(
                            Queries.CheckIfTheNonCircularMethodExists, methodParam,
                            transaction);

                        if (calculationMethodExists == 0)
                        {
                            await connection
                            .ExecuteAsync(Queries.InsertNonCircularCalculationMethod, methodParam, transaction);
                        }
                    }
                }

                await connection.ExecuteAsync(
                    Queries.DeleteNonCircularCalculationMethod,
                    new
                    {
                        StructureId = structure.Id,
                        structure.Slide2Configuration.NonCircularParameters?.CalculationMethods
                    }, transaction);

                foreach (var aspect in structure.Aspects)
                {
                    var aspectParam = new
                    {
                        aspect.Id,
                        StructureId = structure.Id,
                        AspectId = aspect.Aspect.Id,
                        aspect.Index
                    };

                    await connection.ExecuteAsync(Queries.UpsertAspectStructure, aspectParam, transaction);
                }

                await connection.ExecuteAsync(
                    Queries.DeleteAspectStructure,
                    new
                    {
                        StructureId = structure.Id,
                        Ids = structure.Aspects.Select(a => a.Id)
                    }, transaction);

                foreach (var responsible in structure.Responsibles)
                {
                    var responsibleParam = new
                    {
                        StructureId = structure.Id,
                        ResponsibleId = responsible.Id
                    };

                    var responsibleExists = await connection.QueryFirstAsync<int>(
                        Queries.CheckIfTheResponsibleExists, responsibleParam,
                        transaction);

                    if (responsibleExists == 0)
                    {
                        await connection
                            .ExecuteAsync(Queries.InsertResponsibleStructures, responsibleParam, transaction);
                    }
                }

                await connection.ExecuteAsync(
                    Queries.DeleteResponsibleStructure,
                    new
                    {
                        StructureId = structure.Id,
                        Responsibles = structure.Responsibles.Select(x => x.Id)
                    }, transaction);

                if (structure.EnvironmentalDamagePotential != null)
                {
                    var envDamageParam = new
                    {
                        structure.EnvironmentalDamagePotential.Id,
                        StructureId = structure.Id,
                        structure.EnvironmentalDamagePotential.TotalReservoirVolume,
                        structure.EnvironmentalDamagePotential.PopulationDownstream,
                        structure.EnvironmentalDamagePotential.EnvironmentalImpact,
                        structure.EnvironmentalDamagePotential.SocioeconomicImpact,
                        structure.EnvironmentalDamagePotential.EnvironmentalDamagePotentialTotal
                    };

                    await connection
                        .ExecuteAsync(Queries.UpsertEnvironmentalDamagePotential, envDamageParam, transaction);

                }
                else
                {
                    await connection.ExecuteAsync(
                    Queries.DeleteEnvironmentalDamagePotential,
                    new
                    {
                        StructureId = structure.Id
                    }, transaction);
                }

                foreach (var layer in structure.Layers)
                {
                    var layerParam = new
                    {
                        layer.Id,
                        StructureId = structure.Id,
                        FileName = layer.File.Name,
                        FileUniqueName = layer.File.UniqueName,
                        LayerType = layer.Type,
                    };

                    await connection
                        .ExecuteAsync(Queries.UpsertLayersStructure, layerParam, transaction);

                }

                await connection.ExecuteAsync(
                    Queries.DeleteLayersStructure,
                    new
                    {
                        StructureId = structure.Id,
                        Ids = structure.Layers.Select(a => a.Id)
                    }, transaction);
                
                var historyParams = structure.History.Select(history => new
                {
                    Id = history.Id,
                    StructureId = structure.Id,
                    ModifiedByUserId = history.ModifiedBy.Id,
                    Changes = history.Changes,
                    CreatedDate = history.CreatedDate
                });
                
                await connection
                    .ExecuteAsync(Queries.InsertHistory, historyParams, transaction);
                
                await connection.ExecuteAsync(OutboxQueries.Insert, new
                {
                    Id = Guid.NewGuid(),
                    Type = typeof(UpdatedStructure).AssemblyQualifiedName,
                    Data = JsonSerializer.Serialize(new UpdatedStructure()
                    {
                        Id = structure.Id,
                        Name = structure.Name,
                        ClientUnitId = structure.ClientUnit.Id,
                        Datum = structure.CoordinateSetting.Datum
                    }),
                }, transaction);
                
                await connection.ExecuteAsync(OutboxQueries.Insert, new
                {
                    Id = Guid.NewGuid(),
                    Type = typeof(NotificationCommand).AssemblyQualifiedName,
                    Data = JsonSerializer.Serialize(new NotificationCommand()
                    {
                        Id = structure.Id,
                        Theme = NotificationTheme.StructureUpdated,
                        CreatedById = structure.History.Any()
                            ? structure.History
                                .OrderByDescending(x => x.CreatedDate)
                                .FirstOrDefault()!.ModifiedBy.Id
                            : Guid.Empty,
                        CreatedDate = DateTime.UtcNow
                    })
                }, transaction);

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<Domain.Entities.Structure> GetPercolationMapData(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var lookup = new Dictionary<Guid, Domain.Entities.Structure>();

            await connection.QueryAsync(
                sql: Queries.GetPercolationMapData,
                new[]
                {
                    typeof(Domain.Entities.Structure),
                    typeof(CoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(MapConfiguration),
                    typeof(Orientation),
                    typeof(Dimension),
                    typeof(AspectStructure),
                    typeof(Domain.Entities.Aspect),
                    typeof(Domain.Entities.Area),
                    typeof(Domain.Entities.Section),
                    typeof(SectionCoordinates),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(MapLineSetting),
                    typeof(AbsoluteVariationColors),
                    typeof(Layer),
                    typeof(File)
                },
                (records) =>
                {
                    var structureDb = records[0] as Domain.Entities.Structure;
                    var coordinateSetting = records[1] as CoordinateSetting;
                    var decimalCoordinate = records[2] as DecimalGeodetic;
                    var utmCoordinate = records[3] as Utm;
                    var mapConfig = records[4] as MapConfiguration;
                    var seismicCoefficient = records[5] as Orientation;
                    var crestDimension = records[6] as Dimension;
                    var aspect = records[8] as Domain.Entities.Aspect;
                    var area = records[9] as Domain.Entities.Area;
                    var sectionCoordinates = records[11] as SectionCoordinates;
                    var upstreamDecimalGeodeticCoordinate = records[12] as DecimalGeodetic;
                    var upstreamUtmCoordinate = records[13] as Utm;
                    var upstreamCoordinateSetting = records[14] as SectionCoordinateSetting;
                    var downstreamDecimalGeodeticCoordinate = records[15] as DecimalGeodetic;
                    var downstreamUtmCoordinate = records[16] as Utm;
                    var downstreamCoordinateSetting = records[17] as SectionCoordinateSetting;
                    var midpointDecimalGeodeticCoordinate = records[18] as DecimalGeodetic;
                    var midpointUtmCoordinate = records[19] as Utm;
                    var midpointCoordinateSetting = records[20] as SectionCoordinateSetting;
                    var mapLineSetting = records[21] as MapLineSetting;


                    if (!lookup.TryGetValue(structureDb.Id, out var structure))
                    {
                        lookup.Add(structureDb.Id, structureDb);
                        structure = structureDb;
                    }

                    upstreamCoordinateSetting.CoordinateSystems = new()
                    {
                        DecimalGeodetic = upstreamDecimalGeodeticCoordinate,
                        Utm = upstreamUtmCoordinate
                    };

                    if (sectionCoordinates.Datum != Coordinate.Core.Enums.Datum.SIRGAS2000)
                    {
                        upstreamCoordinateSetting.CoordinateSystems.DecimalGeodetic = Helper.ToDatum(sectionCoordinates.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, upstreamDecimalGeodeticCoordinate);
                        upstreamCoordinateSetting.CoordinateSystems.Utm = Helper.ToDatum(sectionCoordinates.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, upstreamUtmCoordinate);
                    }

                    downstreamCoordinateSetting.CoordinateSystems = new()
                    {
                        DecimalGeodetic = downstreamDecimalGeodeticCoordinate,
                        Utm = downstreamUtmCoordinate
                    };

                    if (midpointCoordinateSetting != null)
                    {
                        midpointCoordinateSetting.CoordinateSystems = new()
                        {
                            DecimalGeodetic = midpointDecimalGeodeticCoordinate,
                            Utm = midpointUtmCoordinate
                        };
                    }

                    if (sectionCoordinates.Datum != Coordinate.Core.Enums.Datum.SIRGAS2000)
                    {
                        upstreamCoordinateSetting.CoordinateSystems.DecimalGeodetic = Helper.ToDatum(sectionCoordinates.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, upstreamDecimalGeodeticCoordinate);
                        upstreamCoordinateSetting.CoordinateSystems.Utm = Helper.ToDatum(sectionCoordinates.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, upstreamUtmCoordinate);

                        downstreamCoordinateSetting.CoordinateSystems.DecimalGeodetic = Helper.ToDatum(sectionCoordinates.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, downstreamDecimalGeodeticCoordinate);
                        downstreamCoordinateSetting.CoordinateSystems.Utm = Helper.ToDatum(sectionCoordinates.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, downstreamUtmCoordinate);

                        if (midpointCoordinateSetting != null)
                        {
                            midpointCoordinateSetting.CoordinateSystems.DecimalGeodetic = Helper.ToDatum(sectionCoordinates.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, midpointDecimalGeodeticCoordinate);
                            midpointCoordinateSetting.CoordinateSystems.Utm = Helper.ToDatum(sectionCoordinates.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, midpointUtmCoordinate);
                        }
                    }

                    sectionCoordinates.UpstreamCoordinateSetting = upstreamCoordinateSetting;
                    sectionCoordinates.DownstreamCoordinateSetting = downstreamCoordinateSetting;
                    sectionCoordinates.MidpointCoordinateSetting = midpointCoordinateSetting;

                    structure.CrestDimension = crestDimension;
                    structure.SeismicCoefficient = seismicCoefficient;
                    structure.MapConfiguration = mapConfig;

                    if (coordinateSetting.Datum != Coordinate.Core.Enums.Datum.SIRGAS2000)
                    {
                        decimalCoordinate = Helper.ToDatum(coordinateSetting.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, decimalCoordinate);
                        utmCoordinate = Helper.ToDatum(coordinateSetting.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, utmCoordinate);
                    }

                    coordinateSetting.Systems = new()
                    {
                        DecimalGeodetic = decimalCoordinate,
                        Utm = utmCoordinate
                    };

                    structure.CoordinateSetting = coordinateSetting;

                    if (records[7] is AspectStructure aspectStructure)
                    {
                        aspect.Area = area;
                        aspectStructure.Aspect = aspect;
                        structure.AddAspect(aspectStructure);
                    }

                    if (records[10] is Domain.Entities.Section section)
                    {
                        section.Coordinates = sectionCoordinates;
                        section.MapLineSetting = mapLineSetting;
                        structure.AddSection(section);
                    }

                    if (records[22] is AbsoluteVariationColors absoluteVariationColors)
                    {
                        structure.AbsoluteVariationColors = absoluteVariationColors;
                    }

                    if (records[23] is Layer layer)
                    {
                        var file = records[24] as File;
                        layer.File = file;
                        structure.AddLayer(layer);
                    }

                    return structure;
                },
                splitOn: "Datum,Latitude,Northing,GeneralZoom,Horizontal,Length,Id,Id,Id,Id,Datum,Latitude,Northing,Format,Latitude,Northing,Format,Latitude,Northing,Format,Color,Id,Id,Name",
                param: new { Id = id }
                );

            return lookup.Values.FirstOrDefault();
        }

        public async Task UpsertAbsoluteVariationColorsAsync(AbsoluteVariationColors absoluteVariationColors, Guid structureId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                await connection.ExecuteAsync(Queries.UpsertAbsoluteVariationColorConfiguration, new
                {
                    absoluteVariationColors.Id,
                    StructureId = structureId,
                    absoluteVariationColors.PositiveAbsoluteVariationColor,
                    absoluteVariationColors.NegativeAbsoluteVariationColor,
                    absoluteVariationColors.ConstantAbsoluteVariationColor
                }, transaction);

                await connection.ExecuteAsync(
                    Queries.DeleteAbsoluteVariationColorConfiguration,
                new
                {
                    absoluteVariationColors.Id,
                    StructureId = structureId
                }, transaction);

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }

        }

        public async Task<Domain.Entities.Structure> GetDisplacementMapData(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var lookup = new Dictionary<Guid, Domain.Entities.Structure>();

            await connection.QueryAsync(
                sql: Queries.GetDisplacementMapData,
                new[]
                {
                    typeof(Domain.Entities.Structure),
                    typeof(CoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(MapConfiguration),
                    typeof(DisplacementMapConfiguration)
                },
                (records) =>
                {
                    var structureDb = records[0] as Domain.Entities.Structure;
                    var coordinateSetting = records[1] as CoordinateSetting;
                    var decimalCoordinate = records[2] as DecimalGeodetic;
                    var utmCoordinate = records[3] as Utm;
                    var mapConfig = records[4] as MapConfiguration;
                    var displacementMapConfig = records[5] as DisplacementMapConfiguration;

                    if (!lookup.TryGetValue(structureDb.Id, out var structure))
                    {
                        lookup.Add(structureDb.Id, structureDb);
                        structure = structureDb;
                    }


                    if (coordinateSetting.Datum != Coordinate.Core.Enums.Datum.SIRGAS2000)
                    {
                        decimalCoordinate = Helper.ToDatum(coordinateSetting.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, decimalCoordinate);
                        utmCoordinate = Helper.ToDatum(coordinateSetting.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, utmCoordinate);
                    }

                    coordinateSetting.Systems = new()
                    {
                        DecimalGeodetic = decimalCoordinate,
                        Utm = utmCoordinate
                    };

                    structure.MapConfiguration = mapConfig;
                    structure.CoordinateSetting = coordinateSetting;
                    structure.DisplacementMapConfiguration = displacementMapConfig;

                    return structure;
                },
                splitOn: "Datum,Latitude,Northing,GeneralZoom,Id",
                param: new { Id = id }
                );

            return lookup.Values.FirstOrDefault();
        }

        public async Task UpsertDisplacementMapConfigurationAsync(DisplacementMapConfiguration displacementMapConfiguration, Guid structureId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                await connection.ExecuteAsync(Queries.UpsertDisplacementMapConfiguration, new
                {
                    displacementMapConfiguration.Id,
                    StructureId = structureId,
                    displacementMapConfiguration.PositiveSettlementColor,
                    displacementMapConfiguration.NegativeSettlementColor,
                    displacementMapConfiguration.SettlementColorOpacity,
                    displacementMapConfiguration.ArrowColor,
                    displacementMapConfiguration.ArrowWidth
                }, transaction);

                await connection.ExecuteAsync(
                    Queries.DeleteDisplacementMapConfiguration,
                new
                {
                    displacementMapConfiguration.Id,
                    StructureId = structureId
                }, transaction);

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<Domain.Entities.Structure> GetStructureStabilityMapData(Guid id)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);

            var lookup = new Dictionary<Guid, Domain.Entities.Structure>();

            await connection.QueryAsync(
                sql: Queries.GetStabilityMapData,
                new[]
                {
                    typeof(Domain.Entities.Structure),
                    typeof(CoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(MapConfiguration),
                    typeof(Domain.Entities.Section),
                    typeof(SectionCoordinates),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(DecimalGeodetic),
                    typeof(Utm),
                    typeof(SectionCoordinateSetting),
                    typeof(MapLineSetting),
                    typeof(StabilityMapConfiguration)
                },
                (records) =>
                {
                    var structureDb = records[0] as Domain.Entities.Structure;
                    var coordinateSetting = records[1] as CoordinateSetting;
                    var decimalCoordinate = records[2] as DecimalGeodetic;
                    var utmCoordinate = records[3] as Utm;
                    var mapConfig = records[4] as MapConfiguration;
                    var sectionCoordinates = records[6] as SectionCoordinates;
                    var upstreamDecimalGeodeticCoordinate = records[7] as DecimalGeodetic;
                    var upstreamUtmCoordinate = records[8] as Utm;
                    var upstreamCoordinateSetting = records[9] as SectionCoordinateSetting;
                    var downstreamDecimalGeodeticCoordinate = records[10] as DecimalGeodetic;
                    var downstreamUtmCoordinate = records[11] as Utm;
                    var downstreamCoordinateSetting = records[12] as SectionCoordinateSetting;
                    var midpointDecimalGeodeticCoordinate = records[13] as DecimalGeodetic;
                    var midpointUtmCoordinate = records[14] as Utm;
                    var midpointCoordinateSetting = records[15] as SectionCoordinateSetting;
                    var mapLineSetting = records[16] as MapLineSetting;
                    var stabilityMapConfig = records[17] as StabilityMapConfiguration;


                    if (!lookup.TryGetValue(structureDb.Id, out var structure))
                    {
                        lookup.Add(structureDb.Id, structureDb);
                        structure = structureDb;
                    }

                    upstreamCoordinateSetting.CoordinateSystems = new()
                    {
                        DecimalGeodetic = upstreamDecimalGeodeticCoordinate,
                        Utm = upstreamUtmCoordinate
                    };

                    downstreamCoordinateSetting.CoordinateSystems = new()
                    {
                        DecimalGeodetic = downstreamDecimalGeodeticCoordinate,
                        Utm = downstreamUtmCoordinate
                    };

                    if (midpointCoordinateSetting != null)
                    {
                        midpointCoordinateSetting.CoordinateSystems = new()
                        {
                            DecimalGeodetic = midpointDecimalGeodeticCoordinate,
                            Utm = midpointUtmCoordinate
                        };
                    }

                    if (sectionCoordinates.Datum != Coordinate.Core.Enums.Datum.SIRGAS2000)
                    {
                        upstreamCoordinateSetting.CoordinateSystems.DecimalGeodetic = Helper.ToDatum(sectionCoordinates.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, upstreamDecimalGeodeticCoordinate);
                        upstreamCoordinateSetting.CoordinateSystems.Utm = Helper.ToDatum(sectionCoordinates.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, upstreamUtmCoordinate);

                        downstreamCoordinateSetting.CoordinateSystems.DecimalGeodetic = Helper.ToDatum(sectionCoordinates.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, downstreamDecimalGeodeticCoordinate);
                        downstreamCoordinateSetting.CoordinateSystems.Utm = Helper.ToDatum(sectionCoordinates.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, downstreamUtmCoordinate);

                        if (midpointCoordinateSetting != null)
                        {
                            midpointCoordinateSetting.CoordinateSystems.DecimalGeodetic = Helper.ToDatum(sectionCoordinates.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, midpointDecimalGeodeticCoordinate);
                            midpointCoordinateSetting.CoordinateSystems.Utm = Helper.ToDatum(sectionCoordinates.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, midpointUtmCoordinate);
                        }
                    }

                    sectionCoordinates.UpstreamCoordinateSetting = upstreamCoordinateSetting;
                    sectionCoordinates.DownstreamCoordinateSetting = downstreamCoordinateSetting;
                    sectionCoordinates.MidpointCoordinateSetting = midpointCoordinateSetting;
                    structure.MapConfiguration = mapConfig;
                    structure.StabilityMapConfiguration = stabilityMapConfig;

                    if (coordinateSetting.Datum != Coordinate.Core.Enums.Datum.SIRGAS2000)
                    {
                        decimalCoordinate = Helper.ToDatum(coordinateSetting.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, decimalCoordinate);
                        utmCoordinate = Helper.ToDatum(coordinateSetting.Datum, Coordinate.Core.Enums.Datum.SIRGAS2000, utmCoordinate);
                    }

                    coordinateSetting.Systems = new()
                    {
                        DecimalGeodetic = decimalCoordinate,
                        Utm = utmCoordinate
                    };

                    structure.CoordinateSetting = coordinateSetting;

                    if (records[5] is Domain.Entities.Section section)
                    {
                        section.Coordinates = sectionCoordinates;
                        section.MapLineSetting = mapLineSetting;
                        structure.AddSection(section);
                    }

                    return structure;
                },
                splitOn: "Datum,Latitude,Northing,GeneralZoom,Id,Datum,Latitude,Northing,Format,Latitude,Northing,Format,Latitude,Northing,Format,Color,Id",
                param: new { Id = id }
                );

            return lookup.Values.FirstOrDefault();
        }

        public async Task UpsertStabilityMapConfigurationAsync(StabilityMapConfiguration stabilityMapConfiguration, Guid structureId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();
            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                await connection.ExecuteAsync(Queries.UpsertStabilityMapConfiguration, new
                {
                    stabilityMapConfiguration.Id,
                    StructureId = structureId,
                    stabilityMapConfiguration.PiezometerColor,
                    stabilityMapConfiguration.WaterLevelIndicatorColor
                }, transaction);

                await connection.ExecuteAsync(
                    Queries.DeleteStabilityMapConfiguration,
                new
                {
                    stabilityMapConfiguration.Id,
                    StructureId = structureId
                }, transaction);

                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<IEnumerable<CalculationMethod>> GetCalculationMethodsAsync(
            Guid structureId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();

            return await connection.QueryAsync<CalculationMethod>(
                sql: Queries.GetCalculationMethodsByStructureId,
                param: new { StructureId = structureId });
        }

        public async Task<GetStabilityAnalysisDateResponse> GetStabilityAnalysisDateAsync(GetStabilityAnalysisDateRequest request)
        {
            var param = new 
            {
                request.StructureId,
                request.CalculationMethod,
                SliFileType = request.SurfaceType == SurfaceType.Circular 
                ? new[] { SliFileType.CircularDrained, SliFileType.CircularUndrained, SliFileType.CircularPseudoStatic } 
                : new[] { SliFileType.NonCircularDrained, SliFileType.NonCircularUndrained, SliFileType.NonCircularPseudoStatic } 
            };

            var builder = new SqlBuilder();
            var template = builder.AddTemplate(Queries.GetStabilityAnalysisDate);

            builder.Where("[stability-analysis].[structure-id] = @StructureId");

            if (request.CalculationMethod.HasValue)
            {
                builder.Where("[safety-factors].[calculation-method] = @CalculationMethod");
            }

            if (request.SurfaceType.HasValue)
            {
                builder.Where("[safety-factors].[sli-file-type] IN @SliFileType");
            }

            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();

            return await connection.QueryFirstOrDefaultAsync<GetStabilityAnalysisDateResponse>(
                sql: template.RawSql,
                param: param);
        }

        public async Task<IEnumerable<SurfaceType>> GetSurfaceTypesAsync(
            Guid structureId)
        {
            await using var connection = ApplicationDatabase.GetConnection(_connectionString);
            await connection.OpenAsync();

            return await connection.QueryAsync<SurfaceType>(
                sql: Queries.GetSurfaceTypesByStructureId,
                param: new { StructureId = structureId });
        }
    }
}
