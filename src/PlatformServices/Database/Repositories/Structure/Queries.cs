namespace Database.Repositories.Structure
{
    public static class Queries
    {
        public const string Insert =
            @"
            INSERT INTO [structures] 
            (
                id, 
                name, 
                [structure-type-id], 
                protocol,
                status, 
                latitude, 
                longitude, 
                northing, 
                easting, 
                [zone-number],
                [zone-letter], 
                datum, 
                [coordinate-format], 
                [client-unit-id], 
                [should-evaluate-drained-condition], 
                [should-evaluate-undrained-condition], 
                [should-evaluate-pseudo-static-condition], 
                [map-general-zoom], 
                [map-instruments-zoom], 
                [has-auto-update], 
                [interval-fetch-data], 
                [interval-generate-package],
                [seismic-coefficient-horizontal], 
                [seismic-coefficient-vertical], 
                gravity, 
                [city-id], 
                purpose,
                [construction-stages], 
                [crest-length], 
                [crest-width], 
                [total-height], 
                [downstream-slope], 
                [upstream-slope], 
                classification, 
                [section-type], 
                [foundation-type], 
                [raising-method], 
                [expected-elevations], 
                [elevations-made], 
                [reservoir-design-volume], 
                [current-reservoir-volume], 
                [internal-drainage], 
                [superficial-drainage], 
                [basin-area-in-square-kilometers], 
                [project-precipitation],
                [full-of-project], 
                [maximum-influent-flow], 
                [project-flow], 
                [normal-maximum-water-level], 
                [maximum-water-level-maximorum], 
                [freeboard-normal-maximum-water-level], 
                [freeboard-maximum-water-level-maximorum], 
                spillway,
                [circular-search-method], 
                [circular-divisions-along-slope], 
                [circles-per-division],
                [circular-number-of-iterations],
                [circular-divisions-next-iteration],
                [radius-increment], 
                [circular-number-of-surfaces],
                [non-circular-search-method],
                [non-circular-divisions-along-slope],
                [surfaces-per-division],
                [non-circular-number-of-iterations],
                [non-circular-divisions-next-iteration],
                [number-of-vertices-along-surface], 
                [non-circular-number-of-surfaces], 
                [number-of-nests], 
                [maximum-iterations],
                [initial-number-of-surface-vertices], 
                [initial-number-of-iterations], 
                [maximum-number-of-steps],
                [number-of-factors-safety-compared], 
                [tolerance-for-stopping-criterion],
                [number-of-particles],
                [construction-year],
                [start-of-operation-date],
                [end-of-operation-date],
                [designing-company],
                [current-status],
                [crest-quota],
                [spillway-sill-quota],
                [intercepted-watercourse]
            )
            VALUES 
            (
                @Id,
                @Name,
                @StructureTypeId,
                @Protocol,
                @Status,
                @Latitude,
                @Longitude,
                @Northing,
                @Easting,
                @ZoneNumber,
                @ZoneLetter,
                @Datum,
                @CoordinateFormat,
                @ClientUnitId,
                @ShouldEvaluateDrainedCondition,
                @ShouldEvaluateUndrainedCondition,
                @ShouldEvaluatePseudoStaticCondition,
                @MapGeneralZoom,
                @MapInstrumentsZoom,
                @HasAutoUpdate,
                @IntervalFetchData,
                @IntervalGeneratePackage,
                @SeismicCoefficientHorizontal,
                @SeismicCoefficientVertical,
                @Gravity,
                @CityId,
                @Purpose,
                @ConstructionStages,
                @CrestLength,
                @CrestWidth,
                @TotalHeight,
                @DownstreamSlope,
                @UpstreamSlope,
                @Classification,
                @SectionType,
                @FoundationType,
                @RaisingMethod,
                @ExpectedElevations,
                @ElevationsMade,
                @ReservoirDesignVolume,
                @CurrentReservoirVolume,
                @InternalDrainage,
                @SuperficialDrainage,
                @BasinAreaInSquareKilometers,
                @ProjectPrecipitation,
                @FullOfProject,
                @MaximumInfluentFlow,
                @ProjectFlow,
                @NormalMaximumWaterLevel,
                @MaximumWaterLevelMaximorum,
                @FreeboardNormalMaximumWaterLevel,
                @FreeboardMaximumWaterLevelMaximorum,
                @Spillway,
                @CircularSearchMethod,
                @CircularDivisionsAlongSlope,
                @CirclesPerDivision,
                @CircularNumberOfIterations,
                @CircularDivisionsNextIteration,
                @RadiusIncrement,
                @CircularNumberOfSurfaces,
                @NonCircularSearchMethod,
                @NonCircularDivisionsAlongSlope,
                @SurfacesPerDivision,
                @NonCircularNumberOfIterations,
                @NonCircularDivisionsNextIteration,
                @NumberOfVerticesAlongSurface,
                @NonCircularNumberOfSurfaces,
                @NumberOfNests,
                @MaximumIterations,
                @InitialNumberOfSurfaceVertices,
                @InitialNumberOfIterations,
                @MaximumNumberOfSteps,
                @NumberOfFactorsSafetyCompared,
                @ToleranceForStoppingCriterion,
                @NumberOfParticles,
                @ConstructionYear,
                @StartOfOperationDate,
                @EndOfOperationDate,
                @DesigningCompany,
                @CurrentStatus,
                @CrestQuota,
                @SpillwaySillQuota,
                @InterceptedWatercourse
            )
            ";

        public const string GetDashboardMap =
            @"
                SELECT 
                [structures].[id] AS [StructureId],
                [structures].[datum] AS [Datum],
                [structures].[latitude] AS [Latitude],
                [structures].[longitude] AS [Longitude],
                [sections].[id] AS [Id], 
                [sections].[name] AS [Name], 
                [sections].[datum] AS [Datum], 
                [sections].[structure-id] AS [StructureId], 
                [upstream-latitude] AS [Latitude], 
                [upstream-longitude] AS [Longitude], 
                [downstream-latitude] AS [Latitude], 
                [downstream-longitude] AS [Longitude], 
                [midpoint-latitude] AS [Latitude],
                [midpoint-longitude] AS [Longitude],
                [line-color] AS [Color], 
                [line-type] AS [Type], 
                [line-width] AS [Width],
                [instruments].[id] AS [Id],
                [instruments].[identifier] AS [Identifier],
                [instruments].[type] AS [Type],
                [instruments].[online] AS [Online],
                [instruments].[datum] AS [Datum],
                [instruments].[latitude] AS [Latitude],
                [instruments].[longitude] AS [Longitude]
                FROM [structures]
                LEFT JOIN [sections] ON [structures].[id] = [sections].[structure-id]
                LEFT JOIN [instruments] ON [instruments].[structure-id] = [structures].[id] AND [instruments].[online] = 1
                WHERE [structures].[id] = @StructureId
            ";


        public const string GetWithReading =
            @"
            WITH ExtendedSections AS (
    SELECT
        [sections].[id] AS SectionId,
        [sections].[structure-id] AS StructureId,
        [sections].[name] AS SectionName,
        [sections].[minimum-drained-depth] AS MinimumDrainedDepth,
        [sections].[minimum-undrained-depth] AS MinimumUndrainedDepth,
        [sections].[minimum-pseudo-static-depth] AS MinimumPseudoStaticDepth,
        [sr].[id] AS LastReviewId,
        [sr].[drawing-unique-name] AS LastReviewDrawingUniqueName,
        ROW_NUMBER() OVER (PARTITION BY [sections].[id] ORDER BY [sr].[created-date] DESC) AS LastReviewRank,
        CASE
            WHEN [sr].[drawing-unique-name] IS NOT NULL AND [sr].[drawing-unique-name] IS NOT NULL THEN 1
            ELSE 0
            END AS LastReviewHasDxf
    FROM
        [sections]
            LEFT JOIN [section-reviews] AS sr ON [sr].[section-id] = [sections].[id]
)
   , FilteredSections AS (
    SELECT
        SectionId,
        StructureId,
        SectionName,
        MinimumDrainedDepth,
        MinimumUndrainedDepth,
        MinimumPseudoStaticDepth,
        LastReviewHasDxf
    FROM
        ExtendedSections
    WHERE
        LastReviewRank = 1
)
SELECT
    [structures].[id] AS Id,
    [structures].[name] AS Name,
    [structures].[circular-search-method] AS [CircularSearchMethod],
    [structures].[circular-divisions-along-slope] AS [DivisionsAlongSlope],
    [structures].[circles-per-division] AS [CirclesPerDivision],
    [structures].[circular-number-of-iterations] AS [NumberOfIterations],
    [structures].[circular-divisions-next-iteration] AS [DivisionsNextIteration],
    [structures].[radius-increment] AS [RadiusIncrement],
    [structures].[circular-number-of-surfaces] AS [NumberOfSurfaces],
    [structures].[non-circular-search-method] AS [NonCircularSearchMethod],
    [structures].[non-circular-divisions-along-slope] AS [DivisionsAlongSlope],
    [structures].[surfaces-per-division] AS [SurfacesPerDivision],
    [structures].[non-circular-number-of-iterations] AS [NumberOfIterations],
    [structures].[non-circular-divisions-next-iteration] AS [DivisionsNextIteration],
    [structures].[number-of-vertices-along-surface] AS [NumberOfVerticesAlongSurface],
    [structures].[non-circular-number-of-surfaces] AS [NumberOfSurfaces],
    [structures].[number-of-nests] AS [NumberOfNests],
    [structures].[maximum-iterations] AS [MaximumIterations],
    [structures].[initial-number-of-surface-vertices] AS [InitialNumberOfSurfaceVertices],
    [structures].[initial-number-of-iterations] AS [InitialNumberOfIterations],
    [structures].[maximum-number-of-steps] AS [MaximumNumberOfSteps],
    [structures].[number-of-factors-safety-compared] AS [NumberOfFactorsSafetyComparedBeforeStopping],
    [structures].[tolerance-for-stopping-criterion] AS [ToleranceForStoppingCriterion],
    [structures].[number-of-particles] AS [NumberOfParticles],
    [circular-calculation-methods].[calculation-method] AS [CalculationMethods],
    [non-circular-calculation-methods].[calculation-method] AS [CalculationMethods],
    [FilteredSections].SectionId AS Id,
    [FilteredSections].SectionName AS Name,
    [FilteredSections].StructureId,
    [FilteredSections].MinimumDrainedDepth,
    [FilteredSections].MinimumUndrainedDepth,
    [FilteredSections].MinimumPseudoStaticDepth,
    [FilteredSections].LastReviewHasDxf,
    [LatestBeachLength].[length] AS Length,
    [instruments].[id] AS Id,
    [instruments].[identifier] AS Identifier,
    [instruments].[type] AS Type,
    [instruments].[online] AS Online,
    [instruments].[base-quota] AS BaseQuota,
    [instruments].[top-quota] AS TopQuota,
    [instruments].[linimetric-ruler-position] AS LinimetricRulerPosition,
    [measurements].[id] AS Id,
    [measurements].[identifier] AS Identifier,
    [measurements].[quota] AS Quota,
    [LatestReading].[id] AS Id,
    [LatestReading].[date] AS Date,
    [LatestReading].[quota] AS Quota,
    [LatestReading].[dry] AS Dry,
    [section-reviews].[id] AS Id,
    [section-reviews].[start-date] AS StartDate,
    [section-reviews].[dxf-has-waterline] AS DxfHasWaterline,
    [section-reviews].[index] AS [Index],
    [section-reviews].[is-under-construction] AS IsUnderConstruction,
    [section-reviews].[drawing-name] AS Name,
    [section-reviews].[drawing-unique-name] AS UniqueName,
    [construction-stages].[id] AS Id,
    [construction-stages].[stage] AS Stage,
    [construction-stages].[dxf-has-waterline] AS DxfHasWaterline,
    [construction-stages].[is-current-stage] AS IsCurrentStage,
    [construction-stages].[section-review-id] AS SectionReviewId,
    [construction-stages].[drawing-name] AS Name,
    [construction-stages].[drawing-unique-name] AS UniqueName
FROM
    [structures]
        LEFT JOIN
    [circular-calculation-methods] ON [circular-calculation-methods].[structure-id] = [structures].[id]
        LEFT JOIN
    [non-circular-calculation-methods] ON [non-circular-calculation-methods].[structure-id] = [structures].[id]
        LEFT JOIN
    FilteredSections ON FilteredSections.StructureId = [structures].[id]
        LEFT JOIN
    [section-instruments] ON [section-instruments].[section-id] = FilteredSections.SectionId
        LEFT JOIN
    [section-reviews] ON [section-reviews].[section-id] = FilteredSections.SectionId
        LEFT JOIN
    [construction-stages] ON [construction-stages].[section-review-id] = [section-reviews].[id]
        LEFT JOIN
    [instruments] ON
        (
            [instruments].[id] = [section-instruments].[instrument-id]  AND [instruments].[type] IN @InstrumentTypes
            )
            OR
        (
            [instruments].[structure-id] = FilteredSections.StructureId AND [instruments].[type] = @LinimetricRulerType 
            )
        LEFT JOIN
    [measurements] ON [measurements].[instrument-id] = [instruments].[id] 
        LEFT JOIN
    (
        SELECT TOP(1) WITH TIES
            [reading-values].[id],
            [reading-values].[date],
            [reading-values].[quota],
            [reading-values].[dry],
            [reading-values].[measurement-id],
            [readings].[instrument-id],
            ROW_NUMBER() OVER(PARTITION BY [readings].[instrument-id] ORDER BY [reading-values].[date] DESC) AS RowNumber
        FROM [reading-values]
                 JOIN readings ON readings.id = [reading-values].[reading-id]
        ORDER BY RowNumber
    ) AS LatestReading ON (
        ([LatestReading].[measurement-id] IS NULL OR [LatestReading].[measurement-id] = [measurements].[id])
            AND ([LatestReading].[instrument-id] = [instruments].[id])
        )
        LEFT JOIN
    (
        SELECT TOP(1) WITH TIES
            [beach-lengths].[id],
            [beach-lengths].[length],
            [beach-lengths].[section-id],
            ROW_NUMBER() OVER(PARTITION BY [beach-lengths].[section-id] ORDER BY [beach-lengths].[date] DESC) AS RowNumber
        FROM [beach-lengths]
        ORDER BY RowNumber
    ) AS LatestBeachLength ON [LatestBeachLength].[section-id] = FilteredSections.SectionId
WHERE
    [structures].[id] = @StructureId
ORDER BY
    [instruments].[id], [section-reviews].[start-date]
            ";

        public const string List =
            @"SELECT
            [structures].[id] AS [Id],
            [structures].[name] AS [Name],
            [structures].[active] AS [Active],
            [datum] AS [Datum], 
            [coordinate-format] AS [Format], 
            [latitude] AS [Latitude], 
            [longitude] AS [Longitude], 
            [northing] AS [Northing], 
            [easting] AS [Easting], 
            [zone-number] AS [ZoneNumber], 
            [zone-letter] AS [ZoneLetter]
            FROM [structures]
            /**where**/
            ORDER BY [name] ASC";

        public const string GetByFiltersMaps =
            @"
            SELECT 
            [structures].id AS [Id], 
            [structures].name AS [Name], 
            [structures].datum AS [Datum], 
            [structures].[coordinate-format] AS [Format], 
            [structures].latitude AS [Latitude], 
            [structures].longitude AS [Longitude], 
            [structures].northing AS [Northing], 
            [structures].easting AS [Easting], 
            [structures].[zone-number] AS [ZoneNumber], 
            [structures].[zone-letter] AS [ZoneLetter]
            FROM [structures]
            INNER JOIN [client-units]
                ON [client-units].[id] = [structures].[client-unit-id]
            WHERE (@ClientId IS NULL OR [client-units].[client-id] = @ClientId)
            AND (@RequestedBySuperSupport = 1 OR [structures].[id] IN @RequestedUserStructures)
            AND (@HasStructureIds = 0 OR [structures].[id] IN @StructureIds)";

        public const string Search =
            @"SELECT
            [structures].[Id] AS [Id],
            [structures].[Name] AS [Name],
            [structures].[search-identifier] AS [SearchIdentifier],
            [structures].[status] AS [Status],
            [structures].[active] AS [Active],
            [client-units].[id] AS [Id],
            [client-units].[name] AS [Name],
            [client-units].[client-id] AS [ClientId],
            [client-units].[active] AS [Active]
            FROM [structures]
            INNER JOIN [client-units]
                ON [client-units].[id] = [structures].[client-unit-id]
            WHERE (@Status IS NULL OR [structures].[status] = @Status)
            AND (@Active IS NULL OR [structures].[active] = @Active)
            AND (@ClientId IS NULL OR [client-units].[client-id] = @ClientId)
            AND (@Name IS NULL OR [structures].[name] LIKE CONCAT('%', @Name, '%'))
            AND (@SearchIdentifier IS NULL OR [structures].[search-identifier] = @SearchIdentifier)
            AND (@Id IS NULL OR [structures].[id] = @Id)
            AND (@ClientUnitId IS NULL OR [structures].[client-unit-id] = @ClientUnitId)
            AND (@RequestedBySuperSupport = 1 OR [structures].[id] IN @StructureIds)
            ORDER BY [structures].[search-identifier] DESC
            OFFSET @Skip ROWS
            FETCH NEXT @PageSize ROWS ONLY
            ";

        public const string Count =
            @"SELECT COUNT(*)
            FROM [structures]
            INNER JOIN [client-units]
                ON [client-units].[id] = [structures].[client-unit-id]
            WHERE (@Status IS NULL OR [structures].[status] = @Status)
            AND (@Active IS NULL OR [structures].[active] = @Active)
            AND (@Id IS NULL OR [structures].[id] = @Id)
            AND (@ClientId IS NULL OR [client-units].[client-id] = @ClientId)
            AND (@Name IS NULL OR [structures].[name] LIKE CONCAT('%', @Name, '%'))
            AND (@SearchIdentifier IS NULL OR [structures].[search-identifier] = @SearchIdentifier)
            AND (@ClientUnitId IS NULL OR [structures].[client-unit-id] = @ClientUnitId)
            AND (@RequestedBySuperSupport = 1 OR [structures].[id] IN @StructureIds)
            ";

        public const string GetBySearchIdentifier =
            @"
                SELECT 
                    [structures].[id] AS [Id], 
                    [structures].[search-identifier] AS [SearchIdentifier],
                    [structures].[name] AS [Name], 
                    [structures].[client-unit-id] AS [ClientUnitId]
                FROM [structures]
                /**where**/
            ";

        public const string GetMapConfiguration =
            @"
                SELECT [map-general-zoom] AS [GeneralZoom], 
                [map-instruments-zoom] AS [InstrumentsZoom]
                FROM [structures]
                WHERE [structures].[id] = @Id
            ";

        public const string GetBasic =
            @"
            SELECT 
                [structures].[id] AS [Id], 
                [structures].[name] AS [Name], 
                [structures].[status] AS [Status],
                [should-evaluate-drained-condition] AS [ShouldEvaluateDrainedCondition], 
                [should-evaluate-undrained-condition] AS [ShouldEvaluateUndrainedCondition], 
                [should-evaluate-pseudo-static-condition] AS [ShouldEvaluatePseudoStaticCondition],
                [structures].datum AS [Datum], 
                [structures].[coordinate-format] AS [Format], 
                [structures].latitude AS [Latitude], 
                [structures].longitude AS [Longitude], 
                [structures].northing AS [Northing], 
                [structures].easting AS [Easting], 
                [structures].[zone-number] AS [ZoneNumber], 
                [structures].[zone-letter] AS [ZoneLetter],
                [client-units].[id] AS [Id],
                [client-units].[name] AS [Name],
                [clients].[id] AS [Id],
                [clients].[name] AS [Name],
                [structure-types].[Id] AS [Id],
                [structure-types].[Name] AS [Name],
                [structure-type-activities].[Id] AS [Id],
                [structure-type-activities].[Activity] AS [Activity],
                [structure-type-activities].[Index] AS [Index]
            FROM [structures]
                INNER JOIN [client-units]
                    ON [client-units].[id] = [structures].[client-unit-id]
                INNER JOIN [clients]
                    ON [clients].[id] = [client-units].[client-id]
                INNER JOIN [structure-types]
                    ON [structure-types].[id] = [structures].[structure-type-id] 
                INNER JOIN [structure-type-activities]
                    ON [structure-type-activities].[structure-type-id] = [structure-types].[id]
            WHERE [structures].[Id] = @Id
            ";

        public const string GetStabilityAnalysisData =
            @"
            SELECT 
            [structures].id AS [Id], 
            [structures].name AS [Name], 
            [should-evaluate-drained-condition] AS [ShouldEvaluateDrainedCondition], 
            [should-evaluate-undrained-condition] AS [ShouldEvaluateUndrainedCondition], 
            [should-evaluate-pseudo-static-condition] AS [ShouldEvaluatePseudoStaticCondition],
            [seismic-coefficient-horizontal] AS [Horizontal], 
            [seismic-coefficient-vertical] AS [Vertical], 
            [circular-search-method] AS [CircularSearchMethod],
            [circular-divisions-along-slope] AS [DivisionsAlongSlope],
            [circles-per-division] AS [CirclesPerDivision],
            [circular-number-of-iterations] AS [NumberOfIterations],
            [circular-divisions-next-iteration] AS [DivisionsNextIteration],
            [radius-increment] AS [RadiusIncrement],
            [circular-number-of-surfaces] AS [NumberOfSurfaces],
            [non-circular-search-method] AS [NonCircularSearchMethod],
            [non-circular-divisions-along-slope] AS [DivisionsAlongSlope],
            [surfaces-per-division] AS [SurfacesPerDivision],
            [non-circular-number-of-iterations] AS [NumberOfIterations],
            [non-circular-divisions-next-iteration] AS [DivisionsNextIteration],
            [number-of-vertices-along-surface] AS [NumberOfVerticesAlongSurface],
            [non-circular-number-of-surfaces] AS [NumberOfSurfaces],
            [number-of-nests] AS [NumberOfNests],
            [maximum-iterations] AS [MaximumIterations],
            [initial-number-of-surface-vertices] AS [InitialNumberOfSurfaceVertices],
            [initial-number-of-iterations] AS [InitialNumberOfIterations],
            [maximum-number-of-steps] AS [MaximumNumberOfSteps],
            [number-of-factors-safety-compared] AS [NumberOfFactorsSafetyComparedBeforeStopping],
            [tolerance-for-stopping-criterion] AS [ToleranceForStoppingCriterion],
            [number-of-particles] AS [NumberOfParticles],
            [circular-calculation-methods].[calculation-method] AS [CalculationMethods],
            [non-circular-calculation-methods].[calculation-method] AS [CalculationMethods]
            FROM [structures]
            LEFT JOIN [circular-calculation-methods]
                ON [circular-calculation-methods].[structure-id] = [structures].[id]
            LEFT JOIN [non-circular-calculation-methods]
                ON [non-circular-calculation-methods].[structure-id] = [structures].[id]
            WHERE [structures].[Id] = @Id
            ";

        public const string GetById =
            @"SELECT 
            [structures].id AS [Id], 
            [structures].active AS [Active], 
            [structures].name AS [Name], 
            protocol AS [Protocol],
            status AS [Status], 
            [structures].[created-date] AS [CreatedDate], 
            [should-evaluate-drained-condition] AS [ShouldEvaluateDrainedCondition], 
            [should-evaluate-undrained-condition] AS [ShouldEvaluateUndrainedCondition], 
            [should-evaluate-pseudo-static-condition] AS [ShouldEvaluatePseudoStaticCondition], 
            [has-auto-update] AS [HasAutoUpdate], 
            gravity AS [Gravity], 
            purpose AS [Purpose],
            [construction-stages] AS [ConstructionStages], 
            [total-height] AS [TotalHeight], 
            [downstream-slope] AS [DownstreamSlope], 
            [upstream-slope] AS [UpstreamSlope], 
            classification AS [Classification], 
            [section-type] AS [SectionType], 
            [foundation-type] AS [FoundationType], 
            [raising-method] AS [RaisingMethod], 
            [expected-elevations] AS [ExpectedElevations], 
            [elevations-made] AS [ElevationsMade], 
            [reservoir-design-volume] AS [ReservoirDesignVolume], 
            [current-reservoir-volume] AS [CurrentReservoirVolume], 
            [internal-drainage] AS [InternalDrainage], 
            [superficial-drainage] AS [SuperficialDrainage], 
            [basin-area-in-square-kilometers] AS [BasinAreaInSquareKilometers], 
            [project-precipitation] AS [ProjectPrecipitation], 
            [full-of-project] AS [FullOfProject], 
            [maximum-influent-flow] AS [MaximumInfluentFlow], 
            [project-flow] AS [ProjectFlow], 
            [normal-maximum-water-level] AS [NormalMaximumWaterLevel], 
            [maximum-water-level-maximorum] AS [MaximumWaterLevelMaximorum], 
            [freeboard-normal-maximum-water-level] AS [FreeboardNormalMaximumWaterLevel], 
            [freeboard-maximum-water-level-maximorum] AS [FreeboardMaximumWaterLevelMaximorum], 
            spillway AS [Spillway],
            [structures].[search-identifier] AS [SearchIdentifier],
            [construction-year] AS [ConstructionYear],
            [start-of-operation-date] AS [StartOfOperationDate],
            [end-of-operation-date] AS [EndOfOperationDate],
            [designing-company] AS [DesigningCompany],
            [current-status] AS [CurrentStatus],
            [crest-quota] AS [CrestQuota],
            [spillway-sill-quota] AS [SpillwaySillQuota],
            [intercepted-watercourse] AS [InterceptedWatercourse],
            [structures].datum AS [Datum], 
            [structures].[coordinate-format] AS [Format], 
            [structures].latitude AS [Latitude], 
            [structures].longitude AS [Longitude], 
            [structures].northing AS [Northing], 
            [structures].easting AS [Easting], 
            [structures].[zone-number] AS [ZoneNumber], 
            [structures].[zone-letter] AS [ZoneLetter],
            [structure-types].[Id] AS [Id],
            [structure-types].[Name] AS [Name],
            [structure-type-activities].[Id] AS [Id],
            [structure-type-activities].[Activity] AS [Activity],
            [structure-type-activities].[Index] AS [Index],
            [client-units].[id] AS [Id],
            [client-units].[name] AS [Name],
            [client-units].[active] AS [Active],
            [client-units].[client-id] AS [ClientId],
            [map-general-zoom] AS [GeneralZoom], 
            [map-instruments-zoom] AS [InstrumentsZoom], 
            [interval-fetch-data] AS [Interval], 
            [last-data-fetch] AS [LastUpdate], 
            [interval-generate-package] AS [Interval], 
            [last-generated-package] AS [LastUpdate], 
            [seismic-coefficient-horizontal] AS [Horizontal], 
            [seismic-coefficient-vertical] AS [Vertical], 
            [cities].[Id] AS [Id],
            [cities].[Name] AS [Name],
            [states].[Id] AS [Id],
            [states].[Name] AS [Name],
            [countries].[Id] AS [Id],
            [countries].[Name] AS [Name],
            [crest-length] AS [Length], 
            [crest-width] AS [Width],
            [responsibles].id AS [Id], 
            [responsibles].[cpf] AS [Cpf],
            [responsibles].[name] AS [Name],
            [responsibles].[professional-record] AS [ProfessionalRecord],
            [responsibles].[email-address] AS [Value],
            [positions].[id] AS [Id],
            [positions].[name] AS [Name],
            [roles].[id] AS [Id],
            [roles].[name] AS [Name],
            [circular-search-method] AS [CircularSearchMethod],
            [circular-divisions-along-slope] AS [DivisionsAlongSlope],
            [circles-per-division] AS [CirclesPerDivision],
            [circular-number-of-iterations] AS [NumberOfIterations],
            [circular-divisions-next-iteration] AS [DivisionsNextIteration],
            [radius-increment] AS [RadiusIncrement],
            [circular-number-of-surfaces] AS [NumberOfSurfaces],
            [non-circular-search-method] AS [NonCircularSearchMethod],
            [non-circular-divisions-along-slope] AS [DivisionsAlongSlope],
            [surfaces-per-division] AS [SurfacesPerDivision],
            [non-circular-number-of-iterations] AS [NumberOfIterations],
            [non-circular-divisions-next-iteration] AS [DivisionsNextIteration],
            [number-of-vertices-along-surface] AS [NumberOfVerticesAlongSurface],
            [non-circular-number-of-surfaces] AS [NumberOfSurfaces],
            [number-of-nests] AS [NumberOfNests],
            [maximum-iterations] AS [MaximumIterations],
            [initial-number-of-surface-vertices] AS [InitialNumberOfSurfaceVertices],
            [initial-number-of-iterations] AS [InitialNumberOfIterations],
            [maximum-number-of-steps] AS [MaximumNumberOfSteps],
            [number-of-factors-safety-compared] AS [NumberOfFactorsSafetyComparedBeforeStopping],
            [tolerance-for-stopping-criterion] AS [ToleranceForStoppingCriterion],
            [number-of-particles] AS [NumberOfParticles],
            [circular-calculation-methods].[calculation-method] AS [CalculationMethods],
            [non-circular-calculation-methods].[calculation-method] AS [CalculationMethods],
            [aspects-structures].[id] AS [Id],
            [aspects-structures].[index] AS [Index],
            [aspects].[id] AS [Id],
            [aspects].[description] AS [Description],
            [aspects].[allow-option-not-applicable] AS [AllowOptionNotApplicable],
            [aspects].[response-for-occurrence] AS [ResponseForOccurrence],
            [areas].[id] AS [Id],
            [areas].[name] AS [Name],
            [sections].[id] AS [Id],
            [sections].[name] AS [Name],
            [section-reviews].[id] AS [Id],
            [section-reviews].[index] AS [Index],
            [section-reviews].[start-date] AS [StartDate],
            [clients].[id] AS [Id],
            [clients].[name] AS [Name],
            [clients].[active] AS [Active],
            [environmental-damage-potentials].[id] AS [Id],
            [environmental-damage-potentials].[total-reservoir-volume] AS [TotalReservoirVolume],
            [environmental-damage-potentials].[population-downstream] AS [PopulationDownstream],
            [environmental-damage-potentials].[environmental-impact] AS [EnvironmentalImpact],
            [environmental-damage-potentials].[socioeconomic-impact] AS [SocioeconomicImpact],
            [environmental-damage-potentials].[environmental-damage-potential-total] AS [EnvironmentalDamagePotentialTotal],
            [layers].[id] AS [Id],
            [layers].[type] AS [Type],
            [layers].[file-name] AS [Name],
            [layers].[file-unique-name] AS [UniqueName]
            FROM [structures]
            JOIN [client-units]
                ON [client-units].[id] = [structures].[client-unit-id] 
            JOIN [clients]
                ON [clients].[id] = [client-units].[client-id]
            JOIN [structure-types]
                ON [structure-types].[id] = [structures].[structure-type-id] 
            JOIN [structure-type-activities]
                ON [structure-type-activities].[structure-type-id] = [structure-types].[id]
            LEFT JOIN [cities]
                ON [cities].[id] = [structures].[city-id]
            LEFT JOIN [states]
                ON [states].[id] = [cities].[state-id] 
            LEFT JOIN [countries]
                ON [countries].[id] = [states].[country-id]
            LEFT JOIN [responsible-structures]
                ON [responsible-structures].[structure-id] = [structures].[id]
            LEFT JOIN [responsibles]
                ON [responsibles].[id] = [responsible-structures].[responsible-id]
            LEFT JOIN [roles]
                ON [roles].[id] = [responsibles].[role-id]
            LEFT JOIN [positions]
                ON [positions].[id] = [responsibles].[position-id]
            LEFT JOIN [circular-calculation-methods]
                ON [circular-calculation-methods].[structure-id] = [structures].[id]
            LEFT JOIN [non-circular-calculation-methods]
                ON [non-circular-calculation-methods].[structure-id] = [structures].[id]
            LEFT JOIN [aspects-structures] 
                ON [aspects-structures].[structure-id] = [structures].[id]
            LEFT JOIN [aspects]
                ON [aspects].[id] = [aspects-structures].[aspect-id]
            LEFT JOIN [areas]
                ON [areas].[id] = [aspects].[area-id]
            LEFT JOIN [sections]
                ON [sections].[structure-id] = [structures].[id]
            LEFT JOIN [section-reviews]
                ON [section-reviews].[section-id] = [sections].[id]
            LEFT JOIN [environmental-damage-potentials]  
                ON [environmental-damage-potentials].[structure-id] = [structures].[id]
            LEFT JOIN [layers]
                ON [structures].[id] = [layers].[structure-id]
            WHERE [structures].[Id] = @Id";

        public const string Update =
            @"
            UPDATE [structures]
            SET name = @Name,
	            [structure-type-id] = @StructureTypeId,
                [protocol] = @Protocol,
	            status = @Status,
	            latitude = @Latitude,
	            longitude = @Longitude,
	            northing = @Northing,
	            easting = @Easting,
	            [zone-number] = @ZoneNumber,
	            [zone-letter] = @ZoneLetter,
	            datum = @Datum,
	            [coordinate-format] = @CoordinateFormat,
	            [client-unit-id] = @ClientUnitId,
	            [should-evaluate-drained-condition] = @ShouldEvaluateDrainedCondition,
	            [should-evaluate-undrained-condition] = @ShouldEvaluateUndrainedCondition,
	            [should-evaluate-pseudo-static-condition] = @ShouldEvaluatePseudoStaticCondition,
	            [map-general-zoom] = @MapGeneralZoom,
	            [map-instruments-zoom] = @MapInstrumentsZoom,
	            [has-auto-update] = @HasAutoUpdate,
	            [interval-fetch-data] = @IntervalFetchData,
	            [interval-generate-package] = @IntervalGeneratePackage,
	            [seismic-coefficient-horizontal] = @SeismicCoefficientHorizontal,
	            [seismic-coefficient-vertical] = @SeismicCoefficientVertical,
	            gravity = @Gravity,
	            [city-id] = @CityId,
	            purpose = @Purpose,
	            [construction-stages] = @ConstructionStages,
	            [crest-length] = @CrestLength,
	            [crest-width] = @CrestWidth,
	            [total-height] = @TotalHeight,
	            [downstream-slope] = @DownstreamSlope,
	            [upstream-slope] = @UpstreamSlope,
	            classification = @Classification,
	            [section-type] = @SectionType,
	            [foundation-type] = @FoundationType,
	            [raising-method] = @RaisingMethod,
	            [expected-elevations] = @ExpectedElevations,
	            [elevations-made] = @ElevationsMade,
	            [reservoir-design-volume] = @ReservoirDesignVolume,
	            [current-reservoir-volume] = @CurrentReservoirVolume,
	            [internal-drainage] = @InternalDrainage,
	            [superficial-drainage] = @SuperficialDrainage,
	            [basin-area-in-square-kilometers] = @BasinAreaInSquareKilometers,
	            [project-precipitation] = @ProjectPrecipitation,
	            [full-of-project] = @FullOfProject,
	            [maximum-influent-flow] = @MaximumInfluentFlow,
	            [project-flow] = @ProjectFlow,
	            [normal-maximum-water-level] = @NormalMaximumWaterLevel,
	            [maximum-water-level-maximorum] = @MaximumWaterLevelMaximorum,
	            [freeboard-normal-maximum-water-level] = @FreeboardNormalMaximumWaterLevel,
	            [freeboard-maximum-water-level-maximorum] = @FreeboardMaximumWaterLevelMaximorum,
	            spillway = @Spillway,
                [circular-search-method] = @CircularSearchMethod,
	            [circular-divisions-along-slope] = @CircularDivisionsAlongSlope,
	            [circles-per-division] = @CirclesPerDivision,
	            [circular-number-of-iterations] = @CircularNumberOfIterations,
	            [circular-divisions-next-iteration] = @CircularDivisionsNextIteration,
	            [radius-increment] = @RadiusIncrement,
	            [circular-number-of-surfaces] = @CircularNumberOfSurfaces,
	            [non-circular-search-method] = @NonCircularSearchMethod,
	            [non-circular-divisions-along-slope] = @NonCircularDivisionsAlongSlope,
	            [surfaces-per-division] = @SurfacesPerDivision,
	            [non-circular-number-of-iterations] = @NonCircularNumberOfIterations,
	            [non-circular-divisions-next-iteration] = @NonCircularDivisionsNextIteration,
	            [number-of-vertices-along-surface] = @NumberOfVerticesAlongSurface,
	            [non-circular-number-of-surfaces] = @NonCircularNumberOfSurfaces,
	            [number-of-nests] = @NumberOfNests,
	            [maximum-iterations] = @MaximumIterations,
	            [initial-number-of-surface-vertices] = @InitialNumberOfSurfaceVertices,
	            [initial-number-of-iterations] = @InitialNumberOfIterations,
	            [maximum-number-of-steps] = @MaximumNumberOfSteps,
	            [number-of-factors-safety-compared] = @NumberOfFactorsSafetyCompared,
	            [tolerance-for-stopping-criterion] = @ToleranceForStoppingCriterion,
	            [number-of-particles] = @NumberOfParticles,
                [construction-year] = @ConstructionYear,
                [start-of-operation-date] = @StartOfOperationDate,
                [end-of-operation-date] = @EndOfOperationDate,
                [designing-company] = @DesigningCompany,
                [current-status] = @CurrentStatus,
                [crest-quota] = @CrestQuota,
                [spillway-sill-quota] = @SpillwaySillQuota,
                [intercepted-watercourse] = @InterceptedWatercourse
                WHERE [id] = @Id
            ";

        public const string InsertCircularCalculationMethod =
            @"
            INSERT INTO [circular-calculation-methods] 
            (
                [structure-id], 
                [calculation-method]
            )
            VALUES 
            (
                @StructureId,
                @CalculationMethod
            )
            ";

        public const string InsertNonCircularCalculationMethod =
            @"
                INSERT INTO [non-circular-calculation-methods] 
                (
                    [structure-id], 
                    [calculation-method]
                )
                VALUES 
                (
                    @StructureId,
                    @CalculationMethod
                )
            ";

        public const string CheckIfTheCircularMethodExists =
            @"SELECT COUNT(*)
                  FROM [circular-calculation-methods]
                  WHERE [calculation-method] = @CalculationMethod
                  AND [structure-id] = @StructureId";

        public const string CheckIfTheNonCircularMethodExists =
            @"SELECT COUNT(*)
                  FROM [non-circular-calculation-methods]
                  WHERE [calculation-method] = @CalculationMethod
                  AND [structure-id] = @StructureId";

        public const string DeleteCircularCalculationMethod =
            @"
                DELETE [circular-calculation-methods]   
                WHERE [calculation-method] NOT IN @CalculationMethods
                AND [structure-id] = @StructureId
            ";

        public const string DeleteNonCircularCalculationMethod =
            @"
                DELETE [non-circular-calculation-methods]       
                WHERE [calculation-method] NOT IN @CalculationMethods
                AND [structure-id] = @StructureId
            ";

        public const string InsertAspectStructure =
            @"
                INSERT INTO [aspects-structures] 
                (
                    [id], 
                    [structure-id], 
                    [aspect-id], 
                    [index]
                )
                VALUES 
                (
                    @Id,
                    @StructureId, 
                    @AspectId, 
                    @Index
                )
            ";

        public const string UpsertAspectStructure =
            @"
            UPDATE [aspects-structures] 
            SET
            [aspect-id] = @AspectId,
            [index] = @Index
            WHERE [id] = @Id
            IF @@ROWCOUNT = 0
                INSERT INTO [aspects-structures] 
                (
                    [id], 
                    [structure-id], 
                    [aspect-id], 
                    [index]
                )
                VALUES 
                (
                    @Id,
                    @StructureId, 
                    @AspectId, 
                    @Index
                )
            ";

        public const string DeleteAspectStructure =
            @"
                DELETE [aspects-structures]       
                WHERE [id] NOT IN @Ids
                AND [structure-id] = @StructureId
            ";

        public const string CheckIfTheResponsibleExists =
            @"SELECT COUNT(*)
                  FROM [responsible-structures]
                  WHERE [structure-id] = @StructureId
                  AND [responsible-id] = @ResponsibleId
            ";

        public const string DeleteResponsibleStructure =
            @"
                DELETE [responsible-structures] 
                WHERE [responsible-id] NOT IN  @Responsibles
                AND [structure-id] = @StructureId
            ";

        public const string InsertResponsibleStructures =
            @"
                INSERT INTO [responsible-structures] 
                (
                    [structure-id],
                    [responsible-id]
                )
                VALUES
                (
                    @StructureId,
                    @ResponsibleId
                )
            ";

        public const string UpdateOnlyActive =
            @"UPDATE [structures]
             SET [active] = @Active
             WHERE [id] = @Id";

        public const string InsertNature =
            @"
                INSERT INTO [natures]
                    (
                        [id],
                        [description],
                        [structure-id]
                    )
                    VALUES
                    (
                        @Id,
                        @Description,
                        @StructureId
                    )
            ";

        public const string InsertLayersStructure =
            @"
                INSERT INTO [layers] 
                (
                    [id], 
                    [structure-id], 
                    [file-name],
                    [file-unique-name],
                    [type]
                )
                VALUES 
                (
                    @Id,
                    @StructureId, 
                    @FileName, 
                    @FileUniqueName,
                    @LayerType
                )
            ";

        public const string UpsertLayersStructure =
            @"
                UPDATE [layers]
                SET               
                    [file-name] = @FileName,
                    [file-unique-name] = @FileUniqueName,
                    [type] = @LayerType
                WHERE [id] = @Id
                IF @@ROWCOUNT = 0
                     INSERT INTO [layers] 
                (
                    [id], 
                    [structure-id], 
                    [file-name],
                    [file-unique-name],
                    [type]
                )
                VALUES 
                (
                    @Id,
                    @StructureId, 
                    @FileName, 
                    @FileUniqueName,
                    @LayerType
                )
            ";

        public const string CheckIfTheLayerExists =
            @"SELECT COUNT(*)
                  FROM [layers]
                  WHERE [id] = @Id
            ";

        public const string DeleteLayersStructure =
            @"
                DELETE [layers] 
                WHERE [Id] NOT IN  @Ids
                AND [structure-id] = @StructureId
            ";

        public const string InsertEnvironmentalDamagePotential =
            @"
                INSERT INTO [environmental-damage-potentials]
                    (
                        [id],
                        [structure-id],
                        [total-reservoir-volume],
                        [population-downstream],
                        [environmental-impact],
                        [socioeconomic-impact],
                        [environmental-damage-potential-total]
                    )
                    VALUES
                    (
                        @Id,
                        @StructureId,
                        @TotalReservoirVolume,
                        @PopulationDownstream,
                        @EnvironmentalImpact,
                        @SocioeconomicImpact,
                        @EnvironmentalDamagePotentialTotal
                    )
            ";

        public const string UpsertEnvironmentalDamagePotential =
            @"
            UPDATE  [environmental-damage-potentials]
            SET               
                [total-reservoir-volume] = @TotalReservoirVolume,
                [population-downstream] = @PopulationDownstream,
                [environmental-impact] = @EnvironmentalImpact,
                [socioeconomic-impact] = @SocioeconomicImpact,
                [environmental-damage-potential-total] = @EnvironmentalDamagePotentialTotal
            WHERE [id] = @Id
            IF @@ROWCOUNT = 0
                INSERT INTO [environmental-damage-potentials]
                    (
                        [id],
                        [structure-id],
                        [total-reservoir-volume],
                        [population-downstream],
                        [environmental-impact],
                        [socioeconomic-impact],
                        [environmental-damage-potential-total]
                    )
                    VALUES
                    (
                        @Id,
                        @StructureId,
                        @TotalReservoirVolume,
                        @PopulationDownstream,
                        @EnvironmentalImpact,
                        @SocioeconomicImpact,
                        @EnvironmentalDamagePotentialTotal
                    )";

        public const string DeleteEnvironmentalDamagePotential =
            @"
                DELETE  [environmental-damage-potentials]
                WHERE [structure-id] = @StructureId
            ";

        public const string GetPercolationMapConfiguration =
            @"
                SELECT [positive-absolute-variation-color] AS [PositiveAbsoluteVariationColor],
                [negative-absolute-variation-color] AS [NegativeAbsoluteVariationColor],
                [constatant-absolute-variation-color] AS [ConstantAbsoluteVariationColor]
                FROM [percolation-maps-configurations]
                WHERE [structure-id] = @StructureId
            ";

        public const string GetPercolationMapData =
            @"SELECT 
            [structures].id AS [Id], 
            [structures].name AS [Name],
            [structures].datum AS [Datum], 
            [structures].[coordinate-format] AS [Format], 
            [structures].latitude AS [Latitude], 
            [structures].longitude AS [Longitude], 
            [structures].northing AS [Northing], 
            [structures].easting AS [Easting], 
            [map-general-zoom] AS [GeneralZoom], 
            [map-instruments-zoom] AS [InstrumentsZoom],
            [seismic-coefficient-horizontal] AS [Horizontal], 
            [seismic-coefficient-vertical] AS [Vertical],
            [crest-length] AS [Length], 
            [crest-width] AS [Width],
            [aspects-structures].[id] AS [Id],
            [aspects-structures].[index] AS [Index],
            [aspects].[id] AS [Id],
            [aspects].[description] AS [Description],
            [areas].[id] AS [Id],
            [areas].[name] AS [Name],
            [sections].[id] AS [Id],
            [sections].[name] AS [Name],
            [sections].[datum] AS [Datum],
            [sections].[upstream-latitude] AS [Latitude], 
            [sections].[upstream-longitude] AS [Longitude],
            [sections].[upstream-northing] AS [Northing], 
            [sections].[upstream-easting] AS [Easting], 
            [sections].[upstream-zone-number] AS [ZoneNumber], 
            [sections].[upstream-zone-letter] AS [ZoneLetter],
            [sections].[upstream-coordinate-format] AS [Format],
            [sections].[downstream-latitude] AS [Latitude], 
            [sections].[downstream-longitude] AS [Longitude],
            [sections].[downstream-northing] AS [Northing], 
            [sections].[downstream-easting] AS [Easting], 
            [sections].[downstream-zone-number] AS [ZoneNumber], 
            [sections].[downstream-zone-letter] AS [ZoneLetter],
            [sections].[downstream-coordinate-format] AS [Format],
            [sections].[midpoint-latitude] AS [Latitude],
            [sections].[midpoint-longitude] AS [Longitude],
            [sections].[midpoint-northing] AS [Northing], 
            [sections].[midpoint-easting] AS [Easting],
            [sections].[midpoint-zone-number] AS [ZoneNumber],
            [sections].[midpoint-zone-letter] AS [ZoneLetter],
            [sections].[midpoint-coordinate-format] AS [Format],
            [sections].[line-color] AS [Color], 
            [sections].[line-type] AS [Type], 
            [sections].[line-width] AS [Width], 
            [absolute-variation-color-configurations].[id] AS [Id],
            [absolute-variation-color-configurations].[positive-absolute-variation-color] AS [PositiveAbsoluteVariationColor],
            [absolute-variation-color-configurations].[negative-absolute-variation-color] AS [NegativeAbsoluteVariationColor],
            [absolute-variation-color-configurations].[constant-absolute-variation-color] AS [ConstantAbsoluteVariationColor],
            [layers].[id] AS [Id],
            [layers].[type] AS [Type],
            [layers].[file-name] AS [Name],
            [layers].[file-unique-name] AS [UniqueName]
            FROM [structures]
            LEFT JOIN [aspects-structures] 
                ON [aspects-structures].[structure-id] = [structures].[id]
            LEFT JOIN [aspects]
                ON [aspects].[id] = [aspects-structures].[aspect-id]
            LEFT JOIN [areas]
                ON [areas].[id] = [aspects].[area-id]
            LEFT JOIN [sections]
                ON [sections].[structure-id] = [structures].[id]
            LEFT JOIN [layers]
                ON [layers].[structure-id] = [structures].[id]            
            LEFT JOIN [absolute-variation-color-configurations]
                ON [absolute-variation-color-configurations].[structure-id] = [structures].[id]
            WHERE [structures].[Id] = @Id";

        public const string UpsertAbsoluteVariationColorConfiguration =
            @"
            UPDATE  [absolute-variation-color-configurations]
            SET               
                [positive-absolute-variation-color] = @PositiveAbsoluteVariationColor,
                [negative-absolute-variation-color] = @NegativeAbsoluteVariationColor,
                [constant-absolute-variation-color] = @ConstantAbsoluteVariationColor
            WHERE [id] = @Id
            IF @@ROWCOUNT = 0
                INSERT INTO [absolute-variation-color-configurations]
                    (
                        [id],
                        [structure-id],
                        [positive-absolute-variation-color],
                        [negative-absolute-variation-color],
                        [constant-absolute-variation-color]
                    )
                    VALUES
                    (
                        @Id,
                        @StructureId,
                        @PositiveAbsoluteVariationColor,
                        @NegativeAbsoluteVariationColor,
                        @ConstantAbsoluteVariationColor
                    )";

        public const string DeleteAbsoluteVariationColorConfiguration =
            @"
                DELETE [absolute-variation-color-configurations] 
                WHERE [Id] <> @Id
                AND [structure-id] = @StructureId
            ";

        public const string UpsertDisplacementMapConfiguration =
            @"
            UPDATE  [displacement-map-configurations]
            SET               
                [positive-settlement-color] = @PositiveSettlementColor,
                [negative-settlement-color] = @NegativeSettlementColor,
                [settlement-color-opacity] = @SettlementColorOpacity,
                [arrow-color] = @ArrowColor,
                [arrow-width] = @ArrowWidth
            WHERE [id] = @Id
            IF @@ROWCOUNT = 0
                INSERT INTO [displacement-map-configurations]
                    (
                        [id],
                        [structure-id],
                        [positive-settlement-color],
                        [negative-settlement-color],
                        [settlement-color-opacity],
                        [arrow-color],
                        [arrow-width]
                    )
                    VALUES
                    (
                        @Id,
                        @StructureId,
                        @PositiveSettlementColor,
                        @NegativeSettlementColor,
                        @SettlementColorOpacity,
                        @ArrowColor,
                        @ArrowWidth
                    )";

        public const string DeleteDisplacementMapConfiguration =
            @"
                DELETE [displacement-map-configurations] 
                WHERE [Id] <> @Id
                AND [structure-id] = @StructureId
            ";

        public const string GetDisplacementMapData =
            @"SELECT 
            [structures].id AS [Id], 
            [structures].name AS [Name],

            [structures].datum AS [Datum], 
            [structures].[coordinate-format] AS [Format],

            [structures].latitude AS [Latitude], 
            [structures].longitude AS [Longitude],

            [structures].northing AS [Northing], 
            [structures].easting AS [Easting],

            [map-general-zoom] AS [GeneralZoom], 
            [map-instruments-zoom] AS [InstrumentsZoom],

            [displacement-map-configurations].[id] AS [Id],
            [displacement-map-configurations].[positive-settlement-color] AS [PositiveSettlementColor],
            [displacement-map-configurations].[negative-settlement-color] AS [NegativeSettlementColor],
            [displacement-map-configurations].[settlement-color-opacity] AS [SettlementColorOpacity],
            [displacement-map-configurations].[arrow-color] AS [ArrowColor],
            [displacement-map-configurations].[arrow-width] AS [ArrowWidth]

            FROM [structures]                       
            LEFT JOIN [displacement-map-configurations]
                ON [displacement-map-configurations].[structure-id] = [structures].[id]
            WHERE [structures].[Id] = @Id";

        public const string GetStabilityMapData =
            @"SELECT 
            [structures].id AS [Id], 
            [structures].name AS [Name],

            [structures].datum AS [Datum], 
            [structures].[coordinate-format] AS [Format], 

            [structures].latitude AS [Latitude], 
            [structures].longitude AS [Longitude], 

            [structures].northing AS [Northing], 
            [structures].easting AS [Easting], 

            [map-general-zoom] AS [GeneralZoom], 
            [map-instruments-zoom] AS [InstrumentsZoom],        

            [sections].[id] AS [Id],
            [sections].[name] AS [Name],

            [sections].[datum] AS [Datum],

            [sections].[upstream-latitude] AS [Latitude], 
            [sections].[upstream-longitude] AS [Longitude],

            [sections].[upstream-northing] AS [Northing], 
            [sections].[upstream-easting] AS [Easting], 
            [sections].[upstream-zone-number] AS [ZoneNumber], 
            [sections].[upstream-zone-letter] AS [ZoneLetter],

            [sections].[upstream-coordinate-format] AS [Format],

            [sections].[downstream-latitude] AS [Latitude], 
            [sections].[downstream-longitude] AS [Longitude],

            [sections].[downstream-northing] AS [Northing], 
            [sections].[downstream-easting] AS [Easting], 
            [sections].[downstream-zone-number] AS [ZoneNumber], 
            [sections].[downstream-zone-letter] AS [ZoneLetter],

            [sections].[downstream-coordinate-format] AS [Format],

            [sections].[midpoint-latitude] AS [Latitude],
            [sections].[midpoint-longitude] AS [Longitude],

            [sections].[midpoint-northing] AS [Northing], 
            [sections].[midpoint-easting] AS [Easting],
            [sections].[midpoint-zone-number] AS [ZoneNumber],
            [sections].[midpoint-zone-letter] AS [ZoneLetter],

            [sections].[midpoint-coordinate-format] AS [Format],

            [sections].[line-color] AS [Color], 
            [sections].[line-type] AS [Type], 
            [sections].[line-width] AS [Width],

            [stability-map-configurations].[id] AS [Id],
            [stability-map-configurations].[piezometer-color] AS [PiezometerColor],
            [stability-map-configurations].[water-level-indicator-color] AS [WaterLevelIndicatorColor]
            
            FROM [structures]         
            LEFT JOIN [sections]
                ON [sections].[structure-id] = [structures].[id]
            LEFT JOIN [stability-map-configurations]
                ON [stability-map-configurations].[structure-id] = [structures].[id]
            WHERE [structures].[Id] = @Id";

        public const string UpsertStabilityMapConfiguration =
            @"
            UPDATE  [stability-map-configurations]
            SET               
                [piezometer-color] = @PiezometerColor,
                [water-level-indicator-color] = @WaterLevelIndicatorColor
            WHERE [id] = @Id
            IF @@ROWCOUNT = 0
                INSERT INTO [stability-map-configurations]
                    (
                        [id],
                        [structure-id],
                        [piezometer-color],
                        [water-level-indicator-color]
                    )
                    VALUES
                    (
                        @Id,
                        @StructureId,
                        @PiezometerColor,
                        @WaterLevelIndicatorColor
                    )";

        public const string DeleteStabilityMapConfiguration =
            @"
                DELETE [stability-map-configurations] 
                WHERE [Id] <> @Id
                AND [structure-id] = @StructureId
            ";

        public const string GetCalculationMethodsByStructureId =
            @"
                SELECT
                    [safety-factors].[calculation-method]
                FROM [safety-factors] (NOLOCK)
                JOIN [stability-analysis] (NOLOCK) ON [stability-analysis].[id] = [safety-factors].[stability-analysis-id]
                WHERE
                    [stability-analysis].[structure-id] = @StructureId
                GROUP BY 
                    [safety-factors].[calculation-method]
            ";

        public const string GetStabilityAnalysisDate =
            @"
                SELECT 
	                MIN([reading-created-date]) AS FirstAnalysis,
	                MAX([reading-created-date]) AS LastAnalysis
	                FROM [stability-analysis] (NOLOCK)
                JOIN [safety-factors] (NOLOCK) ON [safety-factors].[stability-analysis-id] = [stability-analysis].[id]
                /**where**/
            ";

        public const string GetSurfaceTypesByStructureId =
            @"
                SELECT
                    DISTINCT(
                        (CASE 
                            WHEN [safety-factors].[sli-file-type] IN (1,2,3) THEN 1
                            ELSE 2 
                        END)
                    ) AS SurfaceType
                FROM [safety-factors] (NOLOCK)
                    JOIN [stability-analysis] (NOLOCK) ON [stability-analysis].[id] = [safety-factors].[stability-analysis-id]
                WHERE
                    [stability-analysis].[structure-id] = @StructureId
            ";

        public const string GetLastAspectIndexByStructure =
            @"
                SELECT TOP (1) [index] 
                FROM [aspects-structures]
                WHERE [structure-id] = @StructureId
                ORDER BY [index] DESC;
            ";

        public const string InsertHistory =
            @"
                INSERT INTO [structure-history]
                (
                    [id],
                    [structure-id],
                    [modified-by-user-id],
                    [changes],
                    [created-date]
                )
                VALUES
                (
                    @Id,
                    @StructureId,
                    @ModifiedByUserId,
                    @Changes,
                    @CreatedDate
                )
            ";

        public const string SearchHistory =
            @"
                SELECT 
                    [structure-history].[id] AS [Id],
                    [structure-history].[changes] AS [Changes],
                    [structure-history].[created-date] AS [CreatedDate],
                    [users].Id AS [Id],
                    [users].[username] AS [Username],
                    [users].[first-name] AS [Firstname],
                    [users].[surname] AS [Surname]
                FROM [structure-history] 
                    JOIN [users] ON [users].[id] = [structure-history].[modified-by-user-id]
                WHERE [structure-history].[structure-id] = @StructureId
                ORDER BY [structure-history].[created-date] DESC
                OFFSET @Skip ROWS
                FETCH NEXT @PageSize ROWS ONLY
            ";

        public const string CountHistory = 
            @"
                SELECT 
                    COUNT([structure-history].[id])
                FROM [structure-history] 
                    JOIN [users] ON [users].[id] = [structure-history].[modified-by-user-id]
                WHERE [structure-history].[structure-id] = @StructureId
            ";
    }
}