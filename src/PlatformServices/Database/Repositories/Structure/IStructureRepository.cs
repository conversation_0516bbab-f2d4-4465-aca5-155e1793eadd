using Database.Core;
using Domain.Entities;
using Domain.Enums;
using Model.Dashboard.GetStructureMap.Request;
using Model.Dashboard.GetStructureMap.Response;
using Model.Structure.GetByFiltersMaps.Request;
using Model.Structure.GetSimulationData.Request;
using Model.Structure.GetSimulationData.Response;
using Model.Structure.GetStabilityAnalysisDate.Request;
using Model.Structure.GetStabilityAnalysisDate.Response;
using Model.Structure.List.Request;
using Model.Structure.List.Response;
using Model.Structure.Patch.Request;
using Model.Structure.Search.Request;
using Model.Structure.Search.Response;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Model.Structure.SearchHistory.Request;
using Model.Structure.SearchHistory.Response;

namespace Database.Repositories.Structure
{
    public interface IStructureRepository : IRepository<Domain.Entities.Structure>
    {
        Task<Domain.Entities.Structure> GetBasicAsync(Guid id);
        Task<IEnumerable<ListStructureResponse>> ListAsync(ListStructureRequest request);
        Task<IEnumerable<SearchStructureResponse>> SearchAsync(SearchStructureRequest request);
        Task<int> CountAsync(SearchStructureRequest request);
        Task UpdateActiveAsync(Domain.Entities.Structure request);
        Task<Domain.Entities.Structure> GetBySearchIdentifierAsync(int searchIdentifier);
        Task<IEnumerable<Domain.Entities.Structure>> GetBySearchIdentifiersAsync(IEnumerable<int> searchIdentifiers);
        Task<IEnumerable<Domain.Entities.Structure>> GetByFiltersMapsAsync(GetStructureByFiltersMapsRequest request);
        Task<Domain.ValueObjects.MapConfiguration> GetStructureMapConfiguration(Guid id);
        Task<Domain.Entities.Structure> GetPercolationMapData(Guid id);
        Task UpsertAbsoluteVariationColorsAsync(AbsoluteVariationColors absoluteVariationColors, Guid structureId);
        Task<Domain.Entities.Structure> GetDisplacementMapData(Guid id);
        Task UpsertDisplacementMapConfigurationAsync(DisplacementMapConfiguration displacementMapConfiguration, Guid structureId);
        Task<Domain.Entities.Structure> GetStructureStabilityMapData(Guid id);
        Task UpsertStabilityMapConfigurationAsync(StabilityMapConfiguration stabilityMapConfiguration, Guid structureId);
        Task<IEnumerable<CalculationMethod>> GetCalculationMethodsAsync(Guid structureId);
        Task<IEnumerable<SurfaceType>> GetSurfaceTypesAsync(Guid structureId);
        Task<Domain.Entities.Structure> GetStructureStabilityAnalysisData(Guid id);
        Task<GetStabilityAnalysisDateResponse> GetStabilityAnalysisDateAsync(GetStabilityAnalysisDateRequest request);
        Task<GetStructureSimulationDataResponse> GetSimulationDataAsync(GetStructureSimulationDataRequest request);
        Task BindAspectAsync(Guid structureId, AspectStructure aspect);
        Task<int> GetLastAspectIndex(Guid requestStructureId);
        Task<GetDashboardStructureMapResponse> GetDashboardMapAsync(GetDashboardStructureMapRequest request);
        Task<IEnumerable<SearchStructureHistoryResponse>> SearchHistoryAsync(SearchStructureHistoryRequest request);
        Task<int> CountHistoryAsync(SearchStructureHistoryRequest request);
    }
}
