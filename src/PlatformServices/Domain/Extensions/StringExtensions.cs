using System;
using System.Globalization;
using System.Linq;
using static Domain.Constants.DateTimeFormats;

namespace Domain.Extensions;

public static class StringExtensions
{
    private static readonly CultureInfo Culture = CultureInfo.InvariantCulture;

    private static readonly TimeZoneInfo BrazilianTime =
        TimeZoneInfo.FindSystemTimeZoneById("America/Sao_Paulo");

    /// <summary>
    /// Validates if a date string is in accepted format with optional time component.
    /// </summary>
    /// <param name="input">The date string to validate.</param>
    /// <returns><c>true</c> if the date is in valid pt-BR format; otherwise, <c>false</c>.</returns>
    public static bool IsValidInputDate(this string input)
    {
        if (string.IsNullOrWhiteSpace(input))
        {
            return false;
        }

        return DateTime.TryParseExact(
            input,
            AcceptedFormats,
            Culture,
            DateTimeStyles.None,
            out _);
    }

    /// <summary>
    /// Converts the specified date string to a <see cref="DateTime"/> object using accepted formats in UTC.
    /// </summary>
    /// <param name="input">The date string to convert.</param>
    /// <returns>
    /// A <see cref="DateTime"/> representation of the input string in UTC.
    /// </returns>
    /// <exception cref="ArgumentNullException">
    /// Thrown when <paramref name="input"/> is <c>null</c>.
    /// </exception>
    public static DateTime ToUtcDateTime(this string input)
    {
        ArgumentNullException.ThrowIfNull(input);

        var rawDate = DateTime.ParseExact(
            input,
            AcceptedFormats,
            Culture,
            DateTimeStyles.None);

        var result = new DateTimeOffset(rawDate, BrazilianTime.BaseUtcOffset)
            .UtcDateTime;

        return result;
    }
    
    /// <summary>
    /// Converts a string representation of a boolean value to its boolean equivalent.
    /// </summary>
    /// <param name="value">The string to convert.</param>
    /// <returns>True if the string represents a true value; otherwise, false.</returns>
    public static bool ToBool(this string value)
    {
        if (string.IsNullOrEmpty(value)) return false;

        return value.ToLower() switch
        {
            "true" or "t" or "yes" or "1" or "sim" or "s"
                or "verdadeiro" => true,
            "false" or "f" or "no" or "0" or "não" or "nao" or "n"
                or "falso" => false,
            _ => false
        };
    }
    
    /// <summary>
    /// Determines whether the specified string contains only numeric characters (0-9).
    /// </summary>
    /// <param name="that">The string to evaluate.</param>
    /// <returns>True if the string contains only digits; otherwise, false.</returns>
    public static bool HasOnlyNumbers(this string that)
    {
        return !string.IsNullOrEmpty(that) && that.All(char.IsDigit);
    }
}
