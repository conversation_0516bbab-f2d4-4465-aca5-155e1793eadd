using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using Coordinate.Core.Classes;
using Domain.Core;
using Domain.Enums;
using Domain.Extensions;
using Domain.ValueObjects;

namespace Domain.Entities;

public class Structure : Entity, ICloneable
{
    public Guid ClientUnitId { get; set; }
    public Client Client { get; set; }
    public ClientUnit ClientUnit { get; set; }
    
    [Description("Nome")]
    public string Name { get; set; }
    
    [Description("Status")]
    public StructureStatus Status { get; set; }
    
    [Description("Protocolo de Transferência")]
    public Protocol? Protocol { get; set; }
    
    [Description("Tipo de Estrutura")]
    public StructureType StructureType { get; set; }
    
    [Description("Coordenadas")]
    public CoordinateSetting CoordinateSetting { get; set; }
    
    [Description("Configurações do Slide2")]
    public Slide2Configuration Slide2Configuration { get; set; }
    
    [Description("Avaliar Condição Drenada")]
    public bool ShouldEvaluateDrainedCondition { get; set; }
    
    [Description("Avaliar Condição Não-drenada")]
    public bool ShouldEvaluateUndrainedCondition { get; set; }
    
    [Description("Avaliar Condição Pseudo-estática")]
    public bool ShouldEvaluatePseudoStaticCondition { get; set; }
    
    [Description("Configuração do Mapa")]
    public MapConfiguration MapConfiguration { get; set; }
    
    [Description("Atualização Automática")]
    public bool HasAutoUpdate { get; set; }
    
    [Description("Frequência de Busca de Leituras")]
    public AutomationFrequency FrequencyToFetchData { get; set; }
    
    [Description("Frequência de Geração de Pacotes")]
    public AutomationFrequency FrequencyToGeneratePackages { get; set; }
    
    [Description("Coeficiente Sísmico")]
    public Orientation SeismicCoefficient { get; set; }
    
    [Description("Gravidade")]
    public double Gravity { get; set; }
    
    [Description("Cidade")]
    public City City { get; set; }
    
    [Description("Estado")]
    public State State { get; set; }
    
    [Description("País")]
    public Country Country { get; set; }
    
    [Description("Propósito")]
    public string Purpose { get; set; }
    
    [Description("Etapas de Obra")]
    public int? ConstructionStages { get; set; }
    
    [Description("Dimensão da Crista")]
    public Dimension CrestDimension { get; set; }
    
    [Description("Altura Total")]
    public double? TotalHeight { get; set; }
    
    [Description("Talude a Jusante")]
    public string DownstreamSlope { get; set; }
    
    [Description("Talude a Montante")]
    public string UpstreamSlope { get; set; }
    
    [Description("Classificação")]
    public Classification? Classification { get; set; }
    
    [Description("Tipo de Seção")]
    public string SectionType { get; set; }
    
    [Description("Tipo de Fundação")]
    public string FoundationType { get; set; }
    
    [Description("Método de Alteamento")]
    public string RaisingMethod { get; set; }
    
    [Description("Alteamentos esperados")]
    public int? ExpectedElevations { get; set; }
    
    [Description("Alteamentos realizados")]
    public int? ElevationsMade { get; set; }
    
    [Description("Volume de Projeto do Reservatório")]
    public double? ReservoirDesignVolume { get; set; }
    
    [Description("Volume Atual do Reservatório")]
    public double? CurrentReservoirVolume { get; set; }
    
    [Description("Drenagem Interna")]
    public string InternalDrainage { get; set; }
    
    [Description("Drenagem Superficial")]
    public string SuperficialDrainage { get; set; }
    
    [Description("Area da Bacia")]
    public double? BasinAreaInSquareKilometers { get; set; }
    
    [Description("Precipitação do Projeto")]
    public double? ProjectPrecipitation { get; set; }
    
    [Description("Cheia de Projeto")]
    public int? FullOfProject { get; set; }
    
    [Description("Vazão Máxima Afluente")]
    public double? MaximumInfluentFlow { get; set; }
    
    [Description("Vazão de Projeto")]
    public double? ProjectFlow { get; set; }
    
    [Description("NA Máximo Normal")]
    public double? NormalMaximumWaterLevel { get; set; }
    
    [Description("NA Máximo Maximorum")]
    public double? MaximumWaterLevelMaximorum { get; set; }
    
    [Description("Borda Livre (NA Máximo Normal)")]
    public double? FreeboardNormalMaximumWaterLevel { get; set; }
    
    [Description("Borda Livre (NA Máximo Max)")]
    public double? FreeboardMaximumWaterLevelMaximorum { get; set; }
    
    [Description("Vertedouro")]
    public string Spillway { get; set; }
    
    [Description("Responsáveis")]
    public List<Responsible> Responsibles { get; set; } = new();
    
    [Description("Aspectos a Observar nas Inspeções")]
    public List<AspectStructure> Aspects { get; set; } = new();
    
    [Description("Ativo")]
    public bool Active { get; set; }
    
    public List<Section> Sections { get; } = new();
    
    [Description("Naturezas")]
    public List<Nature> Natures { get; set; } = new();

    [Description("Ano de Construção")]
    public int?
        ConstructionYear { get; set; } // Renomear para construction_year

    [Description("Início da Operação")]
    public int? StartOfOperationDate { get; set; }
    
    [Description("Fim da Operação")]
    public int? EndOfOperationDate { get; set; }
    
    [Description("Empresa Projetista")]
    public string DesigningCompany { get; set; }
    
    [Description("Situação Atual da Barragem")]
    public string CurrentStatus { get; set; }
    
    [Description("Cota da Crista")]
    public decimal? CrestQuota { get; set; }
    
    [Description("Cota da Soleira do Vertedouro")]
    public decimal? SpillwaySillQuota { get; set; }
    
    [Description("Curso d'água interceptado")]
    public string InterceptedWatercourse { get; set; }

    [Description("Potencial de Dano Ambiental")]
    public EnvironmentalDamagePotential EnvironmentalDamagePotential
    {
        get;
        set;
    }

    public List<Layer> Layers { get; set; } = new();
    
    [Description("Cores de Variação Absoluta")]
    public AbsoluteVariationColors AbsoluteVariationColors { get; set; }

    [Description("COnfiguração do Mapa de Deslocamento")]
    public DisplacementMapConfiguration DisplacementMapConfiguration
    {
        get;
        set;
    }

    [Description("Configuração do Mapa de Estabilidade")]
    public StabilityMapConfiguration StabilityMapConfiguration { get; set; }
    
    public List<StructureHistory> History { get; set; }

    public void AddSection(Section section)
    {
        if (!Sections.Any(s => s.Id == section.Id))
        {
            Sections.Add(section);
        }
    }

    public void AddResponsible(Responsible responsible)
    {
        if (!Responsibles.Any(c => c.Id == responsible.Id))
        {
            Responsibles.Add(responsible);
        }
    }

    public void UpdateAspects(List<AspectStructure> aspects)
    {
        Aspects.RemoveAll(original => !aspects.Any(modified => modified.Aspect.Id == original.Aspect.Id));

        foreach (var value in aspects)
        {
            var item = Aspects.FirstOrDefault(aspect => aspect.Aspect.Id == value.Aspect.Id);

            if (item != null)
            {
                continue;
            }

            Aspects.Add(value);
        }
    }

    public void UpdateResponsibles(List<Responsible> responsibles)
    {
        Responsibles.Update(responsibles);
    }

    public void AddAspect(AspectStructure aspect)
    {
        if (!Aspects.Any(c => c.Id == aspect.Id))
        {
            Aspects.Add(aspect);
        }
    }

    public void AddCircularCalculationMethod(CalculationMethod method)
    {
        if (!Slide2Configuration.CircularParameters.CalculationMethods.Any(c =>
                c == method))
        {
            Slide2Configuration.CircularParameters.CalculationMethods.Add(
                method);
        }
    }

    public void AddNonCircularCalculationMethod(CalculationMethod method)
    {
        if (!Slide2Configuration.NonCircularParameters.CalculationMethods
                .Any(c => c == method))
        {
            Slide2Configuration.NonCircularParameters.CalculationMethods.Add(
                method);
        }
    }

    public void AddLayer(Layer layer)
    {
        if (!Layers.Exists(c => c.Id == layer.Id))
        {
            Layers.Add(layer);
        }
    }

    public void UpdateLayers(List<Layer> layers)
    {
        Layers.Update(layers);
    }

    public bool ShouldEvaluateAnyCondition() =>
        ShouldEvaluateDrainedCondition
        || ShouldEvaluateUndrainedCondition
        || ShouldEvaluatePseudoStaticCondition;

    public void AddHistory(
        Guid userId,
        string changeText)
    {
        if (userId == Guid.Empty)
        {
            throw new ArgumentException(
                $"{nameof(userId)} can't be empty",
                nameof(userId));
        }

        if (string.IsNullOrEmpty(changeText))
        {
            return;
        }

        History ??= new List<StructureHistory>();

        History.Add(new StructureHistory
        {
            Structure = this,
            ModifiedBy = new User
            {
                Id = userId
            },
            Changes = changeText.Length > 8000
                ? changeText[..8000]
                : changeText
        });
    }

    public object Clone()
    {
        var clone = (Structure)MemberwiseClone();

        clone.Responsibles = new List<Responsible>();
        foreach (var responsible in Responsibles)
        {
            clone.Responsibles.Add((Responsible)responsible?.Clone());
        }
        
        clone.Aspects = new List<AspectStructure>();
        foreach (var aspect in Aspects)
        {
            clone.Aspects.Add((AspectStructure)aspect?.Clone());
        }
        
        clone.Natures = new List<Nature>();
        foreach (var nature in Natures)
        {
            clone.Natures.Add((Nature)nature?.Clone());
        }
        
        clone.Layers = new List<Layer>();
        foreach (var layer in Layers)
        {
            clone.Layers.Add((Layer)layer?.Clone());
        }
        
        return clone;
    }
}