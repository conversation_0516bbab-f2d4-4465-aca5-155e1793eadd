using Domain.Core;
using Domain.ValueObjects;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using Domain.Extensions;

namespace Domain.Entities;

public sealed class SectionReview : Entity, ICloneable
{
    [Description("Data de Início")]
    public DateTime StartDate { get; set; }

    [Description("Tipo de Estrutura")]
    public StructureType StructureType { get; private set; }

    [Description("Tipo de Seção")]
    public SectionType SectionType { get; private set; }

    [Description("DXF")]
    public File Drawing { get; set; }

    [Description("SLI")]
    public File Sli { get; set; }

    public Section Section { get; set; }

    [Description("Descrição")]
    public string Description { get; set; }

    public int Index { get; set; }
    public User CreatedBy { get; set; }

    [Description("Em Construção")]
    public bool IsUnderConstruction { get; set; }

    [Description("Etapas de Obra")]
    public List<ConstructionStage> ConstructionStages { get; set; } = new();

    public Guid StructureId { get; set; }
    public bool DxfHasWaterline { get; set; }
    public SectionLength LengthData { get; set; }

    public void UpsertConstructionStage(ConstructionStage constructionStage)
    {
        ConstructionStages.Upsert(constructionStage);
    }

    public object Clone()
    {
        var review = new SectionReview
        {
            Id = Id,
            Index = Index,
            StartDate = StartDate,
            StructureType = StructureType,
            SectionType = SectionType,
            Drawing = (File)(Drawing?.Clone()),
            Sli = (File)(Sli?.Clone()),
            Description = Description,
            IsUnderConstruction = IsUnderConstruction,
        };

        if (ConstructionStages != null)
        {
            review.ConstructionStages = new List<ConstructionStage>();
            foreach (var stage in ConstructionStages)
            {
                review.ConstructionStages.Add(
                    (ConstructionStage)stage.Clone());
            }
        }

        return review;
    }

    public bool HasValidFiles() =>
        Drawing != null
        && !string.IsNullOrEmpty(Drawing.UniqueName)
        && Sli != null
        && !string.IsNullOrEmpty(Sli.UniqueName);

    public void SetStructureType(StructureType structureType)
    {
        ArgumentNullException.ThrowIfNull(structureType);

        StructureType = structureType;

        SectionType = new SectionType()
        {
            Id = structureType.Id,
            Name = structureType.Name,
            Active = structureType.Active,
            Activities = structureType.Activities
                ?
                .Select(item => new SectionTypeActivity
                {
                    Activity = item.Activity,
                    CreatedDate = item.CreatedDate,
                    Id = item.Id,
                    Index = item.Index,
                    SearchIdentifier = item.SearchIdentifier
                })
                .ToList(),
        };
    }

    public void SetSectionType(SectionType sectionType)
    {
        ArgumentNullException.ThrowIfNull(sectionType);

        SectionType = sectionType;

        StructureType = new StructureType()
        {
            Id = sectionType.Id,
            Name = sectionType.Name,
            Active = sectionType.Active,
            Activities = sectionType.Activities
                ?
                .Select(item => new StructureTypeActivity
                {
                    Activity = item.Activity,
                    CreatedDate = item.CreatedDate,
                    Id = item.Id,
                    Index = item.Index,
                    SearchIdentifier = item.SearchIdentifier
                })
                .ToList(),
        };
    }
}
