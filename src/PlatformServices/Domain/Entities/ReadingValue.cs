using Coordinate.Core.Enums;
using Domain.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace Domain.Entities
{
    public class ReadingValue : Entity, ICloneable
    {
        public Reading Reading { get; set; }
        public Measurement Measurement { get; set; }
        public bool RemovedFromLastPackage { get; set; }
        public bool ForcedCalculationInLastPackage { get; set; }
        public decimal? MeasurementQuota { get; set; }
        public decimal? MeasurementDeltaRef { get; set; }
        public decimal? MeasurementLength { get; set; }
        public bool? MeasurementIsReferential { get; set; }
        public List<SecurityLevels> SecurityLevels { get; set; } = new();

        [Description("Data e Hora")]
        public DateTime Date { get; set; }
        [Description("Cota")]
        public decimal? Quota { get; set; }
        [Description("Profundidade")]
        public decimal? Depth { get; set; }
        [Description("Pressão")]
        public decimal? Pressure { get; set; }
        [Description("Seco")]
        public bool? Dry { get; set; }
        [Description("A Positivo")]
        public decimal? PositiveA { get; set; }
        [Description("A Negativo")]
        public decimal? NegativeA { get; set; }
        [Description("B Positivo")]
        public decimal? PositiveB { get; set; }
        [Description("B Negativo")]
        public decimal? NegativeB { get; set; }
        [Description("Deslocamento médio A")]
        public decimal? AverageDisplacementA { get; set; }
        [Description("Deslocamento médio B")]
        public decimal? AverageDisplacementB { get; set; }
        [Description("Deslocamento acumulado A")]
        public decimal? AccumulatedDisplacementA { get; set; }
        [Description("Deslocamento acumulado B")]
        public decimal? AccumulatedDisplacementB { get; set; }
        [Description("Desvio A")]
        public decimal? DeviationA { get; set; }
        [Description("Devio B")]
        public decimal? DeviationB { get; set; }
        [Description("Leitura do eixo A")]
        public decimal? AAxisReading { get; set; }
        [Description("Leitura do eixo B")]
        public decimal? BAxisReading { get; set; }
        [Description("Datum")]
        public Datum? Datum { get; set; }
        [Description("Coordenada Leste")]
        public decimal? EastCoordinate { get; set; }
        [Description("Coordenada Norte")]
        public decimal? NorthCoordinate { get; set; }
        [Description("Deslocamento Leste")]
        public decimal? EastDisplacement { get; set; }
        [Description("Deslocamento Norte")]
        public decimal? NorthDisplacement { get; set; }
        [Description("Deslocamento Z")]
        public decimal? ZDisplacement { get; set; }
        [Description("Deslocamento Planimétrico Total")]
        public decimal? TotalPlanimetricDisplacement { get; set; }
        [Description("Deslocamento A")]
        public decimal? ADisplacement { get; set; }
        [Description("Deslocamento B")]
        public decimal? BDisplacement { get; set; }
        [Description("Profundidade Relativa")]
        public decimal? RelativeDepth { get; set; }
        [Description("Delta Ref.")]
        public decimal? DeltaRef { get; set; }
        [Description("Recalque Absoluto")]
        public decimal? AbsoluteSettlement { get; set; }
        [Description("Recalque Relativo")]
        public decimal? RelativeSettlement { get; set; }
        [Description("Natureza")]
        public Nature Nature { get; set; }
        [Description("Eixo A PGA")]
        public decimal? AAxisPga { get; set; }
        [Description("Eixo B PGA")]
        public decimal? BAxisPga { get; set; }
        [Description("Eixo Z PGA")]
        public decimal? ZAxisPga { get; set; }
        [Description("Pluviometria")]
        public decimal? Pluviometry { get; set; }
        [Description("Intensidade")]
        public decimal? Intensity { get; set; }

        private const decimal Gravity = 9.81M;
        private const int MetersToMillimetersMultiplier = 1000;
        public const int UtmCoordinatesDecimalPlaces = 8;
        public const int DisplacementDecimalPlaces = 6;
        public const int QuotaDecimalPlaces = 10;

        
        public object Clone()
        {
            var clone = (ReadingValue)MemberwiseClone();

            return clone;
        }

        public static decimal CalculateUtmCoordinateDisplacement(
            bool isReferential,
            decimal readingCoordinate,
            decimal? referentialCoordinate)
        {
            if (isReferential || !referentialCoordinate.HasValue)
            {
                return 0;
            }

            var displacementInMeters = 
                Math.Round(readingCoordinate, UtmCoordinatesDecimalPlaces) -
                Math.Round(referentialCoordinate.Value, UtmCoordinatesDecimalPlaces);

            return displacementInMeters * MetersToMillimetersMultiplier;
        }
        
        public static decimal CalculateTotalPlanimetricDisplacement(
            decimal northDisplacement, 
            decimal eastDisplacement)
        {
            const double internalPower = 2;
            const double externalPower = 0.5;
            
            var sum = Math.Pow((double)northDisplacement, internalPower) + 
                      Math.Pow((double)eastDisplacement, internalPower);

            return (decimal)Math.Round(
                Math.Pow(sum, externalPower), 
                DisplacementDecimalPlaces);
        }
        
        public static decimal CalculateZDisplacement(
            bool isReferential,
            decimal readingQuota,
            decimal? referentialQuota)
        {
            if (isReferential || !referentialQuota.HasValue)
            {
                return 0;
            }

            var displacementInMeters = 
                Math.Round(readingQuota, QuotaDecimalPlaces) - 
                Math.Round(referentialQuota.Value, QuotaDecimalPlaces);

            return displacementInMeters * MetersToMillimetersMultiplier;
        }
        
        public static decimal? CalculateADisplacement(
            decimal northDisplacement,
            decimal eastDisplacement,
            double? instrumentAzimuth)
        {
            if (!instrumentAzimuth.HasValue)
            {
                return null;
            }
            
            var azimuthAsRadians = instrumentAzimuth.Value * Math.PI / 180.0;
            var azimuthSine = Math.Sin(azimuthAsRadians);
            var azimuthCosine = Math.Cos(azimuthAsRadians);
            var sum = (double)northDisplacement * azimuthCosine +
                      (double)eastDisplacement * azimuthSine;

            return (decimal)
                Math.Round(sum, DisplacementDecimalPlaces);
        }
        
        public static decimal? CalculateBDisplacement(
            decimal northDisplacement,
            decimal eastDisplacement,
            double? instrumentAzimuth)
        {
            if (!instrumentAzimuth.HasValue)
            {
                return null;
            }
            
            var azimuthAsRadians = instrumentAzimuth.Value * Math.PI / 180.0;
            var azimuthSine = Math.Sin(azimuthAsRadians);
            var azimuthCosine = Math.Cos(azimuthAsRadians);
            var sum = (double)northDisplacement * azimuthSine +
                      (double)eastDisplacement * azimuthCosine;

            return (decimal)
                Math.Round(sum, DisplacementDecimalPlaces);
        }
        
        public static (decimal depth, decimal pressure) CalculateDepthAndPressure(
            decimal quota,
            decimal topQuota,
            decimal baseQuota)
        {
            var depth = topQuota - quota;

            var pressure = (quota - baseQuota) * Gravity;

            return (depth, pressure);
        }

        public static (decimal quota, decimal pressure) CalculateQuotaAndPressure(
            decimal depth,
            decimal topQuota,
            decimal baseQuota)
        {
            var quota = topQuota - depth;

            var pressure = (quota - baseQuota) * Gravity;

            return (quota, pressure);
        }

        public static (decimal quota, decimal depth) CalculateQuotaAndDepth(
            decimal pressure,
            decimal topQuota,
            decimal baseQuota)
        {
            var quota = baseQuota + (pressure / Gravity);

            var depth = topQuota - quota;

            return (quota, depth);
        }
        
        public static decimal CalculateConventionalAverageDisplacement(
            decimal positive, 
            decimal negative)
        {
            const decimal divider = 2m;

            return (positive - negative) / divider;
        }
        
        public static decimal CalculateIpiAverageDisplacement(
            double lenght,
            double axisReading)
        {
            var readingAsRadians = axisReading * Math.PI / 180.0;
            
            return (decimal)(lenght * Math.Sin(readingAsRadians));
        }
        
        public static decimal? CalculateDeviation(
            decimal? accumulatedDisplacement,
            decimal? referentialAccumulatedDisplacement)
        {
            if (!accumulatedDisplacement.HasValue
                || !referentialAccumulatedDisplacement.HasValue)
            {
                return null;
            }

            return accumulatedDisplacement - referentialAccumulatedDisplacement;
        }
        
        public static decimal CalculateAccumulatedDisplacement(
            decimal averageDisplacement,
            decimal previouslyAccumulatedDisplacement)
        {
            return averageDisplacement + previouslyAccumulatedDisplacement;
        }
        
        public static decimal CalculateAbsoluteDepth(
            decimal absoluteSettlement,
            decimal measurementQuota,
            decimal topQuota)
        {
            return (topQuota * MetersToMillimetersMultiplier) - (measurementQuota * MetersToMillimetersMultiplier) - absoluteSettlement;
        }

        public static decimal CalculateRelativeDepth(
            decimal topQuota,
            decimal quota)
        {
            return (topQuota * MetersToMillimetersMultiplier) - quota;
        }

        public static decimal CalculateDeltaReference(
            bool isReferentialMagneticRing,
            decimal? referentialMagneticRingAbsoluteDepth,
            decimal absoluteDepth)
        {
            return isReferentialMagneticRing 
                ? 0 
                : (referentialMagneticRingAbsoluteDepth.Value - absoluteDepth) / MetersToMillimetersMultiplier;
        }

        public static decimal CalculateAbsoluteSettlement(
            decimal depth,
            decimal measurementQuota,
            decimal topQuota)
        {
            return (topQuota * MetersToMillimetersMultiplier) - (measurementQuota * MetersToMillimetersMultiplier) - depth;
        }

        public static decimal CalculateRelativeSettlementFromDepth(
            bool isReferentialMeasurement,
            decimal readingDeltaRef,
            decimal measurementDeltaRef)
        {
            return isReferentialMeasurement ? 0 : (readingDeltaRef - measurementDeltaRef) * MetersToMillimetersMultiplier;
        }

        public static decimal CalculateQuota(
            bool isReferentialMeasurement,
            decimal measurementQuota,
            decimal referentialMeasurementQuota,
            decimal deltaRef)
        {
            return isReferentialMeasurement
                ? measurementQuota * MetersToMillimetersMultiplier
                : (referentialMeasurementQuota * MetersToMillimetersMultiplier) + (deltaRef * MetersToMillimetersMultiplier);
        }
    }
}
